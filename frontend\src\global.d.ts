declare module '*.module.css' {
  const classes: { [key: string]: string }
  export default classes
}

declare module '*.module.scss' {
  const classes: { [key: string]: string }
  export default classes
}

declare module '*.otf'
declare module '*.ttf'

// Package should be extending React's props interface, this fixes that to resolve compiler issues with the className prop
// Forking submitting
declare module 'react-leaflet-control' {
  interface ControlProps extends React.HTMLProps<Control> {
    position: 'topleft' | 'topright' | 'bottomright' | 'bottomleft'
    children: React.ReactNode
  }

  export default class Control extends React.Component<ControlProps> {}
}
