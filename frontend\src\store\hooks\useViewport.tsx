import type { LatLngBounds, LatLngLiteral } from 'leaflet'
import { useState } from 'react'

export interface AgriGISMapViewPort {
  bounds: LatLngBounds | undefined
  center: LatLngLiteral | undefined
  zoom: number | undefined
}

export const useViewport = (): [
  AgriGISMapViewPort,
  (bounds: LatLngBounds | undefined) => void,
  (center: LatLngLiteral | undefined) => void,
  (zoom: number | undefined) => void,
  (viewport: AgriGISMapViewPort) => void,
] => {
  const [viewport, setViewport] = useState<AgriGISMapViewPort>({
    bounds: undefined,
    center: undefined,
    zoom: undefined,
  })

  const setBounds = (newBounds: LatLngBounds | undefined) => {
    if (newBounds) {
      setViewport((prev: AgriGISMapViewPort) => {
        let newState = prev
        newState = { ...newState, bounds: newBounds }
        return newState
      })
    }
  }

  const setCenter = (newCenter: LatLngLiteral | undefined) => {
    if (newCenter) {
      setViewport((prev: AgriGISMapViewPort | undefined) => {
        let newState = prev || {
          bounds: undefined,
          center: undefined,
          zoom: undefined,
        }
        newState = { ...newState, center: newCenter }
        return newState
      })
    }
  }

  const setZoom = (newZoom: number | undefined) => {
    if (newZoom) {
      setViewport((prev: AgriGISMapViewPort | undefined) => {
        let newState = prev || {
          bounds: undefined,
          center: undefined,
          zoom: undefined,
        }
        newState = { ...newState, zoom: newZoom }
        return newState
      })
    }
  }

  return [viewport, setBounds, setCenter, setZoom, setViewport]
}
