import { useMemo } from 'react'
import type { Facility } from '@store/services/sdk'
import { LOAN_GRAPH_FIELDS } from '@util/const'

const GRAPH_FIELDS = LOAN_GRAPH_FIELDS.reduce<Record<string, number>>(
  (record, cur) => {
    record[cur] = 0
    return record
  },
  {}
)

function useFundingSummary(facilities: Facility[]) {
  return useMemo(
    () =>
      Object.entries(
        facilities.reduce((totals, facility) => {
          totals[facility.accountType] += facility.limitFloat
          return totals
        }, GRAPH_FIELDS)
      ).map(([type, value]) => ({ type, value })),
    [facilities]
  )
}

export default useFundingSummary
