// export type AssetType = 'Improvement' | 'Irrigation' | 'Land' | 'Carbon' | 'Planting' | 'Risk' | 'HighestAndBestUse';
export type AssetType =
  | 'Improvement'
  | 'Land'
  | 'Carbon'
  | 'Planting'
  | 'Risk'
  | 'HighestAndBestUse'

export const ASSET_TYPES: AssetType[] = [
  'Improvement',
  'Land',
  'Carbon',
  'Planting',
  'Risk',
  'HighestAndBestUse',
]

export const ASSET_TYPES_WITH_ALL: (AssetType | 'All')[] = [
  'All',
  ...ASSET_TYPES,
]

export const ASSET_GEOMETRY_TYPES: { [key in AssetType]: string[] | null } = {
  Improvement: ['polygon'],
  Land: ['polygon', 'circle'],
  HighestAndBestUse: ['polygon'],
  Carbon: ['polygon'],
  Planting: ['polygon'],
  Risk: ['polygon', 'marker'],
}

export interface Asset {
  assetType: AssetType
  id?: number
  area?: number
}
