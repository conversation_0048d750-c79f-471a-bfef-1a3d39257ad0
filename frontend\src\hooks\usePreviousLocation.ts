import { useEffect, useMemo, useState } from 'react'
import { type Location, useLocation } from 'react-router-dom'
import { hasRedirectState } from '@util/guards'

export default function usePreviousLocation() {
  const location = useLocation()

  const [pathname, setPathname] = useState('')
  const [from, setFrom] = useState<Location | null>()

  useEffect(() => {
    if (location.pathname !== pathname) {
      const state = location.state

      if (hasRedirectState(state)) {
        setFrom(state.from)
      } else {
        setFrom(null)
      }

      setPathname(location.pathname)
    }
  }, [location, pathname])

  return useMemo(
    () => ({
      from,
      to: from ?? '/',
    }),
    [from]
  )
}
