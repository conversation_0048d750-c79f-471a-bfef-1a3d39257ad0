import { Valuation } from '@/types'
import { RVR } from '@models/rvr/RVR'
import { RVRAttachment } from '@models/rvr/RVRAttachment'
import { RVRSummaryForm } from '@models/rvr/RVRSummary'
import { baseApi } from '../baseApi'

export const tradingGroupApi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    getRVR: build.query<RVR, string>({
      query: (rvrId: string) => ({
        url: `/api/v2/rvr/${rvrId}/`,
      }),
      providesTags: (result, error, rvrId) => [
        {
          type: 'RVR',
          id: `${rvrId}`,
        },
      ],
    }),
    createRVR: build.mutation<RVR, Omit<RVR, 'id'>>({
      query: (rvr: RVR) => ({
        url: `/api/v2/rvr/`,
        method: 'POST',
        body: rvr,
      }),
    }),
    getRVRValuations: build.query<Valuation[], string>({
      query: (rvrId: string) => ({
        url: `/api/v2/rvr/${rvrId}/valuations/`,
      }),
      providesTags: (result, error, rvrId) => [
        { type: 'RVRValuations', id: rvrId },
      ],
    }),
    createRelatedInternalValuation: build.mutation<
      Valuation,
      { rvrId: number; valuationId: string }
    >({
      query: ({ rvrId, valuationId }) => ({
        url: `/api/v2/rvr/${rvrId}/valuations/${valuationId}/create_internal_valuation/`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, args) => [
        { type: 'RVRValuations', id: args.rvrId },
        { type: 'ValuationSummary', id: args.valuationId?.toString() },
      ],
    }),
    deleteRelatedInternalValuation: build.mutation<
      Valuation,
      {
        rvrId: number
        valuationId: string
        internalValuationId: string | number
      }
    >({
      query: ({ rvrId, valuationId }) => ({
        url: `/api/v2/rvr/${rvrId}/valuations/${valuationId}/delete_internal_valuation/`,
        method: 'POST',
      }),
      invalidatesTags: (
        result,
        error,
        { valuationId, internalValuationId }
      ) => [
        { type: 'ValuationSummary', id: valuationId?.toString() },
        {
          type: 'ValuationSummary',
          id: internalValuationId?.toString(),
        },
        { type: 'Assets', id: internalValuationId?.toString() },
      ],
    }),
    uploadRVRAttachment: build.mutation<
      void,
      { rvrId: string; file: File; fileDescription: string }
    >({
      query: ({ rvrId, file, fileDescription }) => {
        const blob = new File([file], file.name, { type: file.type })

        const formData = new FormData()
        formData.append('blob', blob, file.name)
        formData.append('fileDescription', fileDescription)

        return {
          method: 'POST',
          url: `/api/v2/rvr/${rvrId}/attachments/`,
          body: formData,
          headers: {
            'Content-Type': undefined,
          },
        }
      },
      invalidatesTags: (result, error, { rvrId }) => [
        { type: 'RVRAttachment', id: rvrId },
      ],
    }),
    deleteRVRAttachment: build.mutation<void, RVRAttachment>({
      query: ({ rvrId, id }) => {
        return {
          method: 'DELETE',
          url: `/api/v2/rvr/${rvrId}/attachments/${id}/`,
          headers: {
            'Content-Type': undefined,
          },
        }
      },
      invalidatesTags: (result, error, { rvrId }) => [
        { type: 'RVRAttachment', id: rvrId },
      ],
    }),
    getRVRAttachments: build.query<RVRAttachment[], string>({
      query: (rvrId) => `/api/v2/rvr/${rvrId}/attachments/`,
      providesTags: (result, error, rvrId) => [
        { type: 'RVRAttachment', id: rvrId },
      ],
    }),
    getRVRForm: build.query<RVRSummaryForm, string>({
      query: (rvrId) => `/api/v2/rvr/${rvrId}/rvr_summary/`,
      providesTags: (result, error, rvrId) => [{ type: 'RVRForm', id: rvrId }],
    }),
    submitRVRForm: build.mutation<
      void,
      { rvrId: string; formData: RVRSummaryForm }
    >({
      query: ({ rvrId, formData }) => ({
        method: 'PUT',
        url: `/api/v2/rvr/${rvrId}/rvr_summary/`,
        body: formData,
      }),
      invalidatesTags: (result, error, { rvrId }) => [
        { type: 'RVRForm', id: rvrId },
        { type: 'RVR', id: rvrId },
      ],
    }),
  }),
})

export const {
  useGetRVRQuery,
  useGetRVRValuationsQuery,
  useCreateRVRMutation,
  useCreateRelatedInternalValuationMutation,
  useDeleteRelatedInternalValuationMutation,
  useUploadRVRAttachmentMutation,
  useGetRVRAttachmentsQuery,
  useDeleteRVRAttachmentMutation,
  useGetRVRFormQuery,
  useSubmitRVRFormMutation,
} = tradingGroupApi
