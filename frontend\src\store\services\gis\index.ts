import { parseTitleProperties } from '../../../api/parse'
import { LayerType } from '../../../models/gis/LayerType'
import { GeoFeature, GeoFeatureGroup } from '../../../types'
import { MAX_ELEVATION, MIN_ELEVATION } from '../../../variables/definitions'
import { mapActions } from '../../features/map'
import { baseApi } from '../baseApi'

interface GetPhysicalLayerQueryParams {
  valuationId?: string
  saleId?: string
  layerType: LayerType
}

interface GetFilteredPhysicalLayerQueryParams {
  valuationId?: string
  saleId?: string
  layerType: LayerType
  overlayFeature: GeoFeature
}

type QueryReturnValue<T = unknown, E = unknown, M = unknown> =
  | {
      error: E
      data?: undefined
      meta?: M
    }
  | {
      error?: undefined
      data: T
      meta?: M
    }

const buildQueryParams = (params: { [key: string]: string | undefined }) => {
  return Object.entries(params)
    .map(([key, value]) =>
      value !== undefined ? `${key}=${value}` : undefined
    )
    .filter((value) => value !== undefined)
    .join('&')
}

export const gisApi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    getPhysicalLayer: build.query<
      GeoFeatureGroup<unknown>,
      GetPhysicalLayerQueryParams
    >({
      providesTags: () => [{ type: 'PhysicalProperties', id: 'LIST' }],
      queryFn: async (params, _api, _extraOptions, baseQuery) => {
        // this is a hacky way to implement custom parsing that has access to the original parameters
        const queryParams: { [key: string]: string | undefined } = {
          valuation: params.valuationId,
          sale: params.saleId,
          layer_type: params.layerType,
        }

        const response = await baseQuery({
          url: `/api/layers/?${buildQueryParams(queryParams)}`,
        })
        if (response.error) {
          throw response.error
        }
        const data = parseGeoFeatures(
          response.data as GeoFeatureGroup,
          params.layerType
        )
        return { data }
      },
      onCacheEntryAdded: async (
        params,
        { dispatch, getCacheEntry, cacheDataLoaded }
      ) => {
        if (params.layerType === 'elevation') {
          await cacheDataLoaded
          const { data } = getCacheEntry()
          if (data) {
            const limits = computeElevationLimits(data)
            dispatch(mapActions.updateElevationLayerLimits(limits))
          }
        }
      },
      keepUnusedDataFor: 60000,
    }),
    getFilteredPhysicalLayer: build.query<
      {
        physicalLayerFeature: ReturnType<typeof parseGeoFeatures>
        boundedOverlayFeature: GeoFeature
      },
      GetFilteredPhysicalLayerQueryParams
    >({
      providesTags: () => [{ type: 'PhysicalProperties', id: 'LIST' }],
      queryFn: async (params, _api, _extraOptions, baseQuery) => {
        // this is a hacky way to implement custom parsing that has access to the original parameters
        const queryParams: { [key: string]: string | undefined } = {
          valuation: params.valuationId,
          sale: params.saleId,
          layer_type: params.layerType,
        }
        const response = await baseQuery({
          url: `/api/layers/?${buildQueryParams(queryParams)}`,
          method: 'POST',
          body: {
            overlay_feature: params.overlayFeature,
          },
        })
        if (response.error) {
          throw response.error
        }
        const result = response as QueryReturnValue<{
          physical_layer_feature: GeoFeatureGroup
          bounded_overlay_feature: GeoFeature
        }>
        if (result.data === undefined) {
          throw new Error('Unknown error')
        }
        const data = {
          physicalLayerFeature: parseGeoFeatures(
            result.data.physical_layer_feature,
            params.layerType
          ),
          boundedOverlayFeature: result.data.bounded_overlay_feature,
        }
        return { data }
      },
      onCacheEntryAdded: async (
        params,
        { dispatch, getCacheEntry, cacheDataLoaded }
      ) => {
        if (params.layerType === 'elevation') {
          await cacheDataLoaded
          const { data } = getCacheEntry()
          if (data) {
            const limits = computeElevationLimits(data.physicalLayerFeature)
            dispatch(mapActions.updateElevationLayerLimits(limits))
          }
        }
      },
      keepUnusedDataFor: 60,
    }),
  }),
})

const computeElevationLimits = (features: GeoFeatureGroup) => {
  let minElevation: number | null = null
  let maxElevation: number | null = null

  features.features.forEach((feature) => {
    const elevation = feature?.properties?.elevation
    if (typeof elevation === 'number') {
      if (minElevation === null || elevation < minElevation) {
        minElevation = elevation
      }
      if (maxElevation === null || elevation > maxElevation) {
        maxElevation = elevation
      }
    }
  })
  const min = minElevation === null ? MIN_ELEVATION : minElevation
  const max = maxElevation === null ? MAX_ELEVATION : maxElevation + 25
  return { minElevation: min, maxElevation: max, elevationStep: 25 }
}

const parseGeoFeatures = (features: GeoFeatureGroup, layerType: LayerType) => {
  if (layerType === 'titles') {
    return {
      ...features,
      features: features?.features?.map((feature) => ({
        ...feature,
        properties: parseTitleProperties(feature.properties),
      })),
    }
  }
  return features
}

export const { useGetPhysicalLayerQuery, useGetFilteredPhysicalLayerQuery } =
  gisApi
