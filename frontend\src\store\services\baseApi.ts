import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import queryString from 'query-string'

export let baseURL: string | undefined = undefined

if (process.env.NODE_ENV === 'development') {
  baseURL = 'http://localhost:8000'
}

export const urlBase = baseURL === undefined ? `/api/` : `${baseURL}/api/`

const readCookieValue = (cookieName: string) => {
  const match = document.cookie.match(
    new RegExp('(^|;\\s*)(' + cookieName + ')=([^;]*)')
  )
  return match ? decodeURIComponent(match[3]) : null
}

const gisTags = ['PhysicalProperties']

const mapTags = [
  'ViewportAddresses',
  'ViewportSales',
  'ViewportResourceConsents',
  'ViewportListings',
  'ViewportAnzUnion',
  'ViewportTitles',
  'ViewportTerritorialAuthorities',
]

const valocitySaleTags = [
  'ValocitySale',
  'ValocitySaleTitles',
  'ValocitySaleAddress',
  'ValocitySaleElevation',
  'ValocitySaleUnion',
  'ValocitySaleSmapFamily',
  'ValocitySaleNeighbours',
  'ValocitySales',
  'ViewportValocitySales',
]

const addressTag = ['AddressPDFData']

const saleTags = [
  'SaleTitles',
  'SaleFiles',
  'SaleNeighbours',
  'SaleUnion',
  'SaleAddress',
  'SaleDescription',
  'SaleElevation',
  'SaleUnion',
  'SaleSmap',
  'SaleDescription',
  'Sales',
  'Listings',
  'Sale',
  'PendingSales',
  'SalePDFData',
]

const assetTags = [
  'Assets',
  'PlantingAssets',
  'BuildingOutlines',
  'AssetsGeometry',
  'ComparableSales',
  'BenchmarkComparableSales',
  'AssetDescriptions',
  'TitleApportionment',
]

const taskTags = ['UserTasks']

const notificationTags = ['Notifications', 'NotificationCount']

const valuationTags = [
  'Valuations',
  'ValuationTitles',
  'ValuationSummary',
  'ValuationPDFData',
]

const rvrTags = ['RVR', 'RVRValuations', 'RVRAttachment']

const tradingGroupTags = ['TradingGroupValuations']

export const baseApi = createApi({
  baseQuery: fetchBaseQuery({
    baseUrl: baseURL ?? window.location.origin,
    credentials: 'include',
    prepareHeaders: (headers) => {
      headers.set('X-CSRFTOKEN', readCookieValue('csrftoken') ?? '')
      return headers
    },
    paramsSerializer: (params: Record<string, any>) =>
      queryString.stringify(params),
  }),
  tagTypes: [
    'PhysicalProperties',
    'ConsentFiles',
    ...assetTags,
    ...saleTags,
    'User',
    ...valocitySaleTags,
    ...gisTags,
    ...saleTags,
    ...taskTags,
    ...notificationTags,
    ...valuationTags,
    ...addressTag,
    ...mapTags,
    ...rvrTags,
    ...tradingGroupTags,
  ],
  endpoints: () => ({}),
})
