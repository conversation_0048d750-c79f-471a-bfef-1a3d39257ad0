import { isANZ } from './banks'

const messageOptions = {
  showPrefix: false,
  showStack: false,
}

describe('Matches bank names', () => {
  test('Matches ANZ aliases', () => {
    for (const alias of [
      'ANZ Bank Limited',
      'The Rural Bank Limited',
      'The Rural Banking and Finance Corporation of New Zealand',
      'Rural Banking and Finance Corporation of New Zealand Limited',
      'The Rural Banking and Finance Corporation',
      'Rural Banking and Finance Corporation of New Zealand',
      'Rural Banking and Finance Corporation',
      'The Rural Banking and Finance Corporation of New Zealand Limited',
      'Rural Bank Limited',
      'The National Bank of New Zealand Limited',
      'National Bank of New Zealand Limited',
      'National Bank of New Zealand',
      'The National Bank of New Zealand',
      'Countrywide Banking Corporation Limited',
      'Countrywide Building Society',
      'Countrywide Banking Corporation',
      'Post Office Bank Limited',
      'Australia and New Zealand Banking Group Limited',
      'Australia and New Zealand Bank Limited',
      'Mortgage to ANZ Bank Limited',
    ]) {
      expect(
        isANZ(alias),
        `'${alias}' matched false when expecting true`,
        messageOptions
      ).toBeTruthy()
    }
  })

  test('Avoids partial ANZ matches', () => {
    for (const alias of [
      'ANZAC',
      'Mortgage to Protranz International Limited',
    ]) {
      expect(
        isANZ(alias),
        `'${alias}' matched true when expecting false`,
        messageOptions
      ).toBeFalsy()
    }
  })
})
