.action-text {
  @include col-flex;

  color: var(--export-blue);
  text-decoration: none;
  background-color: transparent;
  outline: none;
  transition: 0.2s;
  align-items: center;

  .anticon {
    margin-right: 10px;
  }

  &:hover {
    color: shade($secondary, 25);
    cursor: pointer;
  }

  &[data-disabled='true'],
  &.disabled {
    color: $grey-3;

    &:hover {
      cursor: not-allowed;
      text-decoration: none;
    }
  }
}
