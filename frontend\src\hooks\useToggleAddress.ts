import { useDispatch, useSelector } from 'react-redux'
import type { RootState } from '@store'
import { toggleSelectedAddressId } from '@store/ui/actions'
import { isAddressSelected } from '@store/ui/selectors'

export default function useToggleAddress(id: string) {
  const dispatch = useDispatch()

  const selected = useSelector((state: RootState) =>
    isAddressSelected(state, id)
  )

  const toggle = () => dispatch(toggleSelectedAddressId(id))

  return { selected, toggle }
}
