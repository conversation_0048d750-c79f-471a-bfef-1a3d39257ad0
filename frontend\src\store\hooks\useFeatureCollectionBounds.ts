import {
  bbox as turfBbox,
  featureCollection as turfFeaureCollection,
  polygon as turfPolygon,
} from '@turf/turf'
import { type Feature, FeatureCollection, type Polygon } from 'geojson'
import type { LatLngBoundsLiteral } from 'leaflet'
import { useMemo } from 'react'
import {
  isFeatureCollection,
  isLatLngTuple,
  isPolygonGeometry,
} from '@util/guards'

const useFeatureCollectionBounds = <T>(data: T) => {
  const bounds = useMemo(() => {
    if (!isFeatureCollection(data)) return

    const features = [...(data.features ?? [])].map(({ geometry }) => {
      if (!isPolygonGeometry(geometry)) return
      const { coordinates } = geometry
      return turfPolygon(coordinates)
    }) as unknown as Feature<Polygon>[]

    const collection = turfFeaureCollection(features)
    const [minLat, minLng, maxLat, maxLng] = turfBbox(collection)
    const minBounds = [minLng, minLat]
    const maxBounds = [maxLng, maxLat]

    if (!isLatLngTuple(minBounds) || !isLatLngTuple(maxBounds)) return

    return [minBounds, maxBounds] as LatLngBoundsLiteral
  }, [data])

  return bounds
}

export default useFeatureCollectionBounds
