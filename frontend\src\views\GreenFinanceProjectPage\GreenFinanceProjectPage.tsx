import { Layout } from 'antd'
import { Content } from 'antd/lib/layout/layout'
import React from 'react'
import { Helmet } from 'react-helmet'
import { Outlet } from 'react-router-dom'
import ProtectedRoute from '@components/ProtectedRoute'
import { GreenDrawer } from '@components/green/GreenDrawer'
import { GreenSider } from '@components/green/GreenSider/GreenSider'
import styles from './GreenFinanceProjectPage.module.scss'

export const GreenFinanceProjectPage = () => {
  return (
    <Layout
      className={styles.GreenFinanceProjectPage}
      data-testid="green-finance-project-page"
    >
      <Helmet>
        <title>Green Finance</title>
      </Helmet>
      <GreenSider />
      <ProtectedRoute requiredEntitlements={['client:green_finance:*']}>
        <Content className={styles.viewer}>
          <Outlet />
        </Content>
      </ProtectedRoute>
      <GreenDrawer />
    </Layout>
  )
}
