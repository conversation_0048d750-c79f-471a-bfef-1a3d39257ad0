import type { BenchmarkKpi } from '../models/benchmarking/BenchmarkKpi'
import type { KpiDatasets } from '../models/benchmarking/KpiDatasets'

export interface OptValue {
  label: string
  value: string
}

const expenseOptions: OptValue[] = [
  {
    value: 'totalFarmWorkingExpensesEffArea',
    label: 'Total Farm Working Expenses',
  },
  { value: 'administrationEffArea', label: 'Administration' },
  { value: 'animalHealthEffArea', label: 'Animal Health' },
  { value: 'breedingAndTestingEffArea', label: 'Breeding & Testing' },
  { value: 'contractingEffArea', label: 'Contracting' },
  { value: 'electricityEffArea', label: 'Electricity' },
  {
    value: 'feedHayPerSilagePerGrainEffArea',
    label: 'Feed Hay Per Silage Per Grain',
  },
  { value: 'fertiliserAndLimeEffArea', label: 'Fertiliser & Lime' },
  { value: 'freightEffArea', label: 'Freight' },
  { value: 'grazingEffArea', label: 'Grazing' },
  {
    value: 'miscellaneousPerOtherFweEffArea',
    label: 'Miscellaneous Per Other Fwe',
  },
  { value: 'pollinationEffArea', label: 'Pollination' },
  { value: 'postHarvestExpensesEffArea', label: 'Post Harvest Expenses' },
  { value: 'repairsAndMaintenanceEffArea', label: 'Repairs & Maintenance' },
  { value: 'seedEffArea', label: 'Seed' },
  { value: 'shedExpensesEffArea', label: 'Shed Expenses' },
  { value: 'standingChargesEffArea', label: 'Standing Charges' },
  { value: 'vehicleExpensesEffArea', label: 'Vehicle Expenses' },
  { value: 'wagesEffArea', label: 'Wages' },
  { value: 'weedAndPestEffArea', label: 'Weed & Pest' },
]

const incomeOptions: OptValue[] = [
  { value: 'beefIncomeEffArea', label: 'Beef Income' },
  { value: 'croppingIncomeEffArea', label: 'Cropping Income' },
  { value: 'dairyIncomeEffArea', label: 'Dairy Income' },
  { value: 'deerIncomeEffArea', label: 'Deer Income' },
  {
    value: 'grapesStonefruitIncomeEffArea',
    label: 'Grapes Stonefruit Income',
  },
  { value: 'horticultureIncomeEffArea', label: 'Horticulture Income' },
  { value: 'kiwifruitIncomeEffArea', label: 'Kiwifruit Income' },
  { value: 'milkIncomeEffArea', label: 'Milk Income' },
  { value: 'otherIncomeEffArea', label: 'Other Income' },
  { value: 'otherStockIncomeEffArea', label: 'Other Stock Income' },
  { value: 'pigsIncomeEffArea', label: 'Pigs Income' },
  { value: 'pipfruitIncomeEffArea', label: 'Pipfruit Income' },
  { value: 'poultryIncomeEffArea', label: 'Poultry Income' },
  { value: 'sheepIncomeEffArea', label: 'Sheep Income' },
  { value: 'sundryFarmIncomeEffArea', label: 'Sundry Farm Income' },
  { value: 'velvetIncomeEffArea', label: 'Velvet Income' },
  { value: 'woolIncomeEffArea', label: 'Wool Income' },
  { value: 'offFarmIncomeEffArea', label: 'Off Farm Income' },
  { value: 'capitalIntroducedEffArea', label: 'Capital Introduced' },
]

export const determineValidKpis = (
  tradingGroupKpis: BenchmarkKpi | undefined
) => {
  if (!tradingGroupKpis) {
    return []
  }

  const keyArray: string[] = []
  const validKeys: string[] = []

  for (const key of Object.keys(tradingGroupKpis.benchmark)) {
    const valid = !Object.values(
      tradingGroupKpis.benchmark[key as keyof KpiDatasets]
    ).every((dataset) =>
      dataset?.every((value) => ['0.00', null].includes(value.toString()))
    )
    if (valid) {
      validKeys.push(key)
    }
  }

  const _options = [
    {
      label: 'Income',
      options: incomeOptions.filter(
        (x) => keyArray && validKeys.includes(x.value)
      ),
    },
    {
      label: 'Expenses',
      options: expenseOptions.filter(
        (x) => keyArray && validKeys.includes(x.value)
      ),
    },
  ]

  return _options
}
