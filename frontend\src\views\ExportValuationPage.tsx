import React, { useMemo } from 'react'
import { useParams } from 'react-router-dom'
import { ExportPropertySatelitePage } from '@components/export/pages/ExportPropertySatelitePage'
import { ExportPropertyTitlesSection } from '@components/export/sections/ExportPropertyTitlesSection'
import { LoadingOverlay } from '@components/generic'
import {
  ExportControlOverlay,
  ExportGrid,
} from '../components/export/components'
import {
  ExportAttributions,
  ExportAttributionsItem,
} from '../components/export/components/ExportAttributions'
import { ExportPage } from '../components/export/components/ExportPage'
import { ExportParagraph } from '../components/export/components/ExportParagraph'
import { ExportSection } from '../components/export/components/ExportSection'
import { ExportWrapper } from '../components/export/components/ExportWrapper'
import { CoverPage } from '../components/export/pages/CoverPage'
import { ExportContourProfilePage } from '../components/export/pages/ExportContourProfilePage'
import { ExportLUCPage } from '../components/export/pages/ExportLUCPage'
import { ExportPSPage } from '../components/export/pages/ExportPSPage'
import { ExportTitlePages } from '../components/export/pages/ExportTitlePage'
import { ExportVegetationPage } from '../components/export/pages/ExportVegetationPage'
import { LegalDisclaimerPage } from '../components/export/pages/LegalDisclaimerPage'
import { ExportDistrictValuationRollSection } from '../components/export/sections/ExportDistrictValuationRollSection'
import { ExportPropertyBoundariesSection } from '../components/export/sections/ExportPropertyBoundariesSection'
import type { ValuationPDFData } from '../models/valuations/ValuationPDFData'
import { useGetValuationPDFDataQuery } from '../store/services/valuations'

export interface ExportValuationPageRouteParams {
  addressId: string
}

export const ExportValuationPage = () => {
  const { valuationId = '' } = useParams()

  const { data, isFetching } = useGetValuationPDFDataQuery(
    { valuationId },
    { skip: valuationId === undefined }
  )

  const {
    documentTitle,
    filename,
    center,
    valuation,
    titles,
    districtValuationRoll,
    anzUnion,
    summary,
  } = useMemo(() => {
    return { ...data } as ValuationPDFData
  }, [data])

  document.title = filename

  const documentSubtitle = useMemo(() => {
    return (
      <React.Fragment>
        <div>{valuation?.fullAddress}</div>
        <div>{valuation?.tradingGroupName}</div>
      </React.Fragment>
    )
  }, [valuation])

  if (!data) {
    return <LoadingOverlay />
  }

  return (
    <>
      <ExportControlOverlay isFetching={isFetching} />
      <ExportWrapper>
        <CoverPage
          title={documentTitle}
          isFetching={isFetching}
          subtitle={documentSubtitle}
        />
        <LegalDisclaimerPage />
        <ExportPage
          title="Property Overview"
          subtitle="The titles of a section of land have been simplified
                            into broad categories to summarise the overall
                            profile of a given property."
        >
          <ExportGrid>
            <ExportSection title="Land Description">
              <ExportParagraph html={summary?.serviceCentres} />
              <ExportParagraph html={summary?.landDescription} />
            </ExportSection>
            <ExportSection title="Elevation Profile">
              <ExportParagraph html={summary?.elevation} />
            </ExportSection>
            {districtValuationRoll?.map((dvr) => (
              <ExportDistrictValuationRollSection
                key={dvr.dipid}
                districtValuationRoll={dvr}
                isFetching={isFetching}
              />
            ))}
          </ExportGrid>
          <ExportAttributions>
            <ExportAttributionsItem type="valocity" />
            <ExportAttributionsItem type="linz" />
          </ExportAttributions>
        </ExportPage>
        <ExportPage
          title="Legal Definition and Boundaries"
          subtitle="This section summarises the legal definition of the property in regards to the property's legal titles and legal boundaries. "
        >
          <ExportPropertyTitlesSection
            isFetching={isFetching}
            titles={titles}
          />
          <ExportPropertyBoundariesSection
            center={center}
            isFetching={isFetching}
            titles={titles}
          />
        </ExportPage>
        <ExportTitlePages titles={titles} />
        <ExportPropertySatelitePage
          center={center}
          isFetching={isFetching}
          titles={titles}
        />
        <ExportLUCPage
          anzUnion={anzUnion}
          luc={summary?.anzUnion?.luc}
          center={center}
        />
        <ExportPSPage
          anzUnion={anzUnion}
          ps={summary?.anzUnion?.ps}
          center={center}
        />
        <ExportVegetationPage
          anzUnion={anzUnion}
          vegetation={summary?.anzUnion?.vegetation}
          center={center}
        />
        <ExportContourProfilePage
          center={center}
          isFetching={isFetching}
          titles={titles}
        />
      </ExportWrapper>
    </>
  )
}
