.agrigis-table {
  width: 100%;
  overflow-x: auto;
  margin-top: 4px;

  table {
    width: 100%;
    font-size: 12px;

    tfoot {
      td {
        border: 0;

        &:empty {
          &:before {
            content: 'Total';
            font-style: italic;
          }
        }
      }
    }

    :not(thead + tbody) {
      tr {
        &:first-child {
          td {
            border-top: 2px solid $grey-1 !important;
          }
        }
      }
    }

    thead + tbody,
    tfoot {
      tr {
        &:first-child {
          td {
            border-top: 0 !important;
          }
        }
      }
    }

    tbody {
      tr {
        &:last-child {
          td {
            border-bottom: 2px solid $grey-1 !important;
          }
        }
      }

      tr,
      td {
        border: 0 !important;
        border-bottom: 1px $grey-1 solid !important;
        border-right: 1px $grey-1 solid !important;
      }

      td {
        &:first-child {
          border-left: 0 !important;
        }

        &:last-child {
          border-right: 0 !important;
        }
      }
    }

    thead {
      tr > th {
        border-bottom: 2px solid $grey-1 !important;
      }

      th {
        &:before {
          background-color: rgba(0, 0, 0, 0) !important;
          border: 0;
        }
      }
    }

    tr > th {
      background-color: rgba(0, 0, 0, 0) !important;
      font-weight: 400 !important;
      text-transform: uppercase;
      font-size: 11px;
      line-height: 1.075;
      letter-spacing: 0.06em;
      vertical-align: bottom;
      border: none !important;
    }
  }

  .ant-tabs {
    overflow-y: auto;
  }

  .ant-input {
    width: max-content;
  }

  tr.add > td,
  tr.add:hover > td {
    background-color: rgba(0, 255, 0, 0.1) !important;
    border: solid 1px rgba(0, 255, 0, 0.2) !important;
  }

  tr.existing > td,
  tr.existing:hover > td {
    background-color: rgba(0, 0, 255, 0.1) !important;
    border: solid 1px rgba(0, 0, 255, 0.2) !important;
  }

  tr.remove > td,
  tr.remove:hover > td {
    background-color: rgba(255, 0, 0, 0.1) !important;
    border: solid 1px rgba(255, 0, 0, 0.2) !important;
  }

  tr.irrelevant {
    background-color: rgba(255, 0, 0, 0.1) !important;
  }
}

.agrigis-table-expanded-row {
  table {
    tbody {
      th {
        background-color: rgba(175, 225, 255, 0.2) !important;
        color: $secondary !important;
        border-right: 1px solid $grey-1 !important;
        text-transform: none !important;
      }

      tr {
        &:first-child {
          td {
            border-top: 0 !important;
          }
        }

        &:last-child {
          td,
          th {
            border-bottom: 0 !important;
          }
        }
      }
    }
  }
}

.agrigis-widget-header + .agrigis-table {
  margin-top: $quarter;
}

.agrigis-table {
  tbody > tr.linz {
    &:after {
      position: absolute;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 8px;
      margin-top: 8px;
      background-color: rgba(255, 125, 125, 0.75);
      max-width: 200px;
      height: 30px;
      right: 0;
      padding: 0.5em 1em;
      content: 'Land Information NZ Record';
      border-radius: 5px;
    }

    td:not(:first-child) {
      span {
        display: none;
      }
    }
  }

  ol {
    padding-left: 2em;
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
  }
}

// .agrigis-table .not-selected {
//     .action-text:before {
//         content     : '+';
//         font-size   : 20px;
//         line-height : 12px;
//         margin-top  : -px;
//         font-weight : bold;
//         color       : green !important;
//         margin-right: 8px;
//     }
// }

.agrigis-table .selected {
  background-color: #e4f5d4 !important;

  .action-text {
    display: flex;
    align-items: center;
    align-self: center;
  }

  .action-text:before {
    content: '-';
    font-size: 30px;
    line-height: 12px;
    margin-top: -7px;
    font-weight: bold;
    color: red !important;
    margin-right: 8px;
  }
}
