import { FetchArgs } from '@reduxjs/toolkit/dist/query'
import { cloneDeep } from 'lodash/fp'

/**
 * Replaces deeply nested file uploads with a UUID value, and adds the file to the FormData object
 * with that uuid as the key. Modifies `value` in place...
 * @param data form data object being mutated
 * @param value data being sent to the API endpoint
 * @returns nothing, mutates the data object
 */
const addFileFormDataAndReferences = <V = unknown>(
  data: FormData,
  value: V
) => {
  if (value !== undefined && typeof value === 'object' && value != null) {
    for (const [objKey, objValue] of Object.entries(value)) {
      if (objValue instanceof FileList) {
        const id = crypto.randomUUID()
        for (const file of objValue) {
          data.append(id, file)
        }
        ;(value as any)[objKey] = id
      } else if (objValue instanceof File) {
        const id = crypto.randomUUID()
        ;(value as any)[objKey] = id
        data.append(id, objValue)
      } else {
        addFileFormDataAndReferences(data, objValue)
      }
    }
  }
}

/**
 * Creates a multipart/form-data FormData object with support for nested data, including null values and nested files.
 * For use with the `CamelCaseJsonDataRefMultiPartParser` on the backend. This works by serializing the non-file data into a json blob
 * and adding the files to the FormData object with a UUID key, and replacing file references in the json blob with the UUID.
 * This process is then reversed on the server side before the data is deserialized.
 *
 * @param value data being sent to the API endpoint
 * @returns FormData object
 */
export const buildFileRefFormData = <V = unknown>(value: V) => {
  const data = new FormData()
  // deepcopy to avoid issues with bad files breaking the upload and then these bad uploads staying around
  const clonedValue = cloneDeep(value)
  addFileFormDataAndReferences(data, clonedValue)
  data.set('data', JSON.stringify(clonedValue))
  return data
}

export const createMultipartRefQuery =
  <QueryArg extends Record<string, unknown>>(
    url: string | ((queryArg: QueryArg) => string),
    method: 'POST' | 'PATCH' | 'PUT' = 'POST'
  ) =>
  (queryArg: QueryArg): string | FetchArgs => {
    const formData = buildFileRefFormData(queryArg)

    return {
      method,
      url: typeof url === 'string' ? url : url(queryArg),
      body: formData,
      headers: {
        'Content-Type': undefined,
      },
    }
  }

/**
 * Create form data with nested objects and arrays
 * @example payload.nestedObject.nestedArray[0]arrayObjectProperty
 */
const buildFormData = <V = unknown>(data: FormData, value: V, key = '') => {
  const path = key

  if (value !== undefined) {
    if (value instanceof FileList) {
      let i = 0
      for (const file of value) {
        const itemPath = path ? path + `[${i}]` : path
        data.append(itemPath, file)
        i++
      }
    } else if (value instanceof File) {
      data.append(path, value)
    } else if (Array.isArray(value)) {
      let i = 0
      for (const item of value) {
        const itemPath = path ? path + `[${i}]` : path
        buildFormData(data, item, itemPath)
        i++
      }
    } else if (typeof value === 'object' && value != null) {
      for (const [objKey, objValue] of Object.entries(value)) {
        const objPath = key
          ? [key, objKey].join(key.slice(-1) === ']' ? '' : '.')
          : objKey
        buildFormData(data, objValue, objPath)
      }
    } else if (typeof value === 'string') {
      data.append(path, value)
    } else {
      data.append(path, JSON.stringify(value))
    }
  }

  return data
}

export const createMultipartQuery =
  <QueryArg extends Record<string, unknown>>(
    url: string | ((queryArg: QueryArg) => string),
    method: 'POST' | 'PATCH' | 'PUT' = 'POST'
  ) =>
  (queryArg: QueryArg): string | FetchArgs => {
    const formData = buildFormData(new FormData(), queryArg)

    return {
      method,
      url: typeof url === 'string' ? url : url(queryArg),
      body: formData,
      headers: {
        'Content-Type': undefined,
      },
    }
  }
