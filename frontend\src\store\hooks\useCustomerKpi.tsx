import { useCallback, useEffect, useState } from 'react'
import { useAppDispatch } from '@store'
import type { Kpi } from '@store/services/sdk'
import sdk from '@store/services/sdk/enhanced'

export type ExtendedKpi = { entityName: string } & Kpi

const useCustomerKpi = (ids: { customerId: number; entityName: string }[]) => {
  const [kpis, setKpis] = useState<ExtendedKpi[]>([])
  const [loading, setLoading] = useState<boolean>(false)
  const dispatch = useAppDispatch()

  const getCustomerKpi = useCallback(
    async (customerPk: number, entityName: string) => {
      setLoading(true)
      try {
        const { data } = await dispatch(
          sdk.endpoints.customerKpiList.initiate({
            customerPk,
          })
        )
        return [
          ...(data?.map((f) => ({ ...f, entityName })) ?? []),
        ] as ExtendedKpi[]
      } catch (error) {
        console.error(error)
      }
      return [] as ExtendedKpi[]
    },
    [dispatch]
  )

  useEffect(() => {
    void (async () => {
      const aggKpis = (
        await Promise.all(
          ids.map(({ customerId, entityName }) =>
            getCustomerKpi(customerId, entityName)
          )
        )
      ).flat()
      setKpis(aggKpis)
      setLoading(false)
    })()
  }, [ids, getCustomerKpi])

  return {
    loading,
    kpis,
  }
}

export default useCustomerKpi
