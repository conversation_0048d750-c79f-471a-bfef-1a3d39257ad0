import type { EnhancedStore } from '@reduxjs/toolkit'
import { rootReducer } from './rootReducer'

// Needs to use 'import' over 'module'
// Added to a separate file so we can mock it and avoid the import.meta presence tripping Jest
export function hmrReplace(store: EnhancedStore) {
  // biome-ignore lint:
  if (process.env.NODE_ENV === 'development' && (import.meta as any).hot) {
    // biome-ignore lint:
    ;(import.meta as any).hot.accept('./rootReducer', () =>
      store.replaceReducer(rootReducer)
    )
  }
}
