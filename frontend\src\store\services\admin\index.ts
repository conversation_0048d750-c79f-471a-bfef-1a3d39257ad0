import { Moment } from 'moment'
import { UsageSummary } from '../../../models/admin/UsageSummary'
import { baseApi } from '../baseApi'

export const adminApi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    getUsageStats: build.query<
      UsageSummary,
      Partial<{ dateRange?: [Moment, Moment]; timeUnit?: string }>
    >({
      query: (body) => {
        let query = `?time_unit=${body.timeUnit}`
        query += body.dateRange
          ? `&start_date=${body.dateRange[0]
              .toDate()
              .toISOString()}&end_date=${body.dateRange[1]
              .toDate()
              .toISOString()}`
          : ''
        return `/api/admin/usage/${query}`
      },
    }),
  }),
})

export const { useGetUsageStatsQuery } = adminApi
