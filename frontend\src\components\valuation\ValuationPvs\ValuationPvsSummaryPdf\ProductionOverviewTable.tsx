import {
  HighestAndBestUseSummaryWithAssets,
  type ValuationsPvsSummary,
} from '@store/services/sdk'
import { formatNumber } from '@util/labels'
import KeyValueSummaryTable from './KeyValueSummaryTable'

type Props = {
  summary: ValuationsPvsSummary['summary']
}

const ProductionOverviewTable = ({ summary }: Props) => {
  const rows = [
    { key: 'Total Area (ha)', value: `${summary.totalHectares ?? 0} ha` },
    {
      key: 'Effective Area (ha)',
      value: `${summary.totalEffectiveHectares ?? 0} ha`,
    },
  ]

  return (
    <>
      <KeyValueSummaryTable margins={false} rows={rows} />
    </>
  )
}

export default ProductionOverviewTable
