import { skipToken } from '@reduxjs/toolkit/dist/query'
import { useMemo } from 'react'
import { type SmapSibling, useSmapSiblingsListQuery } from '@store/services/sdk'
import type { LayerType } from '../../../models/gis/LayerType'
import type { SmapFeature } from '../../../models/gis/smap/SmapFeature'
import {
  getDominantSiblingFeatures,
  getDominantSiblingLayerLegend,
  getSmuLayerLegend,
} from '../../../models/gis/smap/SmapSibling'
import type { GeoFeature, GeoFeatureGroup } from '../../../types'
import {
  useGetFilteredPhysicalLayerQuery,
  useGetPhysicalLayerQuery,
} from '../../services/gis'
import * as turf from '@turf/turf'

export const useSelectedLayerData = (
  valuationId: string | undefined,
  saleId: string | undefined,
  selectedLayerOption: {
    layerType: LayerType
    filter: { enable: boolean; overlayFeature?: GeoFeature }
  }
) => {
  const { layerType, filter } = selectedLayerOption

  const unfilteredResult = useGetPhysicalLayerQuery(
    filter.enable ? skipToken : { valuationId, saleId, layerType }
  )

  const filteredResult = useGetFilteredPhysicalLayerQuery(
    !filter.enable
      ? skipToken
      : {
          valuationId,
          saleId,
          layerType,
          overlayFeature:
            filter.overlayFeature ?? turf.feature(turf.polygon([])),
        }
  )

  if (filter.enable) {
    // useFilteredPhysicalLayerQuery returns the bounded overlay feature
    // but we only want to return physical layer data here, so destructure and restructure
    const { data, ...rest } = filteredResult
    return { data: data?.physicalLayerFeature, ...rest }
  }
  return unfilteredResult
}

const useSiblingMap = (smu: string[]): Record<string, SmapSibling[]> => {
  const { data: siblings } = useSmapSiblingsListQuery(
    { smu },
    {
      selectFromResult: (result) => ({
        data: result.data?.reduce(
          (accum: Record<string, SmapSibling[]>, siblingData: SmapSibling) => {
            accum[siblingData.smu] = accum[siblingData.smu] ?? []
            accum[siblingData.smu].push(siblingData)
            return accum
          },
          {}
        ),
      }),
    }
  )

  return siblings ?? {}
}

// TODO: maybe make each graph / layer component call their own 'getPhysicalLayer` hook which abstracts this away altogether?
export const useSiblingFeatures = (
  smapFeatures: GeoFeatureGroup<SmapFeature>,
  selectedField: keyof SmapSibling
) => {
  const smu = useMemo(() => {
    return [
      ...new Set(
        smapFeatures?.features?.map((feature) => feature.properties.smu) ?? []
      ),
    ]
  }, [smapFeatures])

  const siblingMap = useSiblingMap(smu)

  const siblingFeatures = useMemo(() => {
    return getDominantSiblingFeatures(siblingMap, smapFeatures)
  }, [siblingMap, smapFeatures])

  const legend = useMemo(() => {
    return getDominantSiblingLayerLegend(siblingFeatures, selectedField)
  }, [siblingFeatures, selectedField])

  const smuLayerLegend = useMemo(() => {
    return getSmuLayerLegend(smapFeatures, siblingMap)
  }, [smapFeatures, siblingMap])

  return {
    siblingMap,
    siblingFeatures,
    smuLayerLegend,
    legend,
  }
}
