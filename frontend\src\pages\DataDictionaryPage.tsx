import {
  type FieldChoice,
  type FieldConstraint,
  type ModelDefinition,
  useDataSchemaQuery,
} from '@store/services/sdk'
import { Card, Col, Descriptions, Layout, Row, Table } from 'antd'
import { useMemo, useState } from 'react'
import styles from './DataDictionaryPage.module.scss'
import { ActionText } from '@components/generic/ActionText'
import classNames from 'classnames'
import { LoadingOutlined } from '@ant-design/icons'
import MermaidGraph from '@components/generic/MermaidGraph'
import ProtectedRoute from '@components/ProtectedRoute'

const { Content, Sider } = Layout

const TableDetails = ({
  selectedModel,
}: {
  selectedModel: ModelDefinition | undefined
}) => {
  return (
    <Descriptions size="small" bordered column={1}>
      <Descriptions.Item label={'Database Table'}>
        {selectedModel?.dbTable}
      </Descriptions.Item>
      {selectedModel?.provider && (
        <Descriptions.Item label={'Provider'}>
          {selectedModel?.provider}
        </Descriptions.Item>
      )}
      {selectedModel?.url && (
        <Descriptions.Item label={'Source'}>
          <a href={selectedModel?.url}>{selectedModel?.name}</a>
        </Descriptions.Item>
      )}
      {selectedModel?.license && (
        <Descriptions.Item label={'License'}>
          <div
            // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
            dangerouslySetInnerHTML={{
              __html: selectedModel?.license ?? '',
            }}
          />
        </Descriptions.Item>
      )}
      <Descriptions.Item label={'Description'}>
        {selectedModel?.description}
      </Descriptions.Item>
      {selectedModel?.mermaid && (
        <Descriptions.Item label={'Lineage'}>
          <div style={{ maxWidth: '500px' }}>
            <MermaidGraph
              md={selectedModel?.mermaid}
              noCss
              key={selectedModel?.modelKey}
            />
          </div>
        </Descriptions.Item>
      )}
      {selectedModel?.exportTable && (
        <Descriptions.Item label={'Export Target'}>
          {selectedModel?.exportTable}
        </Descriptions.Item>
      )}
    </Descriptions>
  )
}

const FieldTable = ({
  selectedModel,
}: {
  selectedModel: ModelDefinition | undefined
}) => {
  return (
    <Table
      bordered
      pagination={false}
      scroll={{ x: 500 }}
      size="small"
      className={classNames('unstyle', styles.FieldTable)}
      dataSource={selectedModel?.fields ?? []}
      columns={[
        { dataIndex: 'name', title: 'Field' },
        {
          dataIndex: 'helpText',
          title: 'Description',
          render: (_) => {
            return _
          },
        },
        { dataIndex: 'fieldType', title: 'Data Type' },
        { dataIndex: 'integrity', title: 'Integrity' },
        { dataIndex: 'confidentiality', title: 'Confidentiality' },
        { dataIndex: 'isPii', title: 'PII Classification' },
        { dataIndex: 'piiType', title: 'PII Type' },
        {
          dataIndex: 'constraints',
          title: 'Field Constraints',
          render: (_, { constraints }) => {
            return (
              <ul>
                {Object.keys(constraints ?? []).map((k) => {
                  if (!constraints) return null
                  const value = constraints[k as keyof FieldConstraint]
                  return (
                    value && (
                      <li key={k}>
                        {k}=
                        {k === 'choices'
                          ? (value as FieldChoice[])
                              .map((x) => x.value)
                              .join(', ')
                          : value.toString()}
                      </li>
                    )
                  )
                })}
              </ul>
            )
          },
        },
      ]}
    />
  )
}

const DataDictionaryPage = () => {
  const [selected, setSelected] = useState<string>()
  const { data: models } = useDataSchemaQuery()

  const selectedModel = useMemo(() => {
    if (!selected || !models) return undefined
    return models.find(({ modelKey }) => modelKey === selected)
  }, [selected, models])

  return (
    <ProtectedRoute>
      <Layout
        style={{ minHeight: '100vh' }}
        className={styles.DataDictionaryPage}
      >
        <Layout>
          <Sider theme="light" className={styles.sider} width={300}>
            {models ? (
              <ul className={styles.tableOfContents}>
                {models?.map(({ modelKey, app, modelName }) => {
                  return (
                    <li key={modelKey}>
                      <ActionText
                        onClick={() => setSelected(modelKey)}
                      >{`${app}.${modelName}`}</ActionText>
                    </li>
                  )
                })}
              </ul>
            ) : (
              <LoadingOutlined />
            )}
          </Sider>
          <Content className={styles.content}>
            {selectedModel && (
              <Card title={selectedModel?.modelKey}>
                <Row gutter={[16, 16]}>
                  <Col xs={24} sm={24} md={24} lg={10} xl={10}>
                    <Row gutter={[16, 16]}>
                      <TableDetails selectedModel={selectedModel} />
                    </Row>
                  </Col>
                  <Col xs={24} sm={24} md={24} lg={14} xl={14}>
                    <FieldTable selectedModel={selectedModel} />
                  </Col>
                </Row>
              </Card>
            )}
          </Content>
        </Layout>
      </Layout>
    </ProtectedRoute>
  )
}

export default DataDictionaryPage
