import type { Address, Title } from '../../types'

export interface DistrictValuationRoll {
  dvrId: string
  dipid: string
  valRef: string
  tlaId: string
  tlaName: string
  fullAddress: string
  category: string
  valDate: string
  cv: number
  lv: number
  iv: number
  landArea: number
  floorArea: number
  improvement: string
  legalDesc: string
  salesGroup: string
  unitsOfUse: string
  landUse: string
  landUseDesc: string
  landZone: string
  landZoneDesc: string
  linzTitles?: Title[]
  address?: Address
}
