import { useMemo } from 'react'
import {
  toBenchmarkCategories,
  toCustomerData,
} from '@components/customer/helpers'
import sdk, {
  type Benchmark,
  type KpiType,
  useValidKpiMeasuresListQuery,
} from '@store/services/sdk'
import { skipArgObject } from '@util/helpers'

export type BenchmarkData = (Benchmark & {
  year: string
  category: string
  value: number | null
})[]

type KpiGraphData = {
  label: string
  description: string
  customerData: {
    year: string
    customer: number | null
  }[]
  denominator: string
  benchmarks: Benchmark[]
  benchmarkData: BenchmarkData
  measureType: Benchmark['measureType']
}

/**
 * @deprecated This should be done on the backend
 */
const useReport = (
  measures: string[],
  customerPk: number,
  segment: 'W' | 'R',
  denominator: string
) => {
  // TODO: This is just fetching static labels and descriptions; move to serializer or something
  const { data: options = [] } = useValidKpiMeasuresListQuery(
    skipArgObject({ segment })
  )

  // TODO: Remove
  const meta = useMemo(() => {
    // const data: Record<string, { label: string; description: string }> = {}
    const data: Record<string, KpiType> = {}
    for (const option of options) {
      data[option.value] = { ...option }
    }
    return data
  }, [options])

  return sdk.useCustomerBenchmarksByMeasureListQuery(
    {
      customerPk,
      measures,
      segment,
      denominator,
    },
    {
      skip: !options,
      selectFromResult: ({ data: _, ...result }) => {
        const currentData = result.currentData || []
        const data: KpiGraphData[] = currentData.map((benchmarks, i) =>
          Object.assign(meta[measures[i]], {
            denominator,
            customerData: benchmarks.map(toCustomerData),
            benchmarkData: benchmarks.flatMap(toBenchmarkCategories),
            benchmarks,
          })
        )
        return { data, ...result }
      },
    }
  )
}

export default useReport
