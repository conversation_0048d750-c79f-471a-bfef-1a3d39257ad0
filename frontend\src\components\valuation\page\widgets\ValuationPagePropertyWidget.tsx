import { Tabs } from 'antd'
import React, { useState } from 'react'
import ValuationTitles from '@components/valuation/ValuationTitles'
import sdk from '@store/services/sdk'
import {
  useGetValuationSummaryQuery,
  useGetValuationWaterConsentsQuery,
} from '@store/services/valuations'
import { ValuationPagePropertyTitleValidationWidget } from './ValuationPagePropertyTitleValidationWidget'
import { ValuationPageDvWidget } from './property/ValuationPageDvWidget'
import { ValuationPageRainfallWidget } from './property/ValuationPageRainfallWidget'
import { ValuationPageServiceCentresWidget } from './property/ValuationPageServiceCentreWidget'
import { ConsentsWidget } from './property/consents/ConsentsWidget'

interface ValuationPagePropertyWidgetProps {
  addressId: string
  valuationId: string
  disabled?: boolean
}

export const ValuationPagePropertyWidget = ({
  disabled,
  addressId,
  valuationId,
}: ValuationPagePropertyWidgetProps) => {
  const [tab, setTab] = useState<string>('CONSENTS')

  const { data: titles, isFetching: titlesLoading } =
    sdk.useValuationsTitlesListQuery({ valuationPk: +valuationId })

  const { data: valuationPageWaterConsents } =
    useGetValuationWaterConsentsQuery(valuationId)

  const { address, valuationTitles, valuationDvr } =
    useGetValuationSummaryQuery(valuationId, {
      selectFromResult: ({ data: result }) => {
        return {
          address: result?.address,
          valuationTitles: result?.titles,
          valuationDvr: result?.dvr ?? [],
        }
      },
    })

  return (
    <>
      <Tabs defaultActiveKey={tab} onChange={setTab}>
        <Tabs.TabPane tab="Titles" key="TITLES">
          <ValuationPagePropertyTitleValidationWidget
            addressId={addressId}
            valuationId={valuationId}
            disabled={disabled}
          />
          <ValuationTitles loading={titlesLoading} titles={titles} />
        </Tabs.TabPane>
        {!valuationDvr || valuationDvr.length === 0 ? (
          <></>
        ) : (
          <Tabs.TabPane tab="Valuations" key="DVR">
            <ValuationPageDvWidget
              valuationTitles={valuationTitles}
              valuationDvr={valuationDvr || []}
            />
          </Tabs.TabPane>
        )}
        <Tabs.TabPane tab="Consents" key="CONSENTS">
          <ConsentsWidget
            valuationId={valuationId}
            disabled={disabled}
            valuationPageWaterConsents={valuationPageWaterConsents}
          />
        </Tabs.TabPane>
        {address && (
          <Tabs.TabPane tab="Distances" key="SERVICE_CENTRES">
            <ValuationPageServiceCentresWidget address={address} />
          </Tabs.TabPane>
        )}
        <Tabs.TabPane tab="Rainfall" key="RAINFALL">
          <ValuationPageRainfallWidget
            addressId={addressId}
            valuationId={valuationId}
          />
        </Tabs.TabPane>
      </Tabs>
    </>
  )
}
