import { WarningOutlined } from '@ant-design/icons'
import { Alert, Table } from 'antd'
import type { FeatureCollection, Geometry } from 'geojson'
import React, { useMemo } from 'react'
import { Widget } from '@components/generic/Widget'
import { formatDescription } from '@components/sales/dashboard/SelectedSaleDetails'
import type { WaterConsent } from '@models/valuations/WaterConsent'

interface ValuationPageWaterConsentWidgetProps {
  valuationPageWaterConsents: FeatureCollection<Geometry, WaterConsent>
}

export const ValuationPageWaterConsentWidget = ({
  valuationPageWaterConsents,
}: ValuationPageWaterConsentWidgetProps) => {
  const waterConsents = useMemo(
    () =>
      valuationPageWaterConsents.features.map((x) => {
        return {
          ...x.properties,
          key: x.properties.resourceConsentId,
        }
      }),
    [valuationPageWaterConsents]
  )

  const columns = useMemo(() => {
    return [
      {
        dataIndex: 'resourceConsentId',
        title: 'ID',
        render: formatDescription,
      },
      {
        dataIndex: 'councilRegionName',
        title: 'Council',
        render: formatDescription,
      },
      {
        dataIndex: 'primarySource',
        title: 'Source',
        render: formatDescription,
      },
      {
        dataIndex: 'primaryUse',
        title: 'Use',
        render: formatDescription,
      },
      {
        dataIndex: 'maxAnnual',
        title: 'Max Annual',
        render: formatDescription,
      },
      {
        dataIndex: 'maxRate',
        title: 'Max Rate',
        render: formatDescription,
      },
    ]
  }, [])

  if (!waterConsents || waterConsents.length === 0) {
    return <div />
  }

  return (
    <Widget title="Water Consents">
      <Alert
        message="Warning"
        type="warning"
        icon={<WarningOutlined />}
        showIcon={true}
        className="agrigis-alert"
        description={
          <span>
            Ownership data is not available from LAWA sourced data, these water
            consents have been identified by geospatial intersection.
            <br />
            If consent data is relied upon, please ensure that resource consent
            belongs to property.
          </span>
        }
      />
      <div className="agrigis-table">
        <Table
          size="small"
          columns={columns}
          dataSource={waterConsents}
          className="resource-consent-table"
          pagination={false}
        />
      </div>
    </Widget>
  )
}
