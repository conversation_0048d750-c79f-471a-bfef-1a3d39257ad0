import type { DistrictValuationRoll } from '../dvr/DistrictValuationRoll'
import type { TitleFeatureCollection } from '../title/TitleFeatureCollection'

export interface Address {
  addressId: string
  address: string
  lat: number
  lng: number
  summary?: string
  districtValuationRoll?: DistrictValuationRoll
  linzTitles?: TitleFeatureCollection
  landArea?: number
  landZone?: string
  landUse?: string
  cv?: number
  iv?: number
  lv?: number
  owners?: string
  mortgagee?: string
  linked?: boolean
  tradingGroupName?: string
  fullAddress?: string
  lastSale?: {
    saleDate: Date
    grossSalesPrice: number
    saleId: string
  }
}
