import type { BaseQueryFn, QueryDefinition } from '@reduxjs/toolkit/dist/query'
import type { QueryArgFrom } from '@reduxjs/toolkit/dist/query/endpointDefinitions'
import type { UseQuery } from '@reduxjs/toolkit/dist/query/react/buildHooks'
import type { LatLngBounds } from 'leaflet'
import { shallowEqual } from 'react-redux'
import { useSelector } from '@store'
import { getBounds, getZoom } from '@store/features/explorer'

type MapLayerPayload = {
  bounds: LatLngBounds | undefined
  zoom: number | undefined
}

function useMapLayerQuery<U extends BaseQueryFn, V>(
  hook: UseQuery<QueryDefinition<MapLayerPayload, U, string, V>>
) {
  const { bounds, zoom } = useSelector(
    (state) => ({
      bounds: getBounds(state),
      zoom: getZoom(state),
    }),
    shallowEqual
  )

  const payload = { bounds, zoom } as QueryArgFrom<
    QueryDefinition<MapLayerPayload, U, string, V>
  >

  const options = {
    refetchOnMountOrArgChange: true,
    skip: !(bounds && zoom),
  }

  return hook(payload, options)
}

export default useMapLayerQuery
