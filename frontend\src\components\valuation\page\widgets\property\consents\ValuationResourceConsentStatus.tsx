import { Form, Input, Skeleton, Space } from 'antd'
import React from 'react'
import { AssessmentForm } from '@components/form/AssessmentForm'
import { Title } from '@components/typography'
import { useValuation } from '@components/valuation/context'
import useMutationForm from '@hooks/useMutationForm'
import sdk, { useValuationsRetrieveQuery } from '@store/services/sdk'
import { skipArgObject } from '@util/helpers'
import styles from './ValuationResourceConsentStatus.module.scss'

const REVIEW_PATH = ['patchedValuationsWritable', 'resourceConsentReview']

const Placeholder = () => (
  <Space direction="vertical" size="large" className={styles.skeleton}>
    <div className={styles.row}>
      <Skeleton.Input active size="small" />
      <Skeleton.Input active size="default" />
    </div>
    <div className={styles.row}>
      <Skeleton.Input active size="small" />
      <Skeleton.Input active size="small" className={styles.skeletonTextarea} />
    </div>
    <Skeleton.Button active />
  </Space>
)

const ValuationResourceConsentStatus = () => {
  const { id } = useValuation()

  const { data: valuation } = useValuationsRetrieveQuery(
    skipArgObject({ pk: id })
  )

  const { status, form, ...formProps } = useMutationForm(
    sdk.useValuationsPartialUpdateMutation
  )

  const editable = valuation && !valuation.completedDate && !status?.isLoading

  return (
    <div>
      <Title level={3}>Review</Title>
      {valuation ? (
        <AssessmentForm
          {...formProps}
          disabled={!editable}
          form={form}
          initialValue={valuation.resourceConsentReview}
          loading={status?.isLoading}
          path={REVIEW_PATH}
          status
        >
          <Form.Item hidden name="pk" initialValue={valuation.id}>
            <Input />
          </Form.Item>
        </AssessmentForm>
      ) : (
        <Placeholder />
      )}
    </div>
  )
}

export default ValuationResourceConsentStatus
