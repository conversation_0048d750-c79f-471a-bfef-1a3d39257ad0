export interface RVRSummary {
  reportInstructedByAnz: RVRSummaryFormValue
  valuationCompletedInAccordanceToInstruction: RVRSummaryFormValue
  addressedToAnz: RVRSummaryFormValue
  validAge: RVRSummaryFormValue
  compliantToStandards: RVRSummaryFormValue
  accreditedValuer: RVRSummaryFormValue
  specialisedAgriReferredToRequiredLicences: RVRSummaryFormValue<string | null>
  adverseTitleProperties: RVRSummaryFormValue
  reportedOnLeaseholdTenureTerms: RVRSummaryFormValue<string | null>
  adverseLocation: RVRSummaryFormValue
  operativeDistrictLandUsePermitted: RVRSummaryFormValue
  allResourceConsentsHeld: RVRSummaryFormValue
  anyResourceConsentIssues: RVRSummaryFormValue
  areImprovementsSuitable: RVRSummaryFormValue
  anyContingenciesIdentified: RVRSummaryFormValue
  avtAgreeOnHighestAndBestUse: RVRSummaryFormValue
  avtAgreeOnAep: RVRSummaryFormValue
  otherFactorsInfluencingMarketValue: RVRSummaryFormValue
  comfortableWithRoaModelling: RVRSummaryFormValue
  anySpecialAssumptionsOrLimitingFactors: RVRSummaryFormValue
  valuerIdentifiedNegativeFeaturesOfProperty: RVRSummaryFormValue
  adoptedMarketValueJustifiedBySales: RVRSummaryFormValue
  appropriateProductiveValueAnalysis: RVRSummaryFormValue
  siteInspectionWarranted: RVRSummaryFormValue

  robustForMortgageSecurityPurposes: boolean
  conclusionComments: string
}

export interface RVRSummaryFormValue<T = string> {
  value: T
  comments?: string
  readOnly: boolean
}

type RVRSummaryFormValues = {
  [key in keyof RVRSummary]: RVRSummaryFormValue
}

export interface RVRSummaryForm extends RVRSummaryFormValues {
  completed: boolean
}
