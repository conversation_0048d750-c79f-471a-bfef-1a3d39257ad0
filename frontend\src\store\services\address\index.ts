import { Feature, Point } from 'geojson'
import { ConsolidatedSimpleSale } from '@models/sales/ConsolidatedSimpleSale'
import { AddressFeature } from '../../../models/address/AddressFeatureCollection'
import { AddressNeighbourFeatureCollection } from '../../../models/address/AddressNeighbour'
import { AddressPDFData } from '../../../models/address/AddressPDFData'
import { DistrictValuationRoll } from '../../../models/dvr/DistrictValuationRoll'
import { PaginatedResponse } from '../../../models/generic/PaginatedResponse'
import { AnzUnionFeatureCollection } from '../../../models/gis/AnzUnionFeatureCollection'
import { ElevationFeatureCollection } from '../../../models/gis/ElevationFeatureCollection'
import { SmapFamilyFeatureCollection } from '../../../models/gis/SmapFamilyFeatureCollection'
import { TitleFeatureCollection } from '../../../models/title/TitleFeatureCollection'
import {
  Address,
  AddressServiceCentre,
  GeoFeature,
  GeoFeatureGroup,
  TitleMemorial,
} from '../../../types'
import { baseApi } from '../baseApi'
import { Sale } from '../sdk'

export function buildArrayUrlParam(
  array: string[] | undefined,
  key: string
): string {
  let _array =
    array
      ?.map((value: string) => encodeURIComponent(value))
      ?.join(`&${key}=`) || ''
  _array = _array ? `&${key}=` + _array : ''
  return _array
}

function buildParamsString(payload: any) {
  const {
    match,
    bestUses,
    regions,
    minCv,
    maxCv,
    minIv,
    maxIv,
    minLv,
    maxLv,
    landZones,
    minFloorArea,
    maxFloorArea,
    minLandArea,
    maxLandArea,
    page,
  } = payload

  const paramsMap: any = {
    match: match,
    page: page,
    district_valuation_roll__cv__gte: minCv,
    district_valuation_roll__cv__lte: maxCv,
    district_valuation_roll__iv__gte: minIv,
    district_valuation_roll__iv__lte: maxIv,
    district_valuation_roll__lv__gte: minLv,
    district_valuation_roll__lv__lte: maxLv,
    district_valuation_roll__floor_area__gte: minFloorArea,
    district_valuation_roll__floor_area__lte: maxFloorArea,
    district_valuation_roll__land_area__gte: minLandArea,
    district_valuation_roll__land_area__lte: maxLandArea,
  }

  const params = []
  for (const [key, value] of Object.entries(paramsMap)) {
    if (value !== undefined) {
      params.push(`${key}=${value}`)
    }
  }

  let paramsString = '?' + params.join('&')

  paramsString += buildArrayUrlParam(
    bestUses,
    'district_valuation_roll__land_use_desc__in'
  )
  paramsString += buildArrayUrlParam(
    regions,
    'district_valuation_roll__tla_name__in'
  )
  paramsString += buildArrayUrlParam(
    landZones,
    'district_valuation_roll__land_zone_desc__in'
  )

  return paramsString
}

export const addressApi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    getAddress: build.query<Address, string>({
      query: (addressId) => `/api/v2/address/${addressId}/`,
    }),
    getAddressMemorials: build.query<
      PaginatedResponse<TitleMemorial[]>,
      { addressId: string; page: number }
    >({
      query: (args) =>
        `/api/v2/address/${args?.addressId}/memorials/?page=${args?.page}`,
    }),
    getAddressNearbySales: build.query<
      PaginatedResponse<
        Feature<Point, Sale['properties'] & { source?: string }>[]
      >,
      { addressId: string; page: number; radius: number }
    >({
      query: (args) =>
        `/api/v2/address/${args?.addressId}/nearby_sales/?page=${args?.page}&radius_km=${args.radius}`,
    }),
    lookupAddressTitles: build.mutation<
      TitleFeatureCollection,
      { addressIds: string[] }
    >({
      query: (body) => {
        return {
          method: 'POST',
          url: '/api/v2/lookup_address_titles/',
          body,
        }
      },
    }),
    getAddresses: build.query<PaginatedResponse<AddressFeature[]>, any>({
      query: (body) => {
        const paramsString = buildParamsString({ ...body })
        return '/api/v2/address/' + paramsString
      },
    }),
    getAddressElevation: build.query<ElevationFeatureCollection, string>({
      query: (addressId) => `/api/v2/address/${addressId}/elevation/`,
    }),
    getAddressUnion: build.query<AnzUnionFeatureCollection, string>({
      query: (addressId) => `/api/v2/address/${addressId}/union/`,
    }),
    getSmapFamily: build.query<SmapFamilyFeatureCollection, string>({
      query: (addressId) => `/api/v2/address/${addressId}/smap_family/`,
    }),
    getAddressTitles: build.query<TitleFeatureCollection, string>({
      query: (addressId) => `/api/v2/address/${addressId}/titles/`,
    }),
    getAddressSales: build.query<
      PaginatedResponse<ConsolidatedSimpleSale[]>,
      { addressId: string }
    >({
      query: (args) => `/api/v2/address/${args?.addressId}/sales/`,
    }),
    getAddressNeighbours: build.query<
      AddressNeighbourFeatureCollection,
      string
    >({
      query: (addressId) => `/api/v2/address/${addressId}/neighbours/`,
    }),
    getAddressDvr: build.query<DistrictValuationRoll, string>({
      query: (addressId) => `/api/v2/address/${addressId}/dvr/`,
    }),
    getAddressServiceCentreDistances: build.query<
      GeoFeatureGroup<AddressServiceCentre>,
      string
    >({
      query: (addressId) =>
        `/api/address/${addressId}/service_centre_distances/`,
      transformResponse: (response: any) => {
        response.features.forEach((feature: GeoFeature<any>) => {
          feature.properties = {
            address_id: feature.properties.address.toString(),
            rank: feature.properties.rank,
            generalized_classification:
              feature.properties.generalized_classification,
            classification_code: feature.properties.classification_code,
            name: feature.properties.name,
            distance: feature.properties.distance,
          }
        })
        return response
      },
    }),
    getAddressPDFData: build.query<
      AddressPDFData,
      { addressId: string; excluded?: Array<number> }
    >({
      query: (body) => {
        let url = `/api/v2/address/${body?.addressId}/pdf/?`
        body?.excluded?.forEach((titleId: number) => {
          url += `&excluded=${titleId}`
        })
        return url
      },
      providesTags: (result, error, args) => [
        {
          type: 'AddressPDFData',
          id: `${args?.addressId}-${args.excluded?.join('-') ?? ''}`,
        },
      ],
    }),
  }),
})

export const {
  useGetAddressQuery,
  useGetAddressElevationQuery,
  useGetAddressUnionQuery,
  useGetAddressTitlesQuery,
  useGetAddressNeighboursQuery,
  useGetAddressDvrQuery,
  useGetAddressesQuery,
  useGetSmapFamilyQuery,
  useGetAddressServiceCentreDistancesQuery,
  useGetAddressSalesQuery,
  useGetAddressMemorialsQuery,
  useGetAddressNearbySalesQuery,
  useLookupAddressTitlesMutation,
  useGetAddressPDFDataQuery,
} = addressApi
