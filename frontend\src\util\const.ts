export const ENV_DEVELOPMENT = process.env.NODE_ENV === 'development'

export const ENV_UAT =
  ENV_DEVELOPMENT || window.location.origin.includes('.nz.service.test')

export const API_HOST = ENV_DEVELOPMENT ? 'http://localhost:8000' : ''  // Direct connection to riskradar Django backend

export const LOCALE = 'en-NZ'

export const CURRENCY = 'NZD'

export const LOAN_GRAPH_FIELDS = ['Loan', 'Green Loan', 'Limit']

export const INVISIBLE_CIRCLE_MARKER_PROPS = {
  radius: 0,
  fillOpacity: 0,
  color: 'rgba(0, 0, 0, 0)',
}

export const LABEL_DISPLAY_OPTIONS = [
  'Auto',
  'All',
  'None',
  'Minimum Area',
] as const

export const EXPLORER_LAYERS = [
  'addresses',
  'anzUnion',
  'consents',
  'sales',
  'listings',
  'titles',
  'valocitySales',
  'valocityListings',
] as const

export const LEAFLET_LAYERS = {
  transport: {
    attribution: '',
    // 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
    url: 'https://services.arcgisonline.com/ArcGIS/rest/services/Reference/World_Transportation/MapServer/tile/{z}/{y}/{x}',
  },
  terrain: {
    attribution:
      'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
    url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
  },
  linz: {
    attribution: 'Powered by <a href="https://linz.govt.nz">LINZ</a>',
    url: 'https://basemaps.linz.govt.nz/v1/tiles/aerial/EPSG:3857/{z}/{x}/{y}.webp?api=c01gbk76x5fmgnr7rds19wxd8vz',
  },
  road: {
    attribution: '',
    // '&amp;copy <a href="http://osm.org/copyright">OpenStreetMap</a> contributors',
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
  },
  contour: {
    attribution: 'Powered by <a href="https://linz.govt.nz">LINZ</a>',
    url: 'https://tiles-cdn.koordinates.com/services;key=9f7f7e0f623544afae68bfd23d69e52a/tiles/v4/layer=52343/EPSG:3857/{z}/{x}/{y}.png',
  },
  suburb: {
    attribution: '',
    // 'Esri, HERE, Garmin, &copy; OpenStreetMap contributors, and the GIS User Community',
    url: 'https://services.arcgisonline.com/ArcGIS/rest/services/Reference/World_Boundaries_and_Places/MapServer/tile/{z}/{y}/{x}',
  },
}

// En Dash
export const PLACEHOLDER_CHAR = '–'

// WxH
export const PRINT_PAGE_FORMATS = {
  A3: [3508, 4960],
  A4: [2480, 3508],
}

export type PrintPageFormat = keyof typeof PRINT_PAGE_FORMATS

export const SATELLITE_LAYERS = ['OpenStreetMap', 'OpenTopoMap']

export const STREET_TYPES = [
  'Access',
  'Adit',
  'Airport',
  'Alley',
  'Anchorage',
  'Apiti',
  'Approach',
  'Ara',
  'Arahutika',
  'Arawhataraki',
  'Arcade',
  'Arch',
  'Arnaud',
  'Arterial',
  'Avenue',
  'Bank',
  'Bay',
  'Beach',
  'Belfry',
  'Belt',
  'Bend',
  'Bluff',
  'Boardwalk',
  'Bollard',
  'Boulevard',
  'Brae',
  'Braes',
  'Braigh',
  'Branch',
  'Briars',
  'Broadway',
  'Bypass',
  'Centre',
  'Chance',
  'Chase',
  'Circle',
  'Circus',
  'Claim',
  'Cliffs',
  'Close',
  'Common',
  'Companionway',
  'Concourse',
  'Court',
  'Courts',
  'Cove',
  'Crescent',
  'Crest',
  'Croft',
  'Crossing',
  'Crowsnest',
  'Cul',
  'Cutting',
  'Dale',
  'Dales',
  'Dell',
  'Deviation',
  'Dividend',
  'Domain',
  'Downs',
  'Drive',
  'Dune',
  'Dunes',
  'Edge',
  'Elm',
  'Enclave',
  'End',
  'Esplanade',
  'Estate',
  'Estuary',
  'Extension',
  'Fairway',
  'Fairways',
  'Fall',
  'Fare',
  'Farms',
  'Fen',
  'Fern',
  'Fjord',
  'Ford',
  'Forum',
  'Furlong',
  'Gable',
  'Gallant',
  'Garden',
  'Gardens',
  'Gate',
  'Gem',
  'Glade',
  'Glebe',
  'Glen',
  'Glove',
  'Grange',
  'Green',
  'Greens',
  'Grove',
  'Gully',
  'Haiona',
  'Harbour',
  'Haven',
  'Head',
  'Heights',
  'Highlands',
  'Highway',
  'Hill',
  'Ho',
  'Hope',
  'Huts',
  'Inlet',
  'Island',
  'Isle',
  'Key',
  'Kilns',
  'Knob',
  'Knoll',
  'Knowe',
  'Lagoon',
  'Landing',
  'Lane',
  'Layline',
  'Lea',
  'Leader',
  'Leigh',
  'Line',
  'Link',
  'Longburn',
  'Lookout',
  'Loop',
  'Mainsail',
  'Mall',
  'Marina',
  'Marlinspike',
  'Masthead',
  'Mead',
  'Meadows',
  'Mews',
  'Mile',
  'Moorings',
  'Motu',
  'Mount',
  'Moutere',
  'Mow',
  'Nest',
  'Nook',
  'Oaks',
  'Oasis',
  'Ocean',
  'Octagon',
  'Oho',
  'Outlook',
  'Oval',
  'Paddock',
  'Palms',
  'Parade',
  'Park',
  'Parkway',
  'Pass',
  'Passage',
  'Path',
  'Pavillions',
  'Peninsula',
  'Pier',
  'Pines',
  'Place',
  'Plaza',
  'Pohangina',
  'Point',
  'Priors',
  'Prom',
  'Promenade',
  'Puramahoi',
  'Quadrant',
  'Quarterdeck',
  'Quay',
  'Reach',
  'Reef',
  'Reserve',
  'Rest',
  'Retreat',
  'Ridge',
  'Ridgeway',
  'Rigi',
  'Rise',
  'Ritz',
  'River',
  'Riwaka',
  'Road',
  'Roadway',
  'Rocks',
  'Rosebowl',
  'Route',
  'Row',
  'Rue',
  'Run',
  'Runway',
  'Rush',
  'Settlement',
  'Shute',
  'Siding',
  'Slope',
  'Sounding',
  'Spa',
  'Spinney',
  'Spur',
  'Square',
  'Stables',
  'State Highway',
  'Steep',
  'Steps',
  'Stoke',
  'Stonestack',
  'Straight',
  'Strand',
  'Street',
  'Tail',
  'Tapawera',
  'Te Ara',
  'Tee',
  'Terrace',
  'Terraces',
  'Topdeck',
  'Tors',
  'Track',
  'Trail',
  'Tramway',
  'Trees',
  'Vale',
  'Valley',
  'Via',
  'View',
  'Views',
  'Village',
  'Villas',
  'Vista',
  'Vue',
  'Waiano',
  'Walk',
  'Watch',
  'Waters',
  'Way',
  'Wells',
  'West',
  'Wharf',
  'Whenua',
  'Whistlestop',
  'Wickets',
  'Willows',
  'Wynd',
  'Yardarm',
  'Zag',
]

export const STREET_SUFFIXES = [
  'Central',
  'East',
  'Extension',
  'Lower',
  'No 1',
  'No 2',
  'North',
  'South',
  'Upper',
  'West',
]

export const ZOOM_SCALE = {
  19: 1128.49722,
  18: 2256.99444,
  17: 4513.98888,
  16: 9027.977761,
  15: 18055.95552,
  14: 36111.91104,
  13: 72223.82209,
  12: 144447.6442,
  11: 288895.2884,
  10: 577790.5767,
  9: 1155581.153,
  8: 2311162.307,
  7: 4622324.614,
  6: 9244649.227,
  5: 18489298.45,
  4: 36978596.91,
  3: 73957193.82,
  2: 147914387.6,
  1: 295828775.3,
  0: 591657550.5,
}

export const VALOCITY_USE_OPTIONS = [
  {
    label: 'COMMERCIAL / MULTI-USE WITHIN COMMERCIAL',
    value: 'COMMERCIAL / MULTI-USE WITHIN COMMERCIAL',
  },
  { label: 'COMMERCIAL / OFFICES', value: 'COMMERCIAL / OFFICES' },
  { label: 'COMMERCIAL / RETAIL', value: 'COMMERCIAL / RETAIL' },
  { label: 'COMMERCIAL / SERVICES', value: 'COMMERCIAL / SERVICES' },
  { label: 'COMMERCIAL / VACANT', value: 'COMMERCIAL / VACANT' },
  { label: 'COMMERCIAL / WHOLESALE', value: 'COMMERCIAL / WHOLESALE' },
  {
    label: 'COMMUNITY SERVICES / DEFENCE',
    value: 'COMMUNITY SERVICES / DEFENCE',
  },
  {
    label: 'COMMUNITY SERVICES / EDUCATIONAL',
    value: 'COMMUNITY SERVICES / EDUCATIONAL',
  },
  {
    label: 'COMMUNITY SERVICES / HALLS',
    value: 'COMMUNITY SERVICES / HALLS',
  },
  {
    label: 'COMMUNITY SERVICES / MULTI-USE WITHIN COMMUNITY SERVICES',
    value: 'COMMUNITY SERVICES / MULTI-USE WITHIN COMMUNITY SERVICES',
  },
  {
    label: 'COMMUNITY SERVICES / RELIGIOUS',
    value: 'COMMUNITY SERVICES / RELIGIOUS',
  },
  {
    label: 'INDUSTRIAL / BUILDING MATERIALS OTHER THAN TIMBER',
    value: 'INDUSTRIAL / BUILDING MATERIALS OTHER THAN TIMBER',
  },
  {
    label: 'INDUSTRIAL / CHEMICAL, PLASTICS, RUBBER AND PAPER',
    value: 'INDUSTRIAL / CHEMICAL, PLASTICS, RUBBER AND PAPER',
  },
  {
    label: 'INDUSTRIAL / DEPOTS, YARDS ETC',
    value: 'INDUSTRIAL / DEPOTS, YARDS ETC',
  },
  {
    label: 'INDUSTRIAL / ENGINEERING, METALWORKING, APPLIANCES, AND MACHINE',
    value: 'INDUSTRIAL / ENGINEERING, METALWORKING, APPLIANCES, AND MACHINE',
  },
  {
    label: 'INDUSTRIAL / FOOD, DRINK AND. TOBACCO',
    value: 'INDUSTRIAL / FOOD, DRINK AND. TOBACCO',
  },
  {
    label: 'INDUSTRIAL / MULTI-USE WITHIN INDUSTRIAL',
    value: 'INDUSTRIAL / MULTI-USE WITHIN INDUSTRIAL',
  },
  {
    label: 'INDUSTRIAL / OTHER INDUSTRIES',
    value: 'INDUSTRIAL / OTHER INDUSTRIES',
  },
  {
    label: 'INDUSTRIAL / TIMBER PRODUCTS AND FURNITURE',
    value: 'INDUSTRIAL / TIMBER PRODUCTS AND FURNITURE',
  },
  { label: 'INDUSTRIAL / VACANT', value: 'INDUSTRIAL / VACANT' },
  { label: 'LIFESTYLE / MULTI UNIT', value: 'LIFESTYLE / MULTI UNIT' },
  {
    label: 'LIFESTYLE / MULTI-USE WITHIN LIFESTYLE',
    value: 'LIFESTYLE / MULTI-USE WITHIN LIFESTYLE',
  },
  { label: 'LIFESTYLE / SINGLE UNIT', value: 'LIFESTYLE / SINGLE UNIT' },
  { label: 'LIFESTYLE / VACANT', value: 'LIFESTYLE / VACANT' },
  {
    label: 'PRIMARY INDUSTRY / ARABLE FARMING',
    value: 'PRIMARY INDUSTRY / ARABLE FARMING',
  },
  {
    label: 'PRIMARY INDUSTRY / DAIRYING',
    value: 'PRIMARY INDUSTRY / DAIRYING',
  },
  {
    label: 'PRIMARY INDUSTRY / FORESTRY',
    value: 'PRIMARY INDUSTRY / FORESTRY',
  },
  {
    label: 'PRIMARY INDUSTRY / MARKET. GARDENS AND ORCHARDS',
    value: 'PRIMARY INDUSTRY / MARKET. GARDENS AND ORCHARDS',
  },
  {
    label: 'PRIMARY INDUSTRY / MINERAL EXTRACTION',
    value: 'PRIMARY INDUSTRY / MINERAL EXTRACTION',
  },
  {
    label: 'PRIMARY INDUSTRY / MULTI-USE WITHIN PRIMARY INDUSTRY',
    value: 'PRIMARY INDUSTRY / MULTI-USE WITHIN PRIMARY INDUSTRY',
  },
  {
    label: 'PRIMARY INDUSTRY / SPECIALIST LIVESTOCK',
    value: 'PRIMARY INDUSTRY / SPECIALIST LIVESTOCK',
  },
  {
    label: 'PRIMARY INDUSTRY / STOCK FATTENING',
    value: 'PRIMARY INDUSTRY / STOCK FATTENING',
  },
  {
    label: 'PRIMARY INDUSTRY / STORE SHEEP',
    value: 'PRIMARY INDUSTRY / STORE SHEEP',
  },
  { label: 'PRIMARY INDUSTRY / VACANT', value: 'PRIMARY INDUSTRY / VACANT' },
  {
    label: 'RECREATIONAL / PASSIVE OUTDOOR',
    value: 'RECREATIONAL / PASSIVE OUTDOOR',
  },
  { label: 'RECREATIONAL / VACANT', value: 'RECREATIONAL / VACANT' },
  { label: 'RESIDENTIAL / BACH', value: 'RESIDENTIAL / BACH' },
  { label: 'RESIDENTIAL / MULTI-UNIT', value: 'RESIDENTIAL / MULTI-UNIT' },
  {
    label: 'RESIDENTIAL / MULTI-USE WITHIN RESIDENTIAL',
    value: 'RESIDENTIAL / MULTI-USE WITHIN RESIDENTIAL',
  },
  {
    label: 'RESIDENTIAL / PUBLIC COMMUNAL - UNLICENSED',
    value: 'RESIDENTIAL / PUBLIC COMMUNAL - UNLICENSED',
  },
  {
    label: 'RESIDENTIAL / SINGLE UNIT (OTHER THAN BACH)',
    value: 'RESIDENTIAL / SINGLE UNIT (OTHER THAN BACH)',
  },
  { label: 'RESIDENTIAL / VACANT', value: 'RESIDENTIAL / VACANT' },
  {
    label: 'TRANSPORT / RAIL TRANSPORT',
    value: 'TRANSPORT / RAIL TRANSPORT',
  },
  {
    label: 'TRANSPORT / ROAD TRANSPORT',
    value: 'TRANSPORT / ROAD TRANSPORT',
  },
  { label: 'TRANSPORT / VACANT', value: 'TRANSPORT / VACANT' },
  {
    label: 'TRANSPORT / WATER TRANSPORT',
    value: 'TRANSPORT / WATER TRANSPORT',
  },
  {
    label: 'UTILITY SERVICES / COMMUNICATIONS',
    value: 'UTILITY SERVICES / COMMUNICATIONS',
  },
  {
    label: 'UTILITY SERVICES / ELECTRICITY',
    value: 'UTILITY SERVICES / ELECTRICITY',
  },
  {
    label: 'UTILITY SERVICES / MULTI-USE WITHIN UTILITY SERVICES',
    value: 'UTILITY SERVICES / MULTI-USE WITHIN UTILITY SERVICES',
  },
  { label: 'UTILITY SERVICES / OTHER', value: 'UTILITY SERVICES / OTHER' },
]

export const PRICE_VALUE_RELATIONSHIP_DESCRIPTIONS: Record<string, string> = {
  'Market interim': `
Arm's-length sale where the physical attributes of the property sold may
not match the rating unit information recorded in the DVR, and a further
review of the sale is required.
  `,
  'Non-Market level': `
Non arm's-length sale or sales which can never be matched with the rating
unit's rateable value.
  `,
  'Market level': `
Arm's-length sale at market price which can be matched with the rating
unit information recorded in the DVR.
`,
}
