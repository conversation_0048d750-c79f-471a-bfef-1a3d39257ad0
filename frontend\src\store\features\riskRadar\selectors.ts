import { createSelector } from '@reduxjs/toolkit'
import { pick } from 'lodash'
import _ from 'lodash'
import type { RootState } from '@store/types'
import { isNonNullable } from '@util/guards'
import { joinArrayProperties } from '@util/helpers'
import { initialState } from './slice'
import { SCENARIO_REQUIRED_FIELDS } from './util'

const select = (state: RootState) => state.riskRadar

const createRiskRadarSelector = <T>(
  callback: (state: ReturnType<typeof select>) => T
) => createSelector(select, callback)

export const getScenarioDimensions = (state: RootState) => {
  return state.riskRadar.scenario.dimensions
}

export const getAreasOfInterest = createRiskRadarSelector((state) => {
  const structure = [
    { type: 'territorialUnit0', length: 1, position: 0 },
    { type: 'territorialUnit1', length: 2, position: 1 },
    { type: 'territorialUnit2', length: 3, position: 2 },
    { type: 'territorialUnit3', length: 4, position: 3 },
    { type: 'territorialUnit4', length: 5, position: 4 },
    { type: 'territorialUnit5', length: 6, position: 5 },
  ]
  return structure.reduce(
    (acc, { type, length, position }) => {
      acc[type as keyof typeof acc] = (state.scenario.dimensions ?? [])
        .filter((x) => x.length === length)
        .flatMap((x) => x[position])
        .join(',')
      return acc
    },
    {} as {
      territorialUnit0: string
      territorialUnit1: string
      territorialUnit2: string
      territorialUnit3: string
      territorialUnit4: string
      territorialUnit5: string
    }
  )
})

export const getInterpretedDimensions = (state: RootState) => {
  return state.riskRadar.scenario.dimensions
    .filter((x) => x.length === 4)
    .map((x) => x[3])
    .join(',')
}

export const getMapState = (state: RootState) => {
  return { ...initialState.map, ...state.riskRadar.map }
}

export const getScenarioState = createRiskRadarSelector((riskRadar) => ({
  ...initialState.scenario,
  ...riskRadar.scenario,
}))

export const getScenarioWithAreasOfInterest = createSelector(
  [getScenarioState, getAreasOfInterest],
  (scenario, areas) => ({ ...scenario, ...areas })
)

export const getPerilValid = createSelector(
  getScenarioState,
  (scenario) => scenario.lossModel && scenario.perilType
)

export const getPerilImpactedArgs = createSelector(
  getScenarioWithAreasOfInterest,
  (scenario) =>
    joinArrayProperties(
      pick(scenario, [
        'perilType',
        'lossModel',
        'dimension',
        'territorialUnit0',
        'territorialUnit1',
        'territorialUnit2',
        'territorialUnit3',
        'territorialUnit4',
        'territorialUnit5',
        'propertyType',
        'propertyZoning',
        'propertyStatus',
        'assetClass',
        'assetPortfolio',
        'anzsic',
        'ccr',
        'si',
        'roofConstruction',
        'customerSegment',
        'wallConstruction',
      ])
    )
)

export const getPerilSummaryArgs = createSelector(
  getScenarioWithAreasOfInterest,
  (scenario) =>
    joinArrayProperties(
      pick(scenario, [
        'perilType',
        'lossModel',
        'dimension',
        'territorialUnit0',
        'territorialUnit1',
        'territorialUnit2',
        'territorialUnit3',
        'territorialUnit4',
        'territorialUnit5',
        'propertyType',
        'propertyZoning',
        'propertyStatus',
        'assetClass',
        'assetPortfolio',
        'anzsic',
        'ccr',
        'si',
        'roofConstruction',
        'customerSegment',
        'wallConstruction',
        'anzPropertyClass',
        'valocityPropertyClass',
      ])
    )
)

export const getSnakePerilSummaryArgs = createSelector(
  getPerilSummaryArgs,
  (args) => {
    const serializedArgs: { [key: string]: string | unknown | undefined } = {}
    Object.entries(args).map(([k, v]) => {
      if (k && v) serializedArgs[_.snakeCase(k)] = v
    })
    return serializedArgs
  }
)

export const getScenarioRiskType = (state: RootState) => {
  return state.riskRadar.scenario.perilType
}

export const getScenarioValid = createSelector([getScenarioState], (scenario) =>
  Object.values(pick(scenario, SCENARIO_REQUIRED_FIELDS)).every(isNonNullable)
)

export const getLoading = createRiskRadarSelector(
  (state) => state.layersLoading > 0
)

export const getSelectedGroupId = (state: RootState) => {
  return state.riskRadar.selectedGroupId
}

export const getMapLayerState = (state: RootState) => {
  return { ...initialState.map.layers, ...state.riskRadar.map.layers }
}

export const getPerilType = (state: RootState) => {
  return state.riskRadar.scenario.perilType
}

export const getPerilCategory = createSelector(
  getScenarioState,
  (state) => state.perilCategory
)

export const getLossModel = (state: RootState) => {
  return state.riskRadar.scenario.lossModel
}

export const getMapSettings = (state: RootState) => {
  return { ...initialState.map.settings, ...state.riskRadar.map.settings }
}

export const getMapLayers = (state: RootState) => {
  return { ...initialState.map.layers, ...state.riskRadar.map.layers }
}
