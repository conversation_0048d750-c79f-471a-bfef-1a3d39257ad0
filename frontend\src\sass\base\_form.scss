.agrigis-form {
  .ant-form-item {
    margin-bottom: 0;
  }

  .search-box-text {
    font-size: 11px !important;
    color: #004165;
    text-transform: uppercase;
    letter-spacing: 1.5px;
  }

  small,
  .ant-select-selection-item {
    font-size: 11px !important;
  }

  .wrap-div {
    display: flex;
    flex-wrap: wrap;
    column-gap: 1vw;

    &::before {
      content: attr(title);
      white-space: pre;
      flex: 100%;
      text-transform: uppercase !important;
      letter-spacing: 1px;
      color: $primary;
      margin-top: 8px;
      margin-left: 8px;
    }

    .ant-form-item {
      flex-grow: 1;
    }
  }

  .form-input-container {
    margin: 4px 0;
    padding: 0 8px;
    flex-grow: 1;
    flex: 33%;
    height: 100%;

    &.hidden {
      display: none;
    }

    &.form-input-textarea {
      flex: 100%;

      textarea {
        min-height: 150px;
      }
    }

    &.select {
      flex: 40%;
    }

    div {
      height: 100%;

      .ant-picker {
        min-height: 32px;
        width: 100%;
      }
    }

    .ant-select {
      width: 100%;
    }
  }

  div[role='alert'] {
    font-size: 11px;
  }

  .ant-form-item-has-error.ant-form-item-has-feedback.ant-form-item-children-icon {
    display: none;
  }

  span.input-label {
    text-transform: uppercase;
    font-size: 11px;
    color: $grey-5;

    &.required::after {
      content: ' *';
      color: red;
      font-weight: 600;
    }

    &::after {
      content: '\A';
      white-space: pre;
    }
  }

  .ant-input-prefix {
    color: $grey-4;
    font-size: 12px;
  }

  input,
  textarea,
  .ant-checkbox-wrapper {
    font-size: 12px;
  }
}
