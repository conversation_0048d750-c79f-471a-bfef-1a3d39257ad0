import { type PayloadAction, createSlice, isAnyOf } from '@reduxjs/toolkit'
import type { LatLngBoundsExpression } from 'leaflet'
import sdk from '@store/services/sdk'
import { getStoredState } from '@store/util'

export type ScenarioDimensionType =
  | 'territorial_unit_0'
  | 'territorial_unit_1'
  | 'territorial_unit_2'
  | 'territorial_unit_3'
  | 'territorial_unit_4'
  | 'territorial_unit_5'

export interface RiskRadarMapLayerState {
  peril: boolean
  customer: boolean
}

export interface RiskRadarScenario {
  dimension: ScenarioDimensionType
  lossModel: number | undefined
  dimensions: (
    | [string, string, string, number]
    | [string, string, number]
    | [string, string]
    | [string]
  )[]
  perilCategory: number | undefined
  perilType: number | undefined
  anzPropertyClass: string[] | undefined
  valocityPropertyClass: string[] | undefined
  wallConstruction: string[] | undefined
  roofConstruction: string[] | undefined
  setCode: string[] | undefined
  propertyType: string[] | undefined
  propertyZoning: string[] | undefined
  propertyStatus: string[] | undefined
  assetClass: string[] | undefined
  assetPortfolio: string[] | undefined
  ccr: string[] | undefined
  si: string[] | undefined
  anzsic: string[] | undefined
  customerSegment: string[] | undefined
}

export interface RiskRadarState {
  scenario: RiskRadarScenario
  selectedGroupId: string | undefined
  map: {
    layers: RiskRadarMapLayerState
    bounds: LatLngBoundsExpression | undefined
    center: {
      lat: number | undefined
      lng: number | undefined
    }
    settings: {
      perilVisualisation: 'peril' | 'lossModel' | undefined
      perilTransparency: number | undefined
    }
    zoom: number | undefined
    selectedProperty:
      | {
          locationId: number
          lat: number
          lng: number
        }
      | undefined
  }
  layersLoading: 0
}

const SLICE_NAME = 'riskRadar'

export const initialState: RiskRadarState = {
  scenario: {
    dimension: 'territorial_unit_2',
    dimensions: [],
    lossModel: undefined,
    perilCategory: undefined,
    perilType: undefined,
    anzPropertyClass: undefined,
    valocityPropertyClass: undefined,
    wallConstruction: undefined,
    roofConstruction: undefined,
    setCode: undefined,
    propertyType: undefined,
    propertyZoning: undefined,
    propertyStatus: undefined,
    assetClass: undefined,
    assetPortfolio: undefined,
    ccr: undefined,
    si: undefined,
    anzsic: undefined,
    customerSegment: undefined,
  },
  selectedGroupId: undefined,
  map: {
    layers: {
      peril: true,
      customer: true,
    },
    center: {
      lat: undefined,
      lng: undefined,
    },
    settings: {
      perilVisualisation: 'peril',
      perilTransparency: 50,
    },
    zoom: undefined,
    bounds: undefined,
    selectedProperty: undefined,
  },
  layersLoading: 0,
}

const riskRadar = createSlice({
  name: SLICE_NAME,
  initialState: {
    ...initialState,
    ...getStoredState(SLICE_NAME),
  },
  reducers: {
    setScenario(
      state,
      { payload }: PayloadAction<Partial<RiskRadarState['scenario']>>
    ) {
      state.scenario = { ...state.scenario, ...payload }
    },
    setScenarioAnzsics: (
      state,
      {
        payload,
      }: PayloadAction<{
        anzsic: RiskRadarState['scenario']['anzsic']
      }>
    ) => {
      state.scenario.anzsic = payload.anzsic
    },
    setScenarioAssetClass: (
      state,
      {
        payload,
      }: PayloadAction<{
        assetClass: RiskRadarState['scenario']['assetClass']
      }>
    ) => {
      state.scenario.assetClass = payload.assetClass
    },
    setScenarioDimension: (
      state,
      {
        payload,
      }: PayloadAction<{
        dimension: RiskRadarState['scenario']['dimension']
      }>
    ) => {
      state.scenario.dimension = payload.dimension
    },
    setLossModel: (
      state,
      {
        payload,
      }: PayloadAction<{
        lossModel: RiskRadarState['scenario']['lossModel']
      }>
    ) => {
      state.scenario.lossModel = payload.lossModel
    },
    setScenarioDimensions: (
      state,
      {
        payload,
      }: PayloadAction<{
        dimensions: RiskRadarState['scenario']['dimensions']
      }>
    ) => {
      state.scenario.dimensions = payload.dimensions
    },
    setPerilCategory: (
      state,
      {
        payload,
      }: PayloadAction<{
        perilCategory: RiskRadarState['scenario']['perilCategory']
      }>
    ) => {
      state.scenario.perilCategory = payload.perilCategory
      state.scenario.perilType = undefined
      state.scenario.lossModel = undefined
    },
    setPerilType: (
      state,
      {
        payload,
      }: PayloadAction<{
        perilType: RiskRadarState['scenario']['perilType']
      }>
    ) => {
      state.scenario.perilType = payload.perilType
    },
    setMapBounds: (
      state,
      { payload }: PayloadAction<{ bounds: RiskRadarState['map']['bounds'] }>
    ) => {
      state.map.bounds = payload.bounds
    },
    setMapZoom: (
      state,
      { payload }: PayloadAction<{ zoom: RiskRadarState['map']['zoom'] }>
    ) => {
      state.map.zoom = payload.zoom
    },
    setMapCenter: (
      state,
      { payload }: PayloadAction<{ center: RiskRadarState['map']['center'] }>
    ) => {
      state.map.center = payload.center
    },
    setMapSelectedProperty: (
      state,
      {
        payload,
      }: PayloadAction<
        { lat: number; lng: number; locationId: number } | undefined
      >
    ) => {
      state.map.selectedProperty = payload
    },
    setSelectedGroupId: (
      state,
      { payload }: PayloadAction<{ groupId: string | undefined }>
    ) => {
      state.selectedGroupId = payload.groupId
    },
    setMapLayerState: (
      state,
      {
        payload,
      }: PayloadAction<{
        layerKey: keyof RiskRadarMapLayerState
        layerState: boolean
      }>
    ) => {
      if (!state.map.layers) {
        state.map.layers = { ...initialState.map.layers }
      }
      state.map.layers[payload.layerKey] = payload.layerState
    },
    setMapSettings(
      state,
      { payload }: PayloadAction<Partial<RiskRadarState['map']['settings']>>
    ) {
      state.map.settings = { ...state.map.settings, ...payload }
    },
  },
  extraReducers: (builder) => {
    builder.addMatcher(
      isAnyOf(sdk.endpoints.perilList.matchPending),
      (state) => {
        state.layersLoading += 1
      }
    )
    builder.addMatcher(
      isAnyOf(
        sdk.endpoints.perilList.matchFulfilled,
        sdk.endpoints.perilList.matchRejected
      ),
      (state) => {
        state.layersLoading -= 1
      }
    )
  },
})

export const actions = riskRadar.actions

export const reducer = riskRadar.reducer

export default riskRadar
