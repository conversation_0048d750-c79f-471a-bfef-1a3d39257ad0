import { createErrorMessage } from './error'

const removeWhitespace = (value: string) => value.replace(/\s/g, '')

describe('Format an error message', () => {
  test('Formats a validation error', () => {
    const formatted = createErrorMessage({
      obj: {
        objPropOne: ['This field is required.'],
        objPropTwo: [
          {
            arrPropOne: ['This field is required.'],
            arrPropTwo: ['This field is required.'],
          },
          {
            arrPropOne: ['This field is required.'],
          },
        ],
      },
    })

    const expected = `
      obj → objPropOne: This field is required.
      obj → objPropTwo → arrPropOne: This field is required.
      obj → objPropTwo → arrPropTwo: This field is required.
      obj → objPropTwo → arrPropOne: This field is required.
    `

    expect(removeWhitespace(formatted)).toEqual(removeWhitespace(expected))
  })
})
