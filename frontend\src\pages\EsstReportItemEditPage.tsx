import { Helmet } from 'react-helmet'
import EsstReportItemView from '@components/esst/EsstReportItemView'
import EsstLayout from '@components/esst/EsstLayout'
import { useParams } from 'react-router-dom'
import { LinkBack } from '@components/anz/Link'

export default function EsstReportItemEditPage() {
  const { esstReportItemId } = useParams()

  return (
    <>
      <Helmet>
        <title>New ESST Report Item</title>
      </Helmet>
      <EsstLayout actions={<LinkBack to="../../">Back to Overview</LinkBack>}>
        <EsstReportItemView esstReportItemId={Number(esstReportItemId)} />
      </EsstLayout>
    </>
  )
}
