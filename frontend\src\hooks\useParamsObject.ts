import { useCallback, useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'

function useParamsObject<T extends Record<string, string>>(
  init?: T
): [T, (params: Partial<T>) => void] {
  const [searchParams, setSearchParams] = useSearchParams(init)

  const params = useMemo(
    () => Object.fromEntries(searchParams.entries()),
    [searchParams]
  ) as T

  const setParams = useCallback(
    (newParams: Partial<T>) => {
      const updatedParams = { ...params, ...newParams }
      // Doing some filthy casting to make this a little nicer to use by accepting partials or undefined values
      const filteredParams = Object.entries(updatedParams)
        .filter(([, v]) => v)
        .reduce((obj, [k, v]) => {
          obj[k as keyof T] = v
          return obj
        }, {} as T)
      setSearchParams(filteredParams)
    },
    [params, setSearchParams]
  )

  return [params, setParams]
}

export default useParamsObject
