import type { AppThunk } from '../../types'
import { mapActions } from '../map'
import { assetsSlice } from './slice'

export const {
  beginAssetEdit,
  toggleAssetLayer,
  toggleAssetForEditing,
  stageAssetForDeletion,
  cancelPendingEdits: _cancelPendingEdits,
  setAddRemainingGeometry,
  updateFeatureUnderEdit,
  buildingOutlineClicked,
  commitPendingEditsComplete,
} = assetsSlice.actions

export const cancelPendingEdits = (): AppThunk => (dispatch) => {
  dispatch(_cancelPendingEdits())
  dispatch(mapActions.toggleByAssetFilters({ enable: true }))
  dispatch(mapActions.resetLayerSelectionFilter())
}

/*
type x = ActionCreatorWithPayload;

export const cancelPendingEdits: typeof _cancelPendingEdits = (payload) => {
    dispatch(_cancelPendingEdits(payload))
}
*/
