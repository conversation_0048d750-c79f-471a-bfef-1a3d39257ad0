import type { AddressFeature } from '@models/address/AddressFeatureCollection'
import type { DistrictValuationRoll } from '../dvr/DistrictValuationRoll'
import type { PDFData } from '../generic/PDFData'
import type { AnzUnionFeatureCollection } from '../gis/AnzUnionFeatureCollection'
import type { TitleFeatureCollection } from '../title/TitleFeatureCollection'
import type { SaleFeature } from './SaleFeatureCollection'
import type { SalePropertyDescription } from './SalePropertyDescription'

export interface SalePDFData extends PDFData {
  address: AddressFeature
  sale: SaleFeature
  titles: TitleFeatureCollection
  districtValuationRoll: Array<DistrictValuationRoll>
  anzUnion: AnzUnionFeatureCollection
  propertyDescription: SalePropertyDescription
  summary: {
    landDescription: string
    serviceCentres: string
    elevation: string
    anzUnion: {
      luc: { [key: string]: number }
      ps: { [key: string]: number }
      vegetation: { [key: string]: number }
    }
  }
}
