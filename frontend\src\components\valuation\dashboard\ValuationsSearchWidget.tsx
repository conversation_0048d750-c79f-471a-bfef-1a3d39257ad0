import React from 'react'
import { Widget } from '../../generic/Widget'
import { ValuationsSearchFilters } from './ValuationsSearchFilters'
import { ValuationsTable } from './ValuationsTable'
import type { ValuationSearch } from '@store/services/sdk'

interface ValuationsSearchWidgetProps {
  valuations: ValuationSearch[]
  valuationsCount: number
  creatorDisabled?: boolean
  isLoading: boolean
}

export const ValuationsSearchWidget = (props: ValuationsSearchWidgetProps) => {
  const { valuations, valuationsCount, creatorDisabled, isLoading } = props

  return (
    <>
      <Widget>
        <ValuationsSearchFilters creatorDisabled={creatorDisabled} />
      </Widget>
      <Widget>
        <ValuationsTable
          isLoading={isLoading}
          creatorDisabled={creatorDisabled}
          valuations={valuations}
          valuationsCount={valuationsCount}
        />
      </Widget>
    </>
  )
}
