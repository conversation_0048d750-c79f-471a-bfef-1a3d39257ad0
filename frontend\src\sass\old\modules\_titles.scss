.title-table {
  width: 100%;
}

tr.selected-title > td,
tr.selected-title:hover > td {
  background-color: rgba(0, 255, 0, 0.1) !important;
  border: solid 1px rgba(0, 255, 0, 0.2) !important;
}

// .extents-shared-title {
//     background-color: #fcdfcd;
// }

tr.deleted-title > td,
tr.deleted-title:hover > td {
  background-color: rgba(255, 0, 0, 0.1) !important;
  border: solid 1px rgba(255, 0, 0, 0.2) !important;
}

.title-table td,
.title-table th,
.title-table tr {
  border: 0.5px solid tint($grey, 50);
}

.title-table tr.ant-table-row:hover > td {
  background: inherit;
}

.memorials-table {
  font-size: 0.7rem;
  padding: 0;
}

.memorials-table.table-sm th,
.memorials-table.table-sm td {
  padding: 0;
}

.search-controls {
  margin-bottom: 3em;
  width: 100%;
}

.title-search-controls-radius {
  padding: $half $half $quarter $half;
}
