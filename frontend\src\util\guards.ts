import type { Feature, FeatureCollection, Geometry, Polygon } from 'geojson'
import type { LatLngTuple, Map as LeafletMap } from 'leaflet'
import type { RedirectState } from './types'

export const hasKey = <T extends Record<string, unknown>>(
  obj: T,
  key: string | number | symbol
): key is keyof T => key in obj

export const isString = (value: unknown): value is string =>
  typeof value === 'string'

export const hasString = (value: unknown): value is string | string[] =>
  Array.isArray(value) || isString(value)

export function hasRedirectState(state: unknown): state is RedirectState {
  return (
    isObjectLiteral(state) &&
    'from' in state &&
    isObjectLiteral(state.from) &&
    'state' in state.from
  )
}

export function isLatLngTuple(value: unknown): value is LatLngTuple {
  if (!Array.isArray(value)) return false
  return value.length === 2 && value.every((v) => typeof v === 'number')
}

function getArrayDepth(value: unknown): number {
  return Array.isArray(value) ? 1 + Math.max(0, ...value.map(getArrayDepth)) : 0
}

export function isPolygonGeometry(value: unknown): value is Polygon {
  return (
    isObjectLiteral(value) &&
    'type' in value &&
    value.type === 'Polygon' &&
    'coordinates' in value &&
    getArrayDepth(value.coordinates) === 3
  )
}

export function isFeature<T extends Geometry, P>(
  value: unknown
): value is Feature<T, P> {
  return (
    isObjectLiteral(value) &&
    'type' in value &&
    'geometry' in value &&
    isObjectLiteral(value.geometry)
  )
}

export function isFeatureWithProperties<T extends Geometry, P>(
  value: unknown
): value is Feature<T, P> {
  return (
    isFeature(value) &&
    'properties' in value &&
    isObjectLiteral(value.properties)
  )
}

export function isFeatureCollection<T extends Geometry, P>(
  value: unknown
): value is FeatureCollection<T, P> {
  return (
    isObjectLiteral(value) &&
    'type' in value &&
    value.type === 'FeatureCollection' &&
    'features' in value &&
    Array.isArray(value.features)
  )
}

export const isKeyOf =
  <T extends object>(obj: T) =>
  (key: PropertyKey): key is keyof T => {
    return key in obj
  }

export function isNonNullableObject<T extends { [K in keyof T]: T[K] }>(
  queryParams: T
): queryParams is { [K in keyof T]: NonNullable<T[K]> } {
  return Object.values(queryParams).every(truthy)
}

export function isMapInstance(instance: unknown): instance is LeafletMap {
  return (
    typeof instance === 'object' &&
    instance != null &&
    'getBounds' in instance &&
    'getZoom' in instance
  )
}

export function isObjectLiteral(
  value: unknown
): value is Record<string | number | symbol, unknown> {
  return value != null && !Array.isArray(value) && typeof value === 'object'
}

export function isObjectType<T>(value: unknown, ...keys: string[]): value is T {
  return isObjectLiteral(value) && keys.every((key) => key in value)
}

export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !Number.isNaN(value)
}

export function isNumberOrString(value: unknown): value is number | string {
  return ['number', 'string'].includes(typeof value)
}

export function truthy<T>(value: T): value is NonNullable<T> {
  return !!value
}

export const isMatrix = (data: unknown[]): data is unknown[][] =>
  Array.isArray(data[0])

export const isNonNullable = <T>(value: T): value is NonNullable<T> =>
  value != null

type ScrollEventTarget = EventTarget & {
  scrollTop: number
  scrollHeight: number
  clientHeight: number
}
export function isScrollEventTarget<T>(
  value: T
): value is T & ScrollEventTarget {
  return (
    isObjectLiteral(value) &&
    'scrollTop' in value &&
    'scrollHeight' in value &&
    'clientHeight' in value
  )
}
