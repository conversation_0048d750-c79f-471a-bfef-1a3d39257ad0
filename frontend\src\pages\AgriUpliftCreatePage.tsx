import { UserOutlined } from '@ant-design/icons'
import classNames from 'classnames'
import React from 'react'
import { Helm<PERSON> } from 'react-helmet'
import { Navigate, useNavigate, useSearchParams } from 'react-router-dom'
import { useTradingGroupRetrieveQuery } from '@store/services/sdk'
import AgriUpliftLayout from '@components/agri-uplift/AgriUpliftLayout'
import { AgriUpliftCreateForm } from '@components/agri-uplift/AgriUpliftForm'
import {
  useFinanceAgriUpliftByTradingGroupIdRetrieveQuery,
  type AgriUplift,
} from '@store/services/finance/codegen'
import { TradingGroupSearch } from '@components/tradingGroup/search/TradingGroupSearch'
import { skipArgObject } from '@util/helpers'
import font from '@styles/font.module.css'
import util from '@styles/util.module.css'

const SEARCH_PARAM_KEY: keyof AgriUplift = 'tradingGroupId'

function AgriUpliftCreatePage() {
  const [searchParams, setSearchParams] = useSearchParams()
  const navigate = useNavigate()

  const entityId = searchParams.get(SEARCH_PARAM_KEY)

  const { data: entity, isLoading: entityLoading } =
    useTradingGroupRetrieveQuery(skipArgObject({ pk: entityId }))

  const { data: existingRecord, isLoading: existingRecordLoading } =
    useFinanceAgriUpliftByTradingGroupIdRetrieveQuery(
      skipArgObject({ pk: entityId })
    )

  if (existingRecord?.pk)
    navigate(`/finance/agri-uplift/${existingRecord.pk}`, { replace:true })

  return (
    <>
      <Helmet>
        <title>New</title>
      </Helmet>
      <AgriUpliftLayout
        loading={entityLoading || existingRecordLoading}
        extra={
          <TradingGroupSearch
            onClear={() => setSearchParams(new URLSearchParams({}))}
            onSelect={(value) => {
              setSearchParams(
                new URLSearchParams({ [SEARCH_PARAM_KEY]: value })
              )
            }}
            style={{ minWidth: 300 }}
          />
        }
      >
        {entityId && entity ? (
          <AgriUpliftCreateForm tradingGroupId={entityId} />
        ) : (
          <div
            style={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: 'var(--space-0)',
            }}
            className={classNames(font.body, util.muted)}
          >
            <UserOutlined />
            <span>Select a customer to start</span>
          </div>
        )}
      </AgriUpliftLayout>
    </>
  )
}

export default AgriUpliftCreatePage
