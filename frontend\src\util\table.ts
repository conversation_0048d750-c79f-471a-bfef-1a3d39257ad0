import type { ColumnType } from 'antd/lib/table'
import { ary, flow } from 'lodash'
import { formatDollarValue } from './index'
import {
  formatAddress,
  formatDollarsAsMillions,
  formatNumber,
  formatPercentage,
} from './labels'
import { toMillions } from './number'
import {
  type PropertySorter,
  sortDigitProperty,
  sortTextProperty,
} from './sort'

export const renderAddress = ary(formatAddress, 1)

export const renderDollars = flow(Number, ary(formatDollarValue, 1))

export const renderDollarsMillions = flow(toMillions, formatDollarsAsMillions)

export const renderNumber = ary(formatNumber, 1)

export const renderPercentage = ary(formatPercentage, 1)

const withPropertySorter =
  <T>(sorter: PropertySorter<T>) =>
  (
    column: Omit<ColumnType<T>, 'dataIndex'> &
      Required<Pick<ColumnType<T>, 'dataIndex'>>
  ) => ({
    ...column,
    sorter: sorter(column.dataIndex as keyof T),
  })

export const withNumberSorter = withPropertySorter(sortDigitProperty)

export const withTextSorter = withPropertySorter(sortTextProperty)

// export const getColumnSortOrder = <V extends string>(sortOrder: (`-{V}` | `{V}`)[] | undefined, sortKey: V): ('descend' | 'ascend' | null) => {
export const getColumnSortOrder = (sortOrder: string[] | undefined, sortKey: string): ('descend' | 'ascend' | null) => {
  const order = sortOrder?.find((order) => order === sortKey || order === `-${sortKey}`)
  if (order) {
    return order.startsWith('-') ? 'descend' : 'ascend'
  }
  return null
}
