import {
  AlertOutlined,
  InfoOutlined,
  LockOutlined,
  UserOutlined,
} from '@ant-design/icons'
import { Alert, Card, Col, Form, Input, Row, Space } from 'antd'
import Button from '@anz/button'
import classNames from 'classnames'
import { memo, useCallback } from 'react'
import { Helmet } from 'react-helmet'
import { Navigate } from 'react-router-dom'
import { LoadingOverlay } from '@components/generic'
import { Logo } from '@components/generic/Logo'
import UatLogo from '@components/generic/UatLogo'
import usePreviousLocation from '@hooks/usePreviousLocation'
import { useUserCurrentRetrieveQuery } from '@store/services/sdk'
import { useLoginMutation } from '@store/services/user'
import { getQueryErrorMessage } from '@util/error'
import styles from './LoginPage.module.scss'
import { ENV_UAT } from '@util/const'
import { AnzGrid, AnzRow, AnzCol, AnzGridProps, AnzRowProps } from '@anz/grid'
import { sortedLastIndex } from 'lodash'
import Anz<PERSON>ext<PERSON>ield from '@anz/text-field'


const colStyle = {
  display: 'flex',
  flexGrow: 1,
  flexBasis: 0,
  alignItems: 'center',
  justifyContent: 'center',
  height: '100px',
}

const Login = memo(LoginForm)

type LoginError = ReturnType<typeof useLoginMutation>[1]['error']

const LoginPage = () => {
  const { data: user, refetch } = useUserCurrentRetrieveQuery()
  const { to } = usePreviousLocation()
  const [login, { error, isLoading }] = useLoginMutation()

  const handleLogin = useCallback(
    ({ username, password }: { username: string; password: string }) => {
      login({ username, password }).then(() => refetch())
    },
    [login, refetch]
  )

  if (user) return <Navigate to={to} />

  const isUat = ENV_UAT

  return (
    <>
      <Helmet>
        <title>Login</title>
      </Helmet>
      {isLoading ? <LoadingOverlay /> : null}
      <div
        className={classNames(
          'container',
          styles.LoginPage,
          isLoading ? styles.loading : null
        )}
        data-testid="login-page"
      >


          <Login isUat={isUat} login={handleLogin} loginError={error} />


      </div>

    </>
  )
}

export default LoginPage

const formItemLayout = {
  wrapperCol: { span: 24 },
}

const buttonItemLayout = {
  wrapperCol: { span: 24, offset: 8 },
}

function LoginForm({
  isUat,
  login,
  loginError,
}: {
  isUat: boolean
  login: (payload: { username: string; password: string }) => void
  loginError: LoginError
}) {
  const onFinish = (values: { username: string; password: string }) => {
    void login({
      username: values.username,
      password: values.password,
    })
  }

  return (
    <>
    <AnzGrid fluid>
      <AnzRow center='lg'>
        <AnzCol xs={3} sm={3} md={3} lg={3}>
          &nbsp;
        </AnzCol>
        <AnzCol xs={3} sm={3} md={3} lg={3}>
        </AnzCol>
        <AnzCol xs={3} sm={3} md={3} lg={3}>
          &nbsp;
        </AnzCol>
      </AnzRow>
      <AnzRow center='lg'>

        </AnzRow>
       <AnzRow center='lg'>
         <Form
            name="normal_login"
            className="login-form"
            initialValues={{ remember: true }}
            onFinish={onFinish}
            style={{
              width: '400px',
            }}
          >

        <AnzCol xs={12} sm={6} md={4} lg={12} >


            <Form.Item
              {...formItemLayout}
              name="username"
              rules={[
                {
                  required: true,
                  message: 'Please input your Username',
                },
              ]}
            >
              <Input
                addonBefore={<UserOutlined />}
                placeholder="Username"
                data-cy="username-input"
                autoComplete="current-username"
              />
            </Form.Item>
            <Form.Item
              {...formItemLayout}
              name="password"
              rules={[
                {
                  required: true,
                  message: 'Please input your Password',
                },
              ]}
            >
              <Input
                addonBefore={<LockOutlined />}
                type="password"
                placeholder="Password"
                autoComplete="current-password"
                data-cy="password-input"
              />
            </Form.Item>
            <Form.Item {...buttonItemLayout} name="submit">

              <Button
                type="primary"
                htmlType="submit"
                className="login-form-button"
                data-cy="login-button"
              >
                Log in
              </Button>
            </Form.Item>
        </AnzCol>
         </Form>
      </AnzRow>
    </AnzGrid>

      {/* <Row justify="center" style={{ margin: '10px', textAlign: 'center' }}>
        <Space direction="vertical" align="center">
          {isUat ? (
            <Alert
              data-cy="uat-alert"
              className="agrigis-alert"
              description="This is a preview branch of ESGIS and is not intended to be used by general staff, only use this deployment if you've been explicitly asked. Please do not share your login credentials."
              message="Preview Branch"
              type="warning"
              icon={<AlertOutlined />}
              showIcon
              style={{ margin: '2em 0 2em 0', textAlign: 'left' }}
            />
          ) : (
            <Alert
              data-cy="login-alert"
              className="agrigis-alert"
              message="Log in with your ANZ LAN ID and associated password."
              description={
                <span>
                  Unable to log in? Instructions to request access can be found{' '}
                  <a
                    href="https://confluence.nz.service.anz/display/ESGIS/Requesting+access+to+ESGIS"
                    target="_blank"
                    rel="noreferrer"
                  >
                    here.
                  </a>
                </span>
              }
              type="info"
              icon={<InfoOutlined style={{ flex: 'none' }} />}
              showIcon
              style={{ margin: '2em 0 2em 0', textAlign: 'left' }}
            />
          )}
          {loginError ? (
            <Alert
              data-cy="uat-alert"
              className="agrigis-alert"
              description={getQueryErrorMessage(loginError).replace(
                'Non Field Errors: ',
                ''
              )}
              message="Error"
              type="error"
              icon={<AlertOutlined />}
              showIcon
              style={{ margin: '2em 0 2em 0', textAlign: 'left' }}
            />
          ) : null}
          <Form
            name="normal_login"
            className="login-form"
            initialValues={{ remember: true }}
            onFinish={onFinish}
            style={{
              width: '250px',
            }}
          >
            <Form.Item
              {...formItemLayout}
              name="username"
              rules={[
                {
                  required: true,
                  message: 'Please input your Username',
                },
              ]}
            >
              <Input
                addonBefore={<UserOutlined />}
                placeholder="Username"
                data-cy="username-input"
                autoComplete="current-username"
              />
            </Form.Item>
            <Form.Item
              {...formItemLayout}
              name="password"
              rules={[
                {
                  required: true,
                  message: 'Please input your Password',
                },
              ]}
            >
              <Input
                addonBefore={<LockOutlined />}
                type="password"
                placeholder="Password"
                autoComplete="current-password"
                data-cy="password-input"
              />
            </Form.Item>
            <Form.Item {...buttonItemLayout} name="submit">
              <Button
                type="primary"
                htmlType="submit"
                className="login-form-button"
                data-cy="login-button"
              >
                Log in
              </Button>
            </Form.Item>
          </Form>
        </Space>
      </Row> */}

    </>
  )
}
