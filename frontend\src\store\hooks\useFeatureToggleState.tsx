import { useCallback, useState } from 'react'

type ToggleableFeature<T> = T & {
  id?: number | string
}

export interface FeatureToggleState<T> {
  [id: string]: ToggleableFeature<T>
}

// biome-ignore lint: Generic
export type BulkToggle<T = any> = (features: ToggleableFeature<T>[]) => void

export const useFeatureToggleState = <T,>(): [
  FeatureToggleState<T>,
  (feature: ToggleableFeature<T>) => void,
  BulkToggle<T>,
  () => void,
] => {
  const [state, setState] = useState<FeatureToggleState<T>>({})

  const toggleItem = useCallback((feature: ToggleableFeature<T>) => {
    setState((prev: FeatureToggleState<T>) => {
      const featureId = feature.id?.toString()
      if (!featureId) {
        return prev
      }
      const newState = { ...prev }
      if (newState[featureId]) {
        delete newState[featureId]
      } else {
        newState[featureId] = feature
      }
      return newState
    })
  }, [])

  const bulkToggle = useCallback((features: ToggleableFeature<T>[]) => {
    setState((prev: FeatureToggleState<T>) => {
      const newState = { ...prev }

      const inputFeatures = new Set([...features.map((x) => x.id?.toString())])
      // Take all current selected values and check existing state for deleted items.
      for (const key of Object.keys(newState)) {
        if (inputFeatures.has(key)) {
          delete newState[key]
        }
      }

      // Then loop through and add all the new features
      for (const feature of features) {
        const featureId = feature.id?.toString()
        if (featureId && !newState[featureId]) {
          newState[featureId] = feature
        }
      }

      return newState
    })
  }, [])

  const clearState = useCallback(() => {
    setState({})
  }, [])

  return [state, toggleItem, bulkToggle, clearState]
}
