import React from 'react'
import { PdfTable } from '@components/pdf'
import type { PdfTableRowProps } from '@components/pdf/PdfTable/PdfTableRow'
import type { PdfTableColumnOptions } from '@components/pdf/PdfTable/helpers'
import type { ValuationMortgageApportionment } from '@store/services/sdk'
import { formatNumber } from '@util/labels'

type Props = {
  apportionments: ValuationMortgageApportionment[]
}

const columns: PdfTableColumnOptions[] = [
  { key: 'instrumentNumber', weighting: 1.5 },
  { key: 'titles' },
  { key: 'totalLWB', title: 'Total LWB ($)' },
  {
    key: 'totalImprovementsValue',
    title: 'Total Improvements Value ($)',
    weighting: 1.5,
  },
  { key: 'totalEffectiveHectares', title: 'Total Effective Hectares' },
  { key: 'totalAEP' },
]

const MortgageValueApportionmentTable = ({ apportionments = [] }: Props) => {
  const rows: PdfTableRowProps[] = apportionments.map(
    ({
      mortgageNumber,
      titles,
      total_LWB,
      totalImprovementsValue,
      totalEffectiveHectares,
      total_AEP,
    }) => ({
      instrumentNumber: mortgageNumber.split('|').join('\n\n'),
      titles: titles.map((title) => title.titleNo).join(', '),
      totalLWB: total_LWB,
      totalEffectiveHectares,
      totalImprovementsValue,
      totalAEP: formatNumber(total_AEP),
    })
  )

  return <PdfTable striped columns={columns} rows={rows} />
}

export default MortgageValueApportionmentTable
