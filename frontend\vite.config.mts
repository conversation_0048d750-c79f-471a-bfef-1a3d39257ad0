import react from '@vitejs/plugin-react'
import path from 'node:path'
import { defineConfig, loadEnv } from 'vite'
import tsconfigPaths from 'vite-tsconfig-paths'
import biomePlugin from 'vite-plugin-biome'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const enableLinting = !env.VITE_AG_QUIET

  return {
    plugins: [
      react(),
      tsconfigPaths(),
      enableLinting &&
        biomePlugin({
          mode: 'lint',
          files: './src',
          failOnError: true,
        }),
    ],
    build: {
      sourcemap: true,
      outDir: 'build',
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "./src/sass/base.scss";`,
        },
        less: {
          modifyVars: {
            'primary-color': '#007dba;',
            'font-size-base': '12px',
          },
          javascriptEnabled: true,
        },
      },
    },
    resolve: {
      alias: {
        '@sass': path.resolve(__dirname, './src/sass'),
        '@styles': path.resolve(__dirname, './src/styles'),
      },
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './src/tests/setup.ts',
      deps: {
        fallbackCJS: true,
      },
      test: {
        globals: true,
        environment: 'jsdom',
        setupFiles: './src/tests/setup.ts',
        deps: {
          fallbackCJS: true,
        },
      },
    },
  }
})
