import type { GeoFeatureGeometry } from '../../types'

// Single document
export interface Listing {
  askingPrice: number | null
  cnt?: number
  listingId?: string
  saleId?: string
  dipId?: string
  listingDate: string
  lastObservedDate?: string
  listingSite?: string
  totalArea?: number
  floorArea?: number
  address?: string
  lat?: number
  long?: number
  propertyType?: string
  geometry?: GeoFeatureGeometry
}

// Explorer viewport
export interface ViewportListingProperties {
  askingPrice: number | null
  cnt: number
  fullAddress: string
  id: number
  listingDate: string
  listingSite: string | null
  totalArea: number
}
