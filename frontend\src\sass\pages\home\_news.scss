.home-page-news {
  display: block;
}

.home-page-news-item {
  margin: $half;
  padding: $half 0;

  &:first-child {
    padding-top: 0 !important;
    margin-top: 0 !important;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  span,
  p {
    margin: 0;
    padding: 0;
    text-transform: none;
    letter-spacing: 0;
  }

  font-size: 12px;

  h1 {
    font-size: 1.5em;
    color: $secondary;
    margin-bottom: 0.5em !important;
  }

  h2 {
    font-size: 1.1em;
    color: $grey-5;
    margin-bottom: 0.5em !important;
    font-weight: 700;
    font-style: italic;
  }

  h3 {
    font-size: 1.1em;
    color: $grey-4;
    font-weight: 500;
  }

  h4 {
    font-size: 1em;
    color: $grey-3;
  }

  hr {
    padding: 0;
    margin: 16px 0;
  }

  p {
    color: $grey-6;
    margin: $half 0;
  }
}

.home-page-news-item-content {
  ul {
    background: rgba(225, 225, 225, 0.2);
    border-left: 3px solid rgba(200, 200, 200, 1);
    padding-top: $half;
    padding-bottom: $half;
  }

  li {
    margin: 0;
    color: $grey-6;

    &:before {
      font-size: 12px !important;
      display: inline-block;
      width: min-content;
      margin-right: 8px;
    }
  }

  ul {
    padding-left: 8px;

    > li {
      &:before {
        content: '>';
      }
    }
  }

  ol {
    counter-reset: list-number;
    padding-left: 8px;
  }

  h2 {
    margin-top: 1em !important;
    font-size: 13px !important;
  }
}

.home-page-news-item-header {
  margin-bottom: 0.5em;

  .home-page-news-item-header-text {
    font-size: 16px;
    color: $secondary;
  }

  li {
    color: $grey-5;
    @include col-flex;

    .anticon {
      padding-right: 10px;
    }
  }

  ul {
    padding: 0;
    margin: 0;
  }
}
