import { combineReducers } from '@reduxjs/toolkit'
import { addressesReducer } from './features/address'
import { reducer as admin } from './features/admin'
import { assetsReducer } from './features/assets'
import { reducer as customer } from './features/customer'
import { reducer as explorer } from './features/explorer'
import { mapReducer } from './features/map'
import { reducer as riskRadar } from './features/riskRadar'
import { baseApi } from './services/baseApi'
import { uiReducer } from './ui'

export const rootReducer = combineReducers({
  assets: assetsReducer,
  addresses: addressesReducer,
  explorer,
  admin,
  ui: uiReducer,
  map: mapReducer,
  customer,
  riskRadar,
  [baseApi.reducerPath]: baseApi.reducer,
})
