import { TitleFeature } from '@models/title/TitleFeatureCollection'
import { PaginatedResponse } from '../../../models/generic/PaginatedResponse'
import { baseApi } from '../baseApi'
import { Title, TitleList } from '../sdk'

export const titleApi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    searchTitle: build.query<
      PaginatedResponse<TitleFeature[]>,
      {
        match: string
        page?: number
        radius?: number
        latLng?: { lat: number | undefined; lng: number | undefined }
      }
    >({
      query: (body) => {
        const { match, page, latLng, radius } = body
        const params = new URLSearchParams({
          match: match.toString(),
          page: (page ?? '').toString(),
          lat: (latLng?.lat ?? '').toString(),
          lng: (latLng?.lng ?? '').toString(),
          radius: (radius ?? '').toString(),
        })
        const url = `/api/v2/title_search/?${params.toString()}`
        return url
      },
    }),
    titleList: build.query<TitleListApiResponse, TitleListApiArg>({
      query: (queryArg) => ({
        url: `/api/v2/title/`,
        params: {
          exclude: queryArg.exclude,
          match: queryArg.match,
          page: queryArg.page,
          size: queryArg.size,
          lat: queryArg.lat,
          lng: queryArg.lng,
          radius: queryArg.radius,
        },
      }),
    }),
    titleRetrieve: build.query<TitleRetrieveApiResponse, TitleRetrieveApiArg>({
      query: (queryArg) => ({ url: `/api/v2/title/${queryArg.pk}/` }),
    }),
    selectedTitleDvrList: build.query<
      SelectedTitleDvrListApiResponse,
      SelectedTitleDvrListApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/title/dvrs/`,
        params: { title_ids: queryArg.titleIds },
      }),
    }),
    selectedTitleExportPdf: build.query<
      SelectedTitleExportPdfApiResponse,
      SelectedTitleExportPdfApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/title/export_pdf/`,
        params: { title_ids: queryArg.titleIds },
      }),
    }),
    selectedTitleList: build.query<
      SelectedTitleListApiResponse,
      SelectedTitleListApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/title/list_selected/`,
        params: { title_ids: queryArg.titleIds },
      }),
    }),
    selectedTitleMemorialList: build.query<
      SelectedTitleMemorialListApiResponse,
      SelectedTitleMemorialListApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/title/memorials/`,
        params: { title_ids: queryArg.titleIds },
      }),
    }),
  }),
})

export type PatchedTitle = {
  type?: 'Feature'
  id?: number
  geometry?: {
    type?: 'MultiPolygon'
    coordinates?: number[][][][]
  }
  properties?: {
    fullAddress?: string
    id?: number
    titleNo?: string
    status?: string | null
    type?: string | null
    landDistrict?: string | null
    issueDate?: string | null
    guaranteeStatus?: string | null
    estateDescription?: string | null
    owners?: string | null
    spatialExtentsShared?: string | null
    createdDate?: string | null
    updatedDate?: string | null
    deletedDate?: string | null
    area?: number
    surveyArea?: number
  }
}

export type PaginatedTitleList = {
  count?: number
  next?: string | null
  previous?: string | null
  results?: TitleList
}

export type TitleListApiResponse = /** status 200  */ PaginatedTitleList
export type TitleListApiArg = {
  exclude?: number[]
  match?: string
  /** A page number within the paginated result set. */
  page?: number
  /** Number of results to return per page. */
  size?: number
  lat?: string
  lng?: string
  radius?: number
}
export type TitleCreateApiResponse = /** status 201  */ Title
export type TitleCreateApiArg = {
  title: Title
}
export type TitleRetrieveApiResponse = /** status 200  */ Title
export type TitleRetrieveApiArg = {
  /** A unique value identifying this linz titles. */
  pk: number
}
export type TitleUpdateApiResponse = /** status 200  */ Title
export type TitleUpdateApiArg = {
  /** A unique value identifying this linz titles. */
  pk: number
  title: Title
}
export type TitlePartialUpdateApiResponse = /** status 200  */ Title
export type TitlePartialUpdateApiArg = {
  /** A unique value identifying this linz titles. */
  pk: number
  patchedTitle: PatchedTitle
}
export type TitleDestroyApiResponse = unknown
export type TitleDestroyApiArg = {
  /** A unique value identifying this linz titles. */
  pk: number
}
export type SelectedTitleDvrListApiResponse =
  /** status 200  */ DistrictValuationRoll[]
export type SelectedTitleDvrListApiArg = {
  titleIds?: number[]
}
export type SelectedTitleExportPdfApiResponse = /** status 200  */ TitlePdfData
export type SelectedTitleExportPdfApiArg = {
  titleIds?: number[]
}
export type SelectedTitleListApiResponse = /** status 200  */ TitleList
export type SelectedTitleListApiArg = {
  titleIds?: number[]
}
export type SelectedTitleMemorialListApiResponse =
  /** status 200  */ LinzTitlesMemorial[]
export type SelectedTitleMemorialListApiArg = {
  titleIds?: number[]
}

export type DistrictValuationRoll = {
  dvrId?: number
  linzTitles?: string
  dipid: string | null
  valRef?: string | null
  tlaId?: string | null
  tlaName?: string | null
  fullAddress?: string | null
  category?: string | null
  valDate?: string | null
  cv?: number | null
  lv?: number | null
  iv?: number | null
  landArea?: number | null
  floorArea?: number | null
  improvement?: string | null
  legalDesc?: string | null
  salesGroup?: string | null
  unitsOfUse?: string | null
  landUse?: string | null
  landUseDesc?: string | null
  landZone?: string | null
  landZoneDesc?: string | null
  deletedDate?: string | null
  updatedDate?: string
  searchVector?: string | null
  titles: number[]
}

export type LinzTitlesMemorial = {
  fid: number
  id: number
  titleNo?: string
  landDistrict?: string | null
  memorialText?: string | null
  current?: string | null
  instrumentNumber?: string | null
  instrumentLodgedDatetime?: string | null
  instrumentType?: string | null
  encumbrancees?: string | null
}

export type TitleFeatureCollection = {
  type?: 'FeatureCollection'
  features: Title[]
}
export type AnzUnionSummary = {
  type?: 'Feature'
  geometry?:
    | {
        type?: 'Point'
        coordinates?: number[]
      }
    | {
        type?: 'LineString'
        coordinates?: number[][]
      }
    | {
        type?: 'Polygon'
        coordinates?: number[][][]
      }
  properties?: {
    contour?: number
    vegetation?: number
    ps?: string
    luc?: string
    geometryArea?: string
  }
}
export type AnzUnionFeatureCollection = {
  type?: 'FeatureCollection'
  features: AnzUnionSummary[]
}
export type AnzUnionAgg = {
  ps: {
    [key: string]: any
  }
  luc: {
    [key: string]: any
  }
  vegetation: {
    [key: string]: any
  }
}
export type ExportSummary = {
  landDescription?: string
  elevation: string
  anzUnion?: AnzUnionAgg
}

export type TitlePdfData = {
  center: string
  documentTitle?: string
  filename: string
  titles: TitleFeatureCollection
  districtValuationRoll?: DistrictValuationRoll[]
  anzUnion?: AnzUnionFeatureCollection
  summary: ExportSummary
}

export const {
  useSearchTitleQuery,
  useTitleListQuery,
  useTitleRetrieveQuery,
  useSelectedTitleDvrListQuery,
  useSelectedTitleExportPdfQuery,
  useSelectedTitleListQuery,
  useSelectedTitleMemorialListQuery,
} = titleApi
