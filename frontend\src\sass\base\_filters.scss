.agrigis-filters {
  display: flex;
  flex-wrap: wrap;

  .control-name {
    display: flex;
    justify-content: space-between;
    text-transform: uppercase;
    color: $grey-5 !important;
    font-size: 12px;
    white-space: nowrap;

    .control-override {
      text-transform: capitalize;
      color: $anz-ocean-blue;
    }
  }

  .ant-select-selection-overflow {
    overflow: hidden;
  }

  .control-container {
    @include row-flex;
    flex: 20%;
    flex-grow: 1;
    margin-bottom: 8px;

    &.smaller {
      flex: 10%;
    }

    &.select {
      flex: 40%;
    }

    &.full {
      flex: 100%;
    }

    &:not(:last-child) {
      margin-right: $quarter;
    }

    &:only-child {
      margin-right: 0;
    }

    span,
    .ant-input,
    ::placeholder {
      font-size: 12px !important;
      color: $grey-4;
    }
  }

  .ant-input-prefix {
    color: $grey-4;
  }

  .ant-input,
  .ant-picker,
  .ant-select {
    height: 30px;

    .ant-input-affix-wrapper,
    .ant-input-prefix,
    div,
    input {
      display: flex;
      height: 30px;
      align-items: center;
      font-size: 12px !important;
    }
  }

  .ant-select-selection-item,
  .ant-select-selection-item-remove {
    display: flex;
    align-items: center;

    svg {
      width: 10px !important;
      height: 10px !important;
      fill: rgba(200, 0, 0, 1);
    }
  }
}

.agrigis-widget-header + .agrigis-filters {
  margin-top: $quarter;
}
