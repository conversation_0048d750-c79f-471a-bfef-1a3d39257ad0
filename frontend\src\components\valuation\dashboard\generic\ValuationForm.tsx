import { SaveOutlined } from '@ant-design/icons'
import { Button, Form, type FormInstance, type FormProps, Input } from 'antd'
import type React from 'react'
import { useEffect } from 'react'
import type { Valuation } from '../../../../types'
import { HighestAndBestUseInput } from '../../../assets/land/HighestAndBestUseInput'

interface ValuationFormProps extends FormProps {
  clone?: boolean
  saving?: boolean
  form: FormInstance<Valuation>
  handleSubmit: (formValues: Valuation) => void
  setFormValues?: React.Dispatch<React.SetStateAction<Valuation>>
}

export const ValuationForm = ({
  clone,
  handleSubmit,
  form,
  saving,
  setFormValues,
  ...props
}: ValuationFormProps) => {
  useEffect(() => {
    form.setFields([{ name: 'valuationName', value: '' }])
  }, [form])

  return (
    <Form
      {...props}
      layout="vertical"
      form={form}
      onValuesChange={(formValues: Valuation) => {
        if (setFormValues) {
          form.setFieldsValue(formValues)
          setFormValues(form.getFieldsValue())
        }
      }}
      onFinish={(formValues: Valuation) => {
        handleSubmit(formValues)
      }}
      className="agrigis-form"
    >
      <Form.Item
        label="Highest & Best Use"
        name="highestAndBestUseTypeId"
        rules={[{ required: true }]}
      >
        <HighestAndBestUseInput />
      </Form.Item>
      <Form.Item
        label="Valuation Name"
        name="valuationName"
        rules={[{ required: true }]}
      >
        <Input />
      </Form.Item>
      <Form.Item>
        <hr />
        <Button
          loading={saving}
          icon={<SaveOutlined />}
          type="primary"
          htmlType="submit"
        >
          {clone ? 'Clone' : 'Save'}
        </Button>
      </Form.Item>
    </Form>
  )
}
