import React from 'react'
import { Helmet } from 'react-helmet'
import ProtectedRoute from '@components/ProtectedRoute'
import { ValuationsDashboardView } from '@components/valuation'

const ValuationsDashboardPage = () => {
  return (
    <>
      <Helmet>
        <title>Valuations Dashboard</title>
      </Helmet>
      <ProtectedRoute requiredEntitlements={['client:propertyflow:*']}>
        <ValuationsDashboardView />
      </ProtectedRoute>
    </>
  )
}

export default ValuationsDashboardPage
