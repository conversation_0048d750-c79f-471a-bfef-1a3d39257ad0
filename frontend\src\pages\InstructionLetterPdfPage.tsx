import CommercialInstructionLetterPdfView from '@components/propertyFlow/externalValuation/instructionLetter/pdf/CommercialInstructionLetterPdfView'
import RuralInstructionLetterPdfView from '@components/propertyFlow/externalValuation/instructionLetter/pdf/RuralInstructionLetterPdfView'
import { Route, Routes } from 'react-router-dom'

const InstructionLetterPdfPage = () => {
  return (
    <Routes>
      <Route path="rural/:id" element={<RuralInstructionLetterPdfView />} />
      <Route
        path="commercial/:id"
        element={<CommercialInstructionLetterPdfView />}
      />
    </Routes>
  )
}

export default InstructionLetterPdfPage
