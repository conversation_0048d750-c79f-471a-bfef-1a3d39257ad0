import { FileTextOutlined } from '@ant-design/icons'
import { Descriptions, Tooltip, Typography } from 'antd'
import React from 'react'
import { renderHighestAndBestUseTypeLabel } from '@models/assets/ValuationTypes'
import type { Valuation } from '@types'
import { formatDollarValue } from '@util'
import { PLACEHOLDER_CHAR } from '@util/const'

export interface ValuationPageInformationTableProps {
  valuation: Valuation
}

export const ValuationPageInformationTable = (
  props: ValuationPageInformationTableProps
) => {
  const { valuation } = props
  const inspection = valuation.inspection

  const inReport = valuation.remarksAndActionsReportInclude

  return (
    <Descriptions bordered column={1} size="small">
      <Descriptions.Item label="Valuation Name">
        {valuation.valuationName}
      </Descriptions.Item>
      <Descriptions.Item label="Highest & Best Use">
        {valuation?.highestAndBestUseType
          ? renderHighestAndBestUseTypeLabel(valuation?.highestAndBestUseType)
          : valuation.farmType}
      </Descriptions.Item>
      <Descriptions.Item label="Tier">{valuation.tier}</Descriptions.Item>
      <Descriptions.Item label="LVR % (Risk Group)">
        {valuation.lvr}
      </Descriptions.Item>
      <Descriptions.Item label="SI">{valuation.si}</Descriptions.Item>
      <Descriptions.Item label="CCR Rating">{valuation.ccr}</Descriptions.Item>
      <Descriptions.Item label="Approach">
        {valuation.approachName}
      </Descriptions.Item>
      <Descriptions.Item label="Creator">
        {valuation.creatorName}
      </Descriptions.Item>
      <Descriptions.Item label="Created Date">
        {valuation.createdDate.toString().slice(0, 10)}
      </Descriptions.Item>
      <Descriptions.Item
        label={valuation.completedDate ? 'Completed Date' : 'Completed'}
      >
        {valuation.completedDate || 'No'}
      </Descriptions.Item>
      {valuation.elevationStep ? (
        <Descriptions.Item label="Elevation step">
          {valuation.elevationStep} masl
        </Descriptions.Item>
      ) : null}
      <Descriptions.Item label="Inspection">
        {inspection?.party || 'Not Required'}
      </Descriptions.Item>
      <Descriptions.Item label="Inspection Date">
        {inspection?.formattedDate || PLACEHOLDER_CHAR}
      </Descriptions.Item>
      <Descriptions.Item label="Inspector Name">
        {inspection?.name || PLACEHOLDER_CHAR}
      </Descriptions.Item>
      <Descriptions.Item
        label={
          <span
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'baseline',
            }}
          >
            <span>Remarks and Actions for CAD Holders</span>
            <Tooltip
              title={`${inReport ? 'Included in' : 'Omitted from'} Report`}
            >
              <FileTextOutlined
                style={{ ...(!inReport && { opacity: 0.3 }) }}
              />
            </Tooltip>
          </span>
        }
      >
        {valuation.remarksAndActions || PLACEHOLDER_CHAR}
      </Descriptions.Item>
      <Descriptions.Item label="Prior Market Value">
        {valuation.priorMarketValue ? (
          <>
            {formatDollarValue(+valuation.priorMarketValue.value)}
            <br />
            <br />
            <Typography.Text type="secondary">Change:</Typography.Text> <br />
            {valuation.priorMarketValue?.changePercent}%
            {valuation.priorMarketValue?.assessmentType && (
              <>
                <br />
                <Typography.Text type="secondary">
                  Assessment Type:
                </Typography.Text>
                <br />
                {valuation.priorMarketValue?.assessmentType}
              </>
            )}
            {valuation.priorMarketValue?.justification && (
              <>
                <br />
                <Typography.Text type="secondary">
                  Justification:
                </Typography.Text>
                <br />
                {valuation.priorMarketValue?.justification}
              </>
            )}
            {valuation.priorMarketValue?.dateOfPriorAssessment && (
              <>
                <br />
                <Typography.Text type="secondary">
                  Date of Prior Assessment:
                </Typography.Text>
                <br />
                {valuation.priorMarketValue?.dateOfPriorAssessment}
              </>
            )}
          </>
        ) : (
          PLACEHOLDER_CHAR
        )}
      </Descriptions.Item>
      {valuation.rvrValuation ? (
        <>
          <Descriptions.Item
            label="RVR Information"
            style={{ fontWeight: 'bold' }}
          >
            &nbsp;
          </Descriptions.Item>
          <Descriptions.Item label="RVR">
            <a href={`/rvr/${valuation.rvrValuation.rvr}/`}>Open</a>
          </Descriptions.Item>
          <Descriptions.Item label="External Valuer">
            {valuation.rvrValuation.externalValuer}
          </Descriptions.Item>
          <Descriptions.Item label="Primary Valuation Approach">
            {valuation.rvrValuation.primaryValuationApproach}
          </Descriptions.Item>
          <Descriptions.Item label="Valuation Date">
            {valuation.rvrValuation.valuationDate}
          </Descriptions.Item>
          <Descriptions.Item label="Valuers Market Value">
            {formatDollarValue(valuation.rvrValuation.rvrMarketValue)}
          </Descriptions.Item>
          <Descriptions.Item label="Valuers AEP">
            {valuation.rvrValuation.valuersAep}{' '}
            {valuation.highestAndBestUseType?.aepUnit ?? ''}
          </Descriptions.Item>
        </>
      ) : null}
    </Descriptions>
  )
}
