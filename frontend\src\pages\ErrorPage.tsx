import { Divider, Typography } from 'antd'
import { Helmet } from 'react-helmet'
import styles from './ErrorPage.module.scss'

const ErrorPage = () => {
  return (
    <div className={styles.container} data-cy="internal-error-alert">
      <Helmet>
        <title>Permission Denied</title>
      </Helmet>
      <div className={styles.error}>
        <Typography className={styles.errorCode}>500</Typography>
        <Typography className={styles.errorType}>
          Internal Server Error
        </Typography>
        <Divider />
        <Typography className={styles.errorDescription}>
          <p>
            Something went wrong while trying to load this page! If this result
            was unexpected, please create an incident by contacting{' '}
            <a href="mailto:<EMAIL>"><EMAIL></a>
          </p>
        </Typography>
      </div>
    </div>
  )
}

export default ErrorPage
