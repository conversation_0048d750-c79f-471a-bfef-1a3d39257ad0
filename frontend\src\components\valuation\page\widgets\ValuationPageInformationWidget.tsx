import { Alert, Tabs } from 'antd'
import type { Feature, GeoJsonProperties, Point } from 'geojson'
import React, { useMemo } from 'react'
import { useSelector } from 'react-redux'
import { RVRAssetSummaryCard } from '@components/assets/RVRAssetSummaryCard'
import { KiwifruitWorksheet } from '@components/assets/kiwifruit/worksheet'
import { ButtonWidget } from '@components/generic'
import { Widget } from '@components/generic/Widget'
import type { RootState } from '@store'
import { uiSelectors } from '@store/ui'
import type { Address, Valuation } from '@types'
import { AssetSummaryCard } from '../../../assets/AssetSummaryCard'
import { ImprovementAssetEditTableCard } from '../../../assets/improvement/ImprovementAssetEditTableCard'
import { LandBreakdownTableCard } from '../../../assets/land/LandBreakdownTableCard'
import { ValuerButtonGroup } from '../controls/ValuerButtonGroup'
import { ValuationPageInformationTable } from './valuation/ValuationPageInformationTable'

interface ValuationPageInformationWidgetProps {
  valuation: Valuation
  address?: Address
  disabled?: boolean
}

// TODO: Use antd editable cells to remove modal

export const ValuationPageInformationWidget = ({
  valuation,
  address,
  disabled,
}: ValuationPageInformationWidgetProps) => {
  const center = useMemo(() => {
    return {
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: address ? [address.lng, address.lat] : [0, 0],
      },
      properties: {},
    } as Feature<Point, GeoJsonProperties>
  }, [address])

  const controls = useMemo(
    () =>
      !disabled ? (
        <ButtonWidget>
          <ValuerButtonGroup valuation={valuation} />
        </ButtonWidget>
      ) : null,
    [disabled, valuation]
  )

  return (
    <Tabs defaultActiveKey="INFO">
      <Tabs.TabPane tab="Information" key="INFO">
        <Widget title="Valuation Information" extra={controls}>
          {!valuation.highestAndBestUseType && (
            <Alert
              type="info"
              message="Please assign a highest and best use in the valuation information tab before breaking down this valuation."
            />
          )}
          <ValuationPageInformationTable valuation={valuation} />
        </Widget>
      </Tabs.TabPane>
      <Tabs.TabPane tab="Land Breakdown" key="LAND_BREAKDOWN">
        <LandBreakdownTableCard valuationId={valuation.valuationId} />
      </Tabs.TabPane>
      <Tabs.TabPane tab="Improvements" key="IMPROVEMENTS">
        <ImprovementAssetEditTableCard valuationId={valuation.valuationId} />
      </Tabs.TabPane>
      <Tabs.TabPane tab="Asset Summary" key="ASSET_SUMMARY">
        {valuation.rvrValuation ? (
          <RVRAssetSummaryCard valuationId={valuation.valuationId} />
        ) : (
          <AssetSummaryCard
            saleId={valuation.saleId}
            valuationId={valuation.valuationId}
            center={center}
          />
        )}
      </Tabs.TabPane>
      {valuation.hasKiwifruitBestUse ? (
        <Tabs.TabPane tab="Kiwifruit Worksheet" key="KIWIFRUIT_WORKSHEET">
          <KiwifruitWorksheet valuationId={valuation.valuationId} />
        </Tabs.TabPane>
      ) : null}
    </Tabs>
  )
}
