import type { Point } from 'geojson'
import { CircleMarker, Tooltip } from 'react-leaflet'
import type { Sale } from '@store/services/sdk'
import { INVISIBLE_CIRCLE_MARKER_PROPS } from '@util/const'
import { integerToAlpha } from '@util/intgerToAlpha'

interface SaleMarkerProps {
  sale: Sale
  state: string
  index: number
  onClick?: (sale: Sale) => void
}

export const SaleMarker = (props: SaleMarkerProps) => {
  const { sale, index, state, onClick } = props

  if (sale.geometry) {
    const [long, lat] = (sale.geometry as Point).coordinates
    return (
      <CircleMarker
        onclick={() => onClick?.(sale)}
        key={sale.id}
        center={[Number(lat), Number(long)]}
        {...INVISIBLE_CIRCLE_MARKER_PROPS}
        radius={10}
      >
        <Tooltip
          className="sale-marker"
          onclick={() => onClick?.(sale)}
          direction="center"
          offset={[0, 0]}
          opacity={1}
          permanent
        >
          <button
            type="button"
            className="label-container"
            onClick={() => onClick?.(sale)}
          >
            <span className={`sales-index-label ${state.toLowerCase()}`}>
              {integerToAlpha(index)}
            </span>
            <span className="gross-sales-price">
              {sale.properties.grossSalesPrice}
            </span>
          </button>
        </Tooltip>
      </CircleMarker>
    )
  }

  return <></>
}
