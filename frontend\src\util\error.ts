import type { SerializedError } from '@reduxjs/toolkit'
import type { FetchBaseQueryError } from '@reduxjs/toolkit/query'
import { startCase } from 'lodash'
import type { ValidateErrorEntity } from 'rc-field-form/lib/interface'
import { isObjectLiteral } from './guards'
import { message } from 'antd'

export type DrfSerializerError = string[]

export type DrfSerializerErrorData = {
  [field: 'nonFieldErrors' | string]: string[]
}

export function getErrorMessage(error: unknown): string {
  return error instanceof Error ? error.message : String(error)
}

export const hasData = <T extends { data: Record<string, unknown> | string[] }>(
  error: unknown
): error is T =>
  isObjectLiteral(error) &&
  (isObjectLiteral(error.data) || Array.isArray(error.data))

export const isPrimitive = (value: unknown): value is number | string =>
  ['number', 'string'].includes(typeof value)

const formatErrorData = (data: Record<string, unknown> | string[]) => {
  if (Array.isArray(data)) return data.join(', ')

  return Object.entries(data).reduce<string>((message, [field, errors]) => {
    if (errors == null) return message
    const key = ['detail', 'nonFieldErrors'].includes(field)
      ? ''
      : `${startCase(field)}: `
    let value = 'Unknown Error'
    if (Array.isArray(errors)) {
      value = errors.join(', ')
    } else if (isPrimitive(errors)) {
      value = errors.toString()
    } else if (isObjectLiteral(errors)) {
      value = formatErrorData(errors)
    }
    return `${message + key + value} \n`
  }, '')
}

function getUnknownErrorMessage(status: string | number) {
  return `Unknown Error: Status ${status}`
}

export function getQueryErrorMessage(error: unknown): string {
  if (isFetchBaseQueryError(error)) {
    if (
      error?.status === 'PARSING_ERROR' &&
      typeof error?.originalStatus === 'number'
    ) {
      return getUnknownErrorMessage(error.originalStatus)
    }
    if ('error' in error) {
      return error.error
    }
    if (hasData(error)) {
      return formatErrorData(error.data)
    }
    return getUnknownErrorMessage(error.status)
  }
  return getErrorMessage(error)
}

export function isFetchBaseQueryError(
  error: unknown
): error is FetchBaseQueryError {
  return typeof error === 'object' && error != null && 'status' in error
}

/**
 * @deprecated Use getQueryErrorMessage in cases where this might be needed
 */
export function isValidateErrorEntity<T>(
  error: unknown
): error is ValidateErrorEntity<T> {
  return typeof error === 'object' && error != null && 'errorFields' in error
}

export function createErrorMessage(error: unknown, message = '') {
  if (error == null) return message
  if (typeof error === 'object') {
    const objMessages = new Set()
    for (const key in error) {
      let lastMessage = message
      if (Number.isNaN(Number(key))) lastMessage += ` ${key} →`
      objMessages.add(
        createErrorMessage(error[key as keyof typeof error], lastMessage)
      )
    }
    return [...objMessages].join('\n')
  }
  const lastMessage = message.replace(/\s→$/, ': ')
  return `${lastMessage}${error}`
}

export function getErrorMessageText(error: unknown): string {
  if (!isFetchBaseQueryError(error)) return getErrorMessage(error)
  if (
    !(
      error?.status === 'PARSING_ERROR' &&
      typeof error?.originalStatus === 'number'
    )
  ) {
    if ('error' in error) return error.error
    if (hasData(error)) return formatErrorData(error.data)
  }

  return getUnknownErrorMessage(error.status)
}

export function flashApiError(err: unknown) {
  const errorMessage = createErrorMessage(err)
  message.error(errorMessage, 6)
}

export function flashResult<
  T extends
    | { data: unknown }
    | { error: FetchBaseQueryError | SerializedError },
>(result: T, options: { success?: string } = {}) {
  if ('error' in result) {
    flashApiError(result.error)
    return
  }
  message.success('Report Completed')
  console.info('result', options.success || 'Success')
}
