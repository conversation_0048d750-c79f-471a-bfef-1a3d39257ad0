import React, { useMemo } from 'react'
import { Line } from 'react-chartjs-2'
import { useGetValuationRainfallQuery } from '@store/services/valuations'
import { titleCase } from '../../../../../util'
import { ValuationPageRainfallTable } from './rainfall/ValuationPageRainfallTable'

interface ValuationPageRainfallWidgetProps {
  addressId: string
  valuationId: string
}

export const ValuationPageRainfallWidget = (
  props: ValuationPageRainfallWidgetProps
) => {
  const { valuationId } = props

  const { data: valuationRainfall } = useGetValuationRainfallQuery(valuationId)

  const rainfallGraphData = useMemo(() => {
    if (!valuationRainfall?.[0]) {
      return undefined
    }
    const rainfall = valuationRainfall[0]
    return {
      labels: Object.keys(rainfall).map((x) => titleCase(x)),
      datasets: [
        {
          data: Object.values(rainfall).map((x) => x * 25),
          backgroundColor: 'rgba(0, 0, 0, 0)',
          borderColor: 'rgba(0, 125, 186, 0.5)',
          pointBackgroundColor: 'rgba(0, 125, 186, 1)',
        },
      ],
    }
  }, [valuationRainfall])

  return (
    <div>
      <h2>Rainfall</h2>
      <div className="agrigis-graph" style={{ marginTop: '20px' }}>
        {rainfallGraphData !== undefined && (
          <Line
            data={rainfallGraphData}
            options={{
              legend: { display: false },
              scales: {
                yAxes: [
                  {
                    ticks: {
                      beginAtZero: true,
                    },
                  },
                ],
              },
            }}
          />
        )}
      </div>
      <div className="agrigis-table">
        {!!valuationRainfall?.length && (
          <ValuationPageRainfallTable valuationRainfall={valuationRainfall} />
        )}
      </div>
    </div>
  )
}
