$primary-font-size: 12px;
$header-font-size: 16px;
$a4-width: 210mm+30mm;
$a4-height: 297mm+30mm;
$map-height: 20cm;

@page {
  margin: 5mm 15mm 15mm 5mm;
  width: $a4-width;
  height: $a4-height;
}

@media print {
  .agrigis-export-control-overlay {
    display: none;
  }

  .agrigis-export-wrapper,
  body {
    width: $a4-width;
    height: $a4-height;
  }

  .agrigis-page[data-type='export'] {
    width: 100vw;
    height: 100vh;

    &[data-variant='cover'] {
      height: 100vh;

      .agrigis-cover-page-wrapper {
        margin-bottom: -11mm;
      }
    }
  }

  .agrigis-export-map {
    display: block;
  }
}

@media screen {
  .agrigis-export-control-overlay {
    position: absolute;
    left: 2em;
    top: 1em;
    z-index: 10200;
  }

  .agrigis-export-control-widget {
    padding: 1em;
    background-color: white;
    margin: 1em 0;
    outline: 1px solid #bbb;

    .agrigis-export-control-widget-title {
      font-size: 13px;
      font-weight: 500;
      margin-bottom: 1em;
      display: flex;
      color: #004168;
    }

    .agrigis-export-control-widget-list {
      font-size: 11px;

      .agrigis-export-control-widget-list-item {
        cursor: pointer;
        padding: 0.25em 0.5em;
        transition: color 0.25s, text-decoration 0.25s, background-color 0.5s;
        outline: 1px solid #ddd;

        &:hover {
          background-color: rgba(235, 235, 235, 0.9);
          transition: background-color 0.25s;
        }

        &[data-selected='false'] {
          text-decoration: line-through;
          color: #999;
        }

        &:not(:last-child) {
          margin-bottom: 0.5em;
        }
      }
    }
  }

  .agrigis-export-wrapper {
    background-color: #ccc;
    padding-top: 0.5cm;
    zoom: 50%;
  }

  .agrigis-page[data-type='export'] {
    width: $a4-width + 60mm;
    height: $a4-height + 80mm;
    margin: 0 auto;
    margin-bottom: 0.5cm;
    padding: 15mm 20mm 20mm 15mm !important;
    box-shadow: 0 0 0.5cm rgba(0, 0, 0, 0.5);
    background: white;

    &[data-variant='map'] {
      display: flex !important;
      flex-direction: column;
      align-content: flex-start;
    }
  }

  .agrigis-export-map {
    overflow-y: hidden;
  }
}

.agrigis-page[data-type='export'] {
  page-break-before: always;
  page-break-inside: avoid;
  display: block;

  &[data-variant='legal-disclaimers'] {
    .agrigis-export-disclaimer-text {
      margin-bottom: 0.5cm;
      font-size: 12px;
      color: #888;
      flex-grow: 0;
    }

    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }

  .agrigis-export-paragraph {
    font-size: $primary-font-size;

    p:last-child {
      margin-bottom: 0;
    }

    li {
      padding-left: 1em;

      &:before {
        content: '-';
        margin-right: 1em;
      }
    }
  }

  .agrigis-export-graph-sider {
    min-width: 5cm;
    margin-left: 1cm;

    .ant-statistic {
      margin-bottom: 0.5cm;

      .ant-statistic-title,
      .ant-statistic-content,
      .ant-statistic-content-value {
        margin: 0;
        padding: 0;
        line-height: 1.25;
      }

      .ant-statistic-title {
        font-size: $primary-font-size;
      }

      .ant-statistic-content {
        color: #007dba;
        font-size: $header-font-size;
      }
    }
  }

  .agrigis-export-map-legend {
    margin-top: 0.5cm;
    display: flex;

    .agrigis-export-map-legend-item {
      display: flex;
      max-width: 10cm;
      align-items: center;
      margin-bottom: 0.25cm;

      &:not(:last-child) {
        margin-right: 0.5cm;
      }

      .agrigis-export-map-legend-icon {
        width: 24px;
        height: 24px;
        margin-right: 0.25cm;
      }

      .agrigis-export-map-legend-label {
        font-size: 12px;
      }
    }
  }

  .agrigis-export-map-attributions {
    margin-top: 0.5cm;

    p {
      margin-bottom: 0;
    }

    .agrigis-export-map-attributions-item {
      color: #999;
      font-size: $primary-font-size;
    }
  }

  &[data-variant='cover'] {
    background-image: linear-gradient($primary, $secondary);
    display: flex;
    justify-content: flex-end;
    align-self: center;
    padding: 0 !important;

    .agrigis-cover-page-wrapper {
      // background-image: url('../img/anz-cover-corner-logo.png');
      background-repeat: no-repeat;
      background-position: 100% 100%;
      padding: 5mm 15mm 15mm 5mm !important;
      height: $a4-height + 80mm;
      color: white !important;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-self: center;
      align-items: flex-end;

      .agrigis-cover-page-content {
        text-align: right;
        margin-bottom: 0.5cm;
      }

      .agrigis-cover-page-date {
        margin-bottom: 2cm;
        font-size: 24px;
        font-weight: 300;
      }

      .agrigis-cover-page-title {
        margin-bottom: 1cm;
        font-weight: 300;
        font-size: 32px;
      }
    }
  }

  &:not([data-variant='cover']) {
    padding: 1cm;
  }

  .agrigis-export-table {
    th {
      background-color: transparent;
    }

    .agrigis-export-table-not-defined {
      font-style: italic;
    }
  }

  .agrigis-export-section {
    padding: 0.5cm 0 0.5cm 0;
    border-bottom: 1px solid #ccc;
    display: block;
    flex-grow: 0;

    ul {
      margin-bottom: 0;
      padding-bottom: 0;
    }

    &:not(:last-child) {
      margin-bottom: 1cm;
    }

    .agrigis-export-section-content {
      &.flex {
        display: flex;
        width: 100%;

        .agrigis-export-graph {
          flex: 50%;

          &:not(:last-child) {
            padding-right: 2cm;
          }
        }
      }

      .agrigis-export-section-inner {
        margin-top: 1cm;

        &.flex {
          display: flex;

          > div {
            flex: 50%;

            &:first-child {
              margin-right: 1cm;
            }
          }
        }
      }
    }

    .agrigis-export-section-title {
      font-size: $header-font-size;
      border-bottom: 1px solid #ccc;
      padding-bottom: 0.25cm;
      margin-bottom: 0.25cm;
      color: #007dba;
      font-weight: 600;
    }
  }

  .agrigis-export-map {
    cursor: default !important;
    width: 100%;
    height: $map-height;
    margin-top: 0.5cm;

    &.half {
      height: calc(#{$map-height} / 1.4);
    }

    &.full {
      height: calc(#{$map-height} * 1.6);
    }

    * {
      cursor: default !important;
    }

    .leaflet-control-zoom {
      display: none;
    }

    .leaflet-tooltip {
      background: none;
      border: none;
      box-shadow: none;
      @include stroke(1, yellow);
      font-weight: bold;
    }
  }

  .agrigis-export-details {
    font-size: $primary-font-size;

    &:not([data-type='memorials']) {
      .agrigis-export-details-item-value {
        max-width: 10cm;
      }
    }

    &:not(:first-child) {
      border-left: 1px solid #ccc;
      margin-left: 1cm;
      padding-left: 1cm;
    }

    &[data-type='financials'] {
      .agrigis-export-details-title {
        font-style: italic;
      }

      .agrigis-export-details-content {
        padding-left: 0.5cm;
      }

      .agrigis-export-details-item {
        &[data-type='placeholder'] {
          .agrigis-export-details-item-label {
            opacity: 0;
          }
        }

        &[data-type='total'],
        &[data-type='subtotal'],
        &[data-type='number'] {
          .agrigis-export-details-item-value {
            &:before {
              content: '$ ';
            }
          }
        }

        &[data-type='total'] {
          .agrigis-export-details-item-label {
            padding-left: 0.5cm;
            font-weight: bold;
          }

          .agrigis-export-details-item-value {
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
          }
        }
      }
    }

    .agrigis-export-details-item {
      display: flex;
      margin: 1mm 0;

      &[data-variant='warning'] {
        color: $reject-color;
        font-style: italic;
      }

      .agrigis-export-details-item-label {
        min-width: 5cm;
      }
    }
  }
}
