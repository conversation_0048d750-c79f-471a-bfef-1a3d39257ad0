import type { LatLngLiteral } from 'leaflet'
import type { DEFAULT_STYLES } from '@util/layers'
import type { LayerType } from '../../../models/gis/LayerType'
import type { GeoFeature, GeoFeatureMap } from '../../../types'

export type LayerContext = 'address' | 'sale'
export type LayerStyles = typeof DEFAULT_STYLES

export type ExplorerLayer =
  | 'addresses'
  | 'anzUnion'
  | 'consents'
  | 'sales'
  | 'listings'
  | 'titles'
  | 'valocitySales'
  | 'valocityListings'

export type ExplorerTileLayer = string

export interface MapState {
  baseLayer: ExplorerTileLayer
  bounds: string | undefined
  center: LatLngLiteral
  layersLoading: number
  menuFeatureId: number | undefined
  menuPosition: { x: number; y: number }
  menuType: 'address' | 'sale' | undefined
  zoom: number

  layerSelection: {
    byAsset?: {
      type: string
      id: string
    }
    // layer type, e.g. union, smap, luc, titles, ...
    layerType: LayerType
    // selected field to view for given layer, e.g. for union layer we could want to see vegetation, luc, etc.
    selectedField: string
    filter: {
      enable: boolean
      overlayFeature?: GeoFeature
      boundingFeature?: GeoFeature
    }
  }
  elevationLayer: {
    current: {
      minElevation: number
      maxElevation: number
    }
    filter: {
      minElevation: number
      maxElevation: number
      elevationStep: number
    }
  }
  measurementLayer: {
    geoFeatures: GeoFeatureMap
  }
  enableLayerClicking: boolean
  enableByAssetFilters: boolean
  enableTitleOutline: boolean
  transparentLayers: boolean
  mapCentre: [number, number] | undefined
}

export interface UpdateLayerSelectionActionPayload {
  layerType?: LayerType
  selectedField?: string
  overlayFeature?: GeoFeature
  boundingFeature?: GeoFeature
  byAsset?: {
    type: string
    id: string
  }
  clearPreviousFilter?: boolean | undefined
}
