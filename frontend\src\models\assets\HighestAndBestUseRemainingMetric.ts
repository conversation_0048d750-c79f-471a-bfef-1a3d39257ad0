import { fromBigNumber, toBigNumber } from '@types'

export interface SerializableHighestAndBestUseRemainingMetric {
  id?: number
  highestAndBestUseType: number

  // this sort of makes the name of this wrong, but it is just the commodity price used to calculate TFI (and then PV ratio) for this H&BS
  // stored here because this is the only model that is held against the H&BS type rather than particular assets.
  commodityPrice: string

  // calculated on backend, and only on frontend during editing mode
  /*
    dollarPerHectare: number;
    totalDollarPerHectare: number;
    totalArea: number;
    total_LWB: number;

    total_AEP: 0;
    effectiveArea: number;
    ineffectiveArea: number;
    */
}

export function deserializeHighestAndBestUseRemainingMetric(
  remainingMetric: SerializableHighestAndBestUseRemainingMetric
) {
  const { commodityPrice, ...rest } = remainingMetric
  return {
    ...rest,
    ...toBigNumber({
      commodityPrice,
    }),
  }
}

export type HighestAndBestUseRemainingMetric = ReturnType<
  typeof deserializeHighestAndBestUseRemainingMetric
>

export type HighestBestUseRemainingMap = {
  [highestAndBestUseTypeId: string]: HighestAndBestUseRemainingMetric
}

export function serializeHighestAndBestUseRemainingMetric(
  remainingMetric: HighestAndBestUseRemainingMetric
) {
  const { commodityPrice, ...rest } = remainingMetric
  return {
    ...rest,
    ...fromBigNumber({
      commodityPrice,
    }),
  }
}
