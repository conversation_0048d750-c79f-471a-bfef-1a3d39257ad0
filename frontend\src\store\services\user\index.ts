import { SavedSettings } from '../../ui/types'
import { baseApi } from '../baseApi'

export const userApi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    login: build.mutation<void, { username: string; password: string }>({
      query: (body) => {
        return {
          url: '/api/auth/login/',
          method: 'POST',
          body,
        }
      },
      invalidatesTags: ['User'],
    }),
    logout: build.mutation<void, void>({
      query: () => {
        return {
          url: '/api/auth/logout/',
          method: 'POST',
        }
      },
      invalidatesTags: ['User'],
      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        await queryFulfilled
        dispatch(userApi.util.resetApiState())
      },
    }),
  }),
})

export const { useLogoutMutation, useLoginMutation } = userApi
