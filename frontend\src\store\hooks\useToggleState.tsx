import { useCallback, useState } from 'react'

export interface ToggleState<T> {
  [id: string]: T
}

/**
 * @deprecated See useFeatureToggleState
 */
export const useToggleState = <T,>(
  idKey: string
): [
  ToggleState<T>,
  (feature: T) => void,
  (features: T[]) => void,
  () => void,
] => {
  const [state, setState] = useState<ToggleState<T>>({})

  const toggleItem = useCallback(
    (feature: T) => {
      setState((prev: ToggleState<T>) => {
        // biome-ignore lint: Deprecated
        const featureId = (feature as any)[idKey].toString()
        if (!featureId) {
          return prev
        }
        const newState = { ...prev }
        if (newState[featureId]) {
          delete newState[featureId]
        } else {
          newState[featureId] = feature
        }
        return newState
      })
    },
    [idKey]
  )

  const bulkToggle = useCallback(
    (features: T[]) => {
      setState((prev: ToggleState<T>) => {
        const newState = { ...prev }

        const inputFeatures = new Set([
          // biome-ignore lint: Deprecated
          ...features.map((x) => (x as any)[idKey]?.toString()),
        ])
        // Take all current selected values and check existing state for deleted items.
        // biome-ignore lint: deprecated
        Object.keys(newState).forEach((key) => {
          if (inputFeatures.has(key)) {
            delete newState[key]
          }
        })

        // Then loop through and add all the new features
        // biome-ignore lint: deprecated
        features.forEach((feature) => {
          // biome-ignore lint: Deprecated
          const featureId = (feature as any)[idKey].toString()
          if (featureId && !newState[featureId]) {
            newState[featureId] = feature
          }
        })

        return newState
      })
    },
    [idKey]
  )

  const clearState = useCallback(() => {
    setState({})
  }, [])

  return [state, toggleItem, bulkToggle, clearState]
}
