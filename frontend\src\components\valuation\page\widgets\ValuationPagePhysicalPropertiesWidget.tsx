import React from 'react'
import { Widget } from '../../../generic/Widget'
import { PhysicalPropertiesGraphPane } from '../../../physicalProperties/PhysicalPropertiesGraphPane'

export interface ValuationPagePhysicalPropertiesWidgetProps {
  valuationId: string
}

export const ValuationPagePhysicalPropertiesWidget = ({
  valuationId,
}: ValuationPagePhysicalPropertiesWidgetProps) => {
  // TODO: Delete agrigis-react\src\components\physicalProperties\PhysicalPropertiesCard.tsx

  return (
    <Widget title="Physical Properties">
      <PhysicalPropertiesGraphPane valuationId={valuationId} />
    </Widget>
  )
}
