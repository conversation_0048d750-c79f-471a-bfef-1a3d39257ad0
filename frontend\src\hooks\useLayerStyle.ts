import { useCallback } from 'react'
import { shallowEqual, useDispatch } from 'react-redux'
import { useSelector } from '@store'
import type { LayerContext, LayerStyles } from '@store/features/map/types'
import { uiSelectors } from '@store/ui'
import { setLayerStyles } from '@store/ui/actions'

function useLayerStyle(context: LayerContext, id: number) {
  const dispatch = useDispatch()

  const layerStyle = useSelector(
    (state) => uiSelectors.getSelectedLayerStyles(state, context, id),
    shallowEqual
  )

  const update = useCallback(
    (updates: LayerStyles) => {
      dispatch(setLayerStyles({ context, id, updates }))
    },
    [dispatch, context, id]
  )

  return { layerStyle, update }
}

export default useLayerStyle
