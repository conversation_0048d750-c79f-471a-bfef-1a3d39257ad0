from django.contrib import admin
from django.urls import path
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView

from .api import api
# from .bff_api import bff_api  # Commented out - BFF not required

urlpatterns = [
    path('admin/', admin.site.urls),
    path("api/", api.urls),
    # path("bff/", bff_api.urls),  # Commented out - BFF not required, frontend connects directly to riskradar
    path('', TemplateView.as_view(template_name='index.html'), name='frontend'),  # Serve frontend
]

if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
