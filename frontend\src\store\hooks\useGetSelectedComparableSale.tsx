import { useGetComparableSalesQuery } from '@store/services/assets'

export const useGetSelectedComparableSale = (
  valuationId: string,
  selectedComparableSaleId: number | undefined
) => {
  const { selectedComparableSale } = useGetComparableSalesQuery(valuationId, {
    selectFromResult: ({ data }) => {
      return {
        selectedComparableSale: data?.find(
          (comparableSale) => comparableSale.id === selectedComparableSaleId
        ),
      }
    },
  })

  return selectedComparableSale
}
