import {
  FinanceAgriUpliftCreateApiArg,
  FinanceAgriUpliftCreateApiResponse,
  FinanceAgriUpliftUpdateApiArg,
  FinanceAgriUpliftUpdateApiResponse,
  enhancedApi,
} from './codegen'
import { createMultipartRefQuery } from '../helpers'

const sdk = enhancedApi.injectEndpoints({
  endpoints: (build) => ({
    agriUpliftMultipartCreate: build.mutation<
      FinanceAgriUpliftCreateApiResponse,
      FinanceAgriUpliftCreateApiArg['agriUplift']
    >({
      query: createMultipartRefQuery('/api/finance/agri-uplift/', 'POST'),
      invalidatesTags: ['agri-uplift'],
    }),
    agriUpliftMultipartPartialUpdate: build.mutation<
      FinanceAgriUpliftUpdateApiResponse,
      FinanceAgriUpliftUpdateApiArg['agriUplift']
    >({
      query: createMultipartRefQuery(
        (arg) => `/api/finance/agri-uplift/${arg.pk}/`,
        'PUT'
      ),
      invalidatesTags: (_, error, { pk }) => {
        return error ? [] : [{ type: 'agri-uplift', pk }]
      },
    }),
  }),
})

export default sdk
