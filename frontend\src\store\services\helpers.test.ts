import { buildFileRefFormData } from './helpers'

/**
 * taken from @testing-library/user-event (already a dependency, but not exported)
 */
export function createFileList(
  window: Window & typeof globalThis,
  files: File[]
): FileList {
  const list: FileList & Iterable<File> = {
    ...files,
    length: files.length,
    item: (index: number) => list[index],
    [Symbol.iterator]: function* nextFile(): Generator<
      File,
      undefined,
      unknown
    > {
      for (let i = 0; i < list.length; i++) {
        yield list[i]
      }
    },
  }
  list.constructor = window.FileList

  // guard for environments without FileList
  /* istanbul ignore else */
  if (window.FileList) {
    Object.setPrototypeOf(list, window.FileList.prototype)
  }

  Object.freeze(list)

  return list
}

describe('buildFileRefFormData', () => {
  it('should create FormData with nested files and replace them with UUIDs', () => {
    const file = new File(['content'], 'test.txt', { type: 'text/plain' })
    const files = [
      new File(['content'], 'bobulus.txt', { type: 'text/plain' }),
      new File(['content'], 'abacus.txt', { type: 'text/plain' }),
    ]
    const fileList = createFileList(window, files)
    const value = {
      id: 1010,
      notNested: file,
      nested: {
        blah: 100,
        bloo: {
          id: 1,
        },
        files: fileList,
      },
      very: {
        id: 1,
        very: {
          id: 2,
          very: {
            id: 3,
            nested: {
              files: file,
            },
          },
        },
        arrayNested: [
          {
            id: 'foo',
            file,
          },
          {
            id: 'bar',
            file,
          },
        ],
      },
      arrayNested: [
        {
          id: 'foo',
          file,
        },
        {
          id: 'bar',
          file,
        },
      ],
    }

    const formData = buildFileRefFormData(value)

    expect(formData.has('data')).toBe(true)
    const data = JSON.parse(formData.get('data') as string)
    expect(typeof data.notNested).toBe('string')
    expect(typeof data.nested.files).toBe('string')
    expect(typeof data.arrayNested[0].file).toBe('string')
    expect(typeof data.arrayNested[1].file).toBe('string')
    expect(typeof data.very.arrayNested[0].file).toBe('string')
    expect(typeof data.very.arrayNested[1].file).toBe('string')

    expect(formData.get(data.notNested)).toBe(file)
    const outputFiles = formData.getAll(data.nested.files)
    for (let i = 0; i < outputFiles.length; i++) {
      expect(outputFiles[i]).toBe(files[i])
    }
    expect(formData.get(data.very.very.very.nested.files)).toBe(file)
    expect(formData.get(data.very.arrayNested[0].file)).toBe(file)
    expect(formData.get(data.very.arrayNested[1].file)).toBe(file)
    expect(formData.get(data.arrayNested[0].file)).toBe(file)
    expect(formData.get(data.arrayNested[1].file)).toBe(file)

    expect(data.id).toBe(1010)
    expect(data.nested.blah).toBe(100)
    expect(data.nested.bloo.id).toBe(1)
    expect(data.very.id).toBe(1)
    expect(data.very.very.id).toBe(2)
    expect(data.very.very.very.id).toBe(3)
    expect(data.arrayNested[0].id).toBe('foo')
    expect(data.arrayNested[1].id).toBe('bar')
  })
})
