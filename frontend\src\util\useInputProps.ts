import { Form, type FormItemProps } from 'antd'
import type { Rule } from 'antd/lib/form'
import { isEqual } from 'lodash'
import type { FormTemplate, SelectOption } from '@store/services/sdk'
import { shouldSkipValidation } from './customInputValidation'

export const useInputProps = (
  fieldName: string[],
  formTemplate: FormTemplate | undefined
): [FormItemProps, string | undefined, SelectOption[] | undefined] => {
  const form = Form.useFormInstance()
  if (formTemplate) {
    const { fields } = formTemplate
    const field = fields.find(({ name }) => {
      return isEqual(name, fieldName)
    })
    if (field) {
      const { addonAfter, options, conditionalRules, ...itemProps } = field

      if (conditionalRules?.noValidationWhen) {
        if (shouldSkipValidation(conditionalRules.noValidationWhen, form)) {
          itemProps.rules = (itemProps.rules || []).filter(
            (rule) => !rule.required
          )
        }
      }

      return [itemProps, addonAfter, options]
    }
  }

  return [{ hidden: true }, undefined, []]
}
