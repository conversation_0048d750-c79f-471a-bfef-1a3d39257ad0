import type { Feature, FeatureCollection, MultiPolygon } from 'geojson'

export interface TerritorialAuthorityProperties {
  id: string
  name: string
  code: number
  abbrev: string
}

export type TerritorialAuthorityFeature = Feature<
  MultiPolygon,
  TerritorialAuthorityProperties
>

export interface TerritorialAuthorityFeatureCollection
  extends FeatureCollection {
  features: Array<TerritorialAuthorityFeature>
}
