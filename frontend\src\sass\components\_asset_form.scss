.asset-form-container {
  padding: 15px;
  margin: 15px;
  border: 0.1rem dashed #b9b9b9;

  .ant-row {
    margin-bottom: 10px;
  }

  legend {
    content: attr(title);
    white-space: pre;
    flex: 100%;
    text-transform: uppercase !important;
    font-size: 14px;
    letter-spacing: 1px;
    color: $primary;
    margin-left: 8px;
    margin-bottom: 0px;
    width: fit-content;
  }
}

.highest-best-use-form {
  display: flex;
  flex-direction: column;
  align-content: space-between;

  .ant-form-item-has-error.ant-form-item-has-feedback
    .ant-form-item-children-icon {
    display: none;
  }

  .ant-form-item-control:nth-child(1) {
    align-items: flex-end !important;
    align-self: flex-end !important;
  }
}

td > .sub-land-asset-table {
  padding: 0;
  margin: 0;
  border: 2px solid #cacaca;
}

.asset-summary-table {
  width: 100%;
  min-width: 800px;
  // max-width: 810px;

  tr:not(:last-child) {
    border-bottom: 0.01rem solid $grey-3;
  }

  th {
    background-color: $grey-1;
    background-blend-mode: soft-light;
    min-width: 80px;
  }

  .ant-descriptions-item-content {
    min-width: 80px;
  }

  .ant-descriptions-item-label {
    padding: 8px 16px !important;
    color: #004165;
  }

  .ant-descriptions-item-label {
    background-color: #fafafa95;
  }

  .summary-row {
    padding: 0px !important;
  }

  .metric-label {
    display: flex;
    flex-direction: column;

    .metric-title {
      font-weight: 500;
      padding-bottom: 10px;
    }

    .metric-detail-title {
      padding-right: 5px;
    }
  }

  .warning-label {
    color: red;
    font-weight: 500;
  }

  &.has-subject-best-use {
    > tbody > tr.subject-best-use-row {
      border: 0.5px solid $primary;
    }
  }
}

.ExportSection {
  .metric-label:not(.otherImprovement) {
    .improvementCondition,
    .improvementDateBuilt,
    .improvementComments {
      display: none;
    }
  }
}

.asset-summary-table .ant-descriptions-row {
  border-bottom: 2px solid #bbb;
}

.ant-col.ant-form-item-label {
  margin-bottom: 0;
  padding-bottom: 0;
}

.input-label.required {
  font-weight: 600 !important;
  color: $grey-6 !important;
}

.ant-form-item:not(.unstyle) .ant-col.ant-form-item-label label {
  text-transform: uppercase;
  font-size: 11px;
  color: $grey-4;

  &.required {
    &::after {
      content: " *";
      color: red;
      font-weight: 600;
    }
  }

  &::after {
    content: "\A";
    white-space: pre;
  }
}

.asset-value-container {
  @include row-flex;
  align-items: space-around;
  width: 100%;

  &:not(:last-child) {
    margin-right: $quarter;
  }

  &:only-child {
    margin-right: 0;
  }

  span,
  .ant-input,
  ::placeholder {
    font-size: 12px !important;
  }

  .value-header {
    text-transform: uppercase;
    color: $grey-3;
    font-size: 12px;

    &::after {
      content: "\A";
      white-space: pre;
    }
  }
}

.asset-form {
  .metric-data-input {
    display: flex;
    margin: 10px;

    .ant-row {
      flex-grow: 1;
    }
  }

  .metric-data-close-button {
    font-size: 5px;
    position: relative;

    .ant-btn {
      position: absolute;
      top: -5px;
      right: -25px;
    }
  }
}

.asset-comments {
  margin: 10px;
  white-space: pre-line;
  padding-right: 10px;
}

.metric-label {
  display: flex;
  flex-direction: column;

  .metric-field {
    padding: 2px;
  }

  .metric-field-title {
    padding-right: 0.2rem;
    color: $grey-6;
  }
}

.best-use-table {
  display: flex;
  flex-direction: row;

  .best-use {
    vertical-align: center;
    font-weight: 700;
  }
}

.metric-table {
  margin-top: -1px;
  margin-left: -1px;
  margin-bottom: 10px;
  display: table;
  width: 100%;
  table-layout: auto;

  thead > th {
    background-color: $grey-1;
    background-blend-mode: soft-light;
  }

  td {
    text-align: right;
    border-right: 0.01rem solid $grey-1;
  }

  tbody th {
    background-color: unset;
    border-right: 0.01rem solid $grey-1;
  }

  tr:not(:last-child) {
    border-bottom: 0.01rem solid $grey-2;
  }

  tbody {
    width: 100%;
  }

  tbody tr {
    width: 100%;
  }

  .separator-row {
    height: 10px;
  }

  .expandable-row-cell {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  .expand-arrow {
    height: 100%;
    margin-right: 5px;
    align-self: center;
    border-right: 0.01rem solid $grey-1;
  }
}

.comparison-metric-table {
  table-layout: fixed;
}

th.comparison-best-use-header {
  background-blend-mode: soft-light;
  min-width: 130px;
  max-width: 130px;
}

.totals-summary-row {
  border-top: 0.01rem solid $grey-2;
}

.adjusted-value {
  display: flex;
  flex-direction: column;

  .adjusted {
    font-weight: 550;
    font-style: italic;
  }

  .original {
    color: rgba(120, 120, 120, 0.8);
  }
}

.improvements-summary-table {
  margin-top: 20px;

  background-color: rgba(255, 246, 143, 0.2);
  background-blend-mode: soft-light;
}

.totals-summary-table {
  display: table;

  th {
    background-color: rgba(50, 50, 50, 0.1);
  }
}

.asset-summary-pane {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 50px;
}

.adjustment-summary {
  display: grid;
  grid-template-columns: auto auto;
  grid-template-rows: auto auto;

  .lwb-label {
    text-align: center;
    grid-area: 1 / 1;
  }

  .lwb-value {
    text-align: left;
    grid-area: 1 / 2;
  }

  .total-label {
    text-align: center;
    grid-area: 2 / 1;
  }

  .total-value {
    text-align: left;
    grid-area: 2 / 2;
  }
}

.labelled-cell-value {
  display: flex;
  justify-content: space-between;
  gap: 5px;
}

.comments-header {
  display: flex;
  flex-direction: row;
  gap: 10px;
  font-weight: 500;
}

.valuation-description {
  white-space: pre-wrap;
}

.valuation-description-select {
  width: 100%;

  .ant-select-selection-item {
    width: 0;
  }
}
