import '@testing-library/jest-dom'
import 'isomorphic-fetch'
import 'jest-expect-message'
import LocalStorage from './mocks/localstorage'

const globalProxy: typeof globalThis & { _localStorage?: unknown } = globalThis

Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // Deprecated
    removeListener: jest.fn(), // Deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

jest.mock('modern-screenshot', () => ({
  domToForeignObjectSvg: () => null,
  domToJpeg: () => null,
}))

jest.mock('./workermock.worker.ts')

if (globalProxy._localStorage !== 'undefined') {
  Object.defineProperty(globalProxy, '_localStorage', {
    value: new LocalStorage(jest),
    writable: false,
  })
  globalProxy.localStorage = globalProxy._localStorage as Storage
}
