import type { SkipToken } from '@reduxjs/toolkit/dist/query'
import type { Feature, FeatureCollection, Geometry, Point } from 'geojson'
import { useMemo } from 'react'
import { shallowEqual, useSelector } from 'react-redux'
import type { PropertyArea } from '@models/PropertyArea'
import type { AssetUpdate } from '@models/assets/AssetUpdate'
import type { LandAsset } from '@models/assets/LandAsset'
import type { PlantingAsset } from '@models/assets/PlantingAsset'
import { formatArea, getPropertyValue, toOption } from '@util'
import { createLegendWithColors } from '@util/colors'
import { assetsSelectors } from '.'
import type { RootState } from '../..'
import type { Asset, AssetType } from '../../../models/assets/Asset'
import type { AssetsFeatureKey } from '../../../models/assets/Assets'
import type {
  HighestAndBestUseType,
  LandClassType,
} from '../../../models/assets/ValuationTypes'
import { type GeoFeatureGroup, ZERO } from '../../../types'
import type { LayerLegend } from '../../../types/GISLayerDescriptors'
import {
  useGetAssetsQuery,
  useGetBuildingOutlinesQuery,
  useGetCarbonProjectionsQuery,
  useGetFullRemainingGeometryQuery,
  useGetPlantingAssetsQuery,
  useGetSpeciesTypesQuery,
  useGetValuationTypesQuery,
} from '../../services/assets'
import { useGetValuationSummaryQuery } from '../../services/valuations'
import {
  availableImprovementAssets,
  improvementAssetsWithBuildingOutlines,
} from './selectors'
import type { ImprovementAsset } from '@models/assets/ImprovementAsset'

export const useMergedAssetsByType = (valuationId: string) => {
  const selectedAssetType = useSelector((state: RootState) =>
    assetsSelectors.selectedAssetType(state)
  )

  const { data: featureGroup } = useGetAssetsQuery(valuationId, {
    selectFromResult: ({ data }) => {
      if (selectedAssetType === 'All' || selectedAssetType === undefined) {
        return {
          data: {
            type: 'FeatureCollection',
            features: Object.entries(data?.features ?? {}).reduce(
              (accum: Feature<Geometry, Asset>[], [, featureGroup]) => {
                const updatedFeatures = accum.concat(
                  featureGroup.features as Feature<Geometry, Asset>[]
                )
                return updatedFeatures
              },
              []
            ),
          } as FeatureCollection<Geometry, Asset>,
        }
      }
      if (data === undefined) {
        return {
          data: undefined,
        }
      }

      return {
        data: data.features[
          selectedAssetType as AssetsFeatureKey
        ] as FeatureCollection<Geometry, Asset>,
      }
    },
  })

  return featureGroup
}

export const useAvailableLandAssets = (valuationId: string) => {
  const deletedAssets = useSelector((state: RootState) =>
    assetsSelectors.assetsToDelete(state, 'Land')
  )
  const { data } = useGetAssetsQuery(valuationId, {
    selectFromResult: ({ data }) => ({
      data:
        data?.features?.Land?.features
          ?.filter((feature) => !deletedAssets[feature.id ?? -1])
          ?.map(
            (feature: Feature<Geometry, LandAsset>): AssetUpdate<LandAsset> => {
              return {
                asset: feature.properties,
                geometry: feature.geometry,
                pseudoId: (feature.id ?? -1).toString(),
                editState: 'EXISTING',
                area: feature.properties.area ?? 0,
              }
            }
          ) ?? [],
    }),
  })
  return data
}

export const useAvailablePlantingAssets = (valuationId: string) => {
  const deletedAssets = useSelector((state: RootState) =>
    assetsSelectors.assetsToDelete(state, 'Planting')
  )
  const { data } = useGetPlantingAssetsQuery(valuationId, {
    selectFromResult: ({ data }) => ({
      data:
        data?.features
          ?.filter((feature) => !deletedAssets[feature.id ?? -1])
          ?.map(
            (
              feature: Feature<Geometry, PlantingAsset>
            ): AssetUpdate<PlantingAsset> => {
              return {
                asset: feature.properties,
                geometry: feature.geometry,
                pseudoId: (feature.id ?? -1).toString(),
                editState: 'EXISTING',
                area: feature.properties.area ?? 0,
              }
            }
          ) ?? [],
    }),
  })
  return data
}

export const useAssetById = (
  valuationId: string,
  assetType: AssetsFeatureKey,
  assetId: string | number | undefined
) => {
  const { data: asset } = useGetAssetsQuery(valuationId, {
    selectFromResult: ({ data }) => {
      return {
        data: (
          data?.features?.[assetType]?.features as Feature<Geometry, Asset>[]
        )?.find(
          (feature) =>
            feature?.properties?.id?.toString() === assetId?.toString()
        )?.properties,
      }
    },
  })

  return asset
}

export const useAssetFeatureById = (
  valuationId: string,
  assetType: AssetType,
  assetId: number
) => {
  const { data: assetFeature } = useGetAssetsQuery(valuationId, {
    selectFromResult: ({ data }) => {
      return {
        data: (
          data?.features?.[assetType as AssetsFeatureKey]?.features as Feature<
            Geometry,
            Asset
          >[]
        )?.find(
          (feature) =>
            feature?.properties?.id?.toString() === assetId.toString()
        ),
      }
    },
  })

  return assetFeature
}

export const useLatLonCenter = (valuationId: string) => {
  const { data: valuationSummary } = useGetValuationSummaryQuery(valuationId)

  return useMemo(() => {
    if (valuationSummary?.address) {
      return [
        valuationSummary?.address?.lat,
        valuationSummary?.address?.lng,
      ] as [number, number]
    }
    return undefined
  }, [valuationSummary?.address])
}

export const useCenter = (valuationId: string) => {
  const { data: valuationSummary } = useGetValuationSummaryQuery(valuationId)

  const valuationCenter = useMemo(
    () =>
      ({
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [
            valuationSummary?.address?.lng ?? 0,
            valuationSummary?.address?.lat ?? 0,
          ],
        },
        properties: {},
      }) as Feature<Point>,
    [valuationSummary]
  )

  return valuationCenter
}

export const useTitles = (valuationId: string) => {
  const { data } = useGetValuationSummaryQuery(valuationId)
  return data?.titles
}

export const useAddress = (valuationId: string) => {
  const { address: valuationAddress } = useGetValuationSummaryQuery(
    valuationId,
    {
      selectFromResult: ({ data }) => ({
        address: data?.address?.address ?? '',
      }),
    }
  )

  return valuationAddress
}

export const useAvailableImprovementAssetsWithOutlines = (
  valuationId: string
): FeatureCollection<Geometry, AssetUpdate<ImprovementAsset>> => {
  const { data: assets } = useGetAssetsQuery(valuationId)
  const { data: buildingOutlines } = useGetBuildingOutlinesQuery(valuationId)

  return useSelector(
    (state: RootState) =>
      improvementAssetsWithBuildingOutlines(
        state,
        valuationId,
        buildingOutlines,
        assets
      ),
    shallowEqual
  )
}

export const useAvailableImprovementAssets = (valuationId: string) => {
  const { data: assets } = useGetAssetsQuery(valuationId)
  const { data: buildingOutlines } = useGetBuildingOutlinesQuery(valuationId)

  return useSelector(
    (state: RootState) =>
      availableImprovementAssets(state, valuationId, buildingOutlines, assets),
    shallowEqual
  )
}

export const useGetSpeciesTypesMap = () => {
  return useGetSpeciesTypesQuery(undefined, {
    selectFromResult: ({ data }) => {
      return {
        data: (data ?? []).reduce(
          (accum: { [key: string]: string }, speciesType) => {
            accum[speciesType.value] = speciesType.label
            return accum
          },
          {}
        ),
      }
    },
  })
}

export const useAssetColorsAndNames = (valuationId: string) => {
  const { data: carbonProjections } = useGetCarbonProjectionsQuery(valuationId)

  const { data: speciesMap } = useGetSpeciesTypesMap()

  const COLORS = [
    '#23C200',
    '#009C00',
    '#007723',
    '#005300',
    '#005343',
    '#009500',
    '#00C223',
    '#00C255',
    '#009C23',
  ]

  if (carbonProjections === undefined) {
    return {}
  }

  const types = carbonProjections.perAsset.map((assetProjectionData) => {
    return assetProjectionData.speciesId
  })

  return types.reduce(
    (
      accum: { [speciesId: string]: { title: string; color: string } },
      speciesId,
      index
    ) => {
      accum[speciesId] = {
        title: speciesMap[speciesId],
        color: COLORS[index % COLORS.length],
      }
      return accum
    },
    {}
  )
}

export const usePropertyAreas = (valuationId: string) => {
  const { data: valuation } = useGetValuationSummaryQuery(valuationId)
  return useMemo(
    () => ({
      totalSurveyAreaM2: valuation?.valuation.totalSurveyArea ?? 0,
      totalGeoAreaM2: valuation?.valuation.totalArea ?? 0,
    }),
    [valuation]
  )
}

export const useAssetSummaryStatistics = (valuationId: string) => {
  const propertyAreas = usePropertyAreas(valuationId)

  const { data: assets } = useGetAssetsQuery(valuationId)

  return useMemo(
    () => ({
      propertyAreas,
      totalSurveyAreaM2: propertyAreas.totalSurveyAreaM2,
      totalHectares: assets?.valuationSummary?.totalHectares ?? ZERO,
      effectiveHectares:
        assets?.valuationSummary?.totalEffectiveHectares ?? ZERO,
      ineffectiveHectares:
        assets?.valuationSummary?.totalIneffectiveHectares ?? ZERO,
      summary: assets?.valuationSummary,
      totalNonTitledHectares:
        assets?.valuationSummary?.totalNonTitledHectares ?? ZERO,
      nonTitledEffectiveHectares:
        assets?.valuationSummary?.totalNonTitledEffectiveHectares ?? ZERO,
      nonTitledIneffectiveHectares:
        assets?.valuationSummary?.totalNonTitledIneffectiveHectares ?? ZERO,
    }),
    [assets?.valuationSummary, propertyAreas]
  )
}

export const useDefaultHighestAndBestUseType = (
  valuationId: string | number | SkipToken
) => {
  const { data: valuationSummary } = useGetValuationSummaryQuery(valuationId)
  return valuationSummary?.valuation?.highestAndBestUseType
}

export const useHighestAndBestUseOptions = () => {
  const { bestUseOptions } = useGetValuationTypesQuery(undefined, {
    selectFromResult: ({ data = {} }) => {
      const bestUseOptions: Record<string, HighestAndBestUseType> =
        Object.values(data).reduce<Record<string, HighestAndBestUseType>>(
          (accum, value) => {
            accum[value.highestAndBestUse] = value
            return accum
          },
          {}
        )
      return {
        bestUseOptions: Object.values(bestUseOptions)
          .sort((a: HighestAndBestUseType, b: HighestAndBestUseType) => {
            if (a.bestUseCategory < b.bestUseCategory) {
              return -1
            }
            if (a.bestUseCategory > b.bestUseCategory) {
              return 1
            }
            return 0
          })
          .map((bestUseType) => toOption(bestUseType.highestAndBestUse)),
      }
    },
  })
  return bestUseOptions
}

export const useHighestAndBestUseSelectOptions = (
  includeAllOption?: boolean,
  validOptions?: number[]
) => {
  const { data: bestUseTypes = {} } = useGetValuationTypesQuery()

  const bestUseOptions = useMemo(() => {
    const options = Object.values(bestUseTypes)
      .filter((bestUseType: HighestAndBestUseType) => {
        if (validOptions) {
          return validOptions.some((option) => option === bestUseType.id)
        }
        return true
      })
      .map((bestUseType: HighestAndBestUseType) => {
        return {
          label:
            bestUseType.bestUseCategory === bestUseType.highestAndBestUse
              ? bestUseType.highestAndBestUse
              : `${bestUseType.bestUseCategory} - ${bestUseType.highestAndBestUse}`,
          value: bestUseType.id,
        }
      })
    options.sort((a, b) => a.label.localeCompare(b.label))
    if (includeAllOption) {
      return [{ label: 'All', value: -1 }, ...options]
    }
    return options
  }, [bestUseTypes, includeAllOption, validOptions])

  return bestUseOptions
}

export const useLandAssetLayerLegend = (
  valuationId: string,
  selectedAssetId?: string | number
): [GeoFeatureGroup<LandAsset> | undefined, LayerLegend] => {
  const FIELD_LABEL = 'bestUseLandClass'

  const { data: assets } = useGetAssetsQuery(valuationId)

  const { data: valuationTypes } = useGetValuationTypesQuery()

  const { data: remainingFeature } = useGetFullRemainingGeometryQuery({
    valuationId,
  })

  const labelledFeatures = useMemo(() => {
    if (assets === undefined) {
      return undefined
    }

    let features = assets?.features?.Land?.features?.map((feature) => {
      const landFeature = feature

      return {
        ...feature,
        properties: {
          ...feature.properties,
          grossArea: landFeature.properties.isProductive
            ? landFeature.properties.totalEffectiveArea
            : landFeature.properties.totalIneffectiveArea,
          [FIELD_LABEL]: `${landFeature.properties.landClass.landClass}`,
        },
      }
    })

    if (
      remainingFeature?.remainingFeature &&
      remainingFeature?.remainingFeature?.properties.area > 0.01
    ) {
      features = features.concat({
        ...remainingFeature.remainingFeature,
        properties: {
          ...remainingFeature?.remainingFeature?.properties,
          grossArea: remainingFeature?.remainingFeature.properties?.area,
          [FIELD_LABEL]: 'Unallocated',
          color: '#101fa0',
        },
        // biome-ignore lint: 🙈
      } as any)
    }

    if (selectedAssetId) {
      features = features.filter(
        (feature) =>
          (feature?.id ?? feature?.properties?.id)?.toString() ===
          selectedAssetId?.toString()
      )
    }

    return {
      ...assets?.features?.Land,
      features: features,
    } as GeoFeatureGroup<LandAsset & { [FIELD_LABEL]: string }>
  }, [assets, remainingFeature, selectedAssetId])

  const legend = useMemo(() => {
    const validLandClasses = Object.values(valuationTypes ?? {})?.flatMap(
      (bestUseType: HighestAndBestUseType) => {
        return bestUseType.validLandClasses.map(
          (landClassType: LandClassType) => landClassType
        )
      }
    )

    const usedLandClasses = new Set(
      labelledFeatures?.features
        ?.map((feature) => {
          return feature?.properties?.[FIELD_LABEL] as string | undefined
        })
        ?.filter((value) => value !== undefined) ?? []
    )

    const landClassColours = validLandClasses.reduce(
      (
        accum: { [landClassType: string]: string },
        landClassType: LandClassType
      ) => {
        if (usedLandClasses.has(landClassType.landClass)) {
          accum[landClassType.landClass] = landClassType.color
        }
        return accum
      },
      {}
    )

    landClassColours.Unallocated = '#101fa0'

    return createLegendWithColors('LandAssetLayer', landClassColours)
  }, [labelledFeatures, valuationTypes])

  return [labelledFeatures, legend]
}

// TODO: hack
export const useLegendAreaSummary = (
  geoFeatures: GeoFeatureGroup | undefined,
  legend: LayerLegend | undefined,
  propertyAreas: PropertyArea,
  gisProperty: string,
  areaProperty: string,
  displayM2?: boolean,
  dontProRata?: boolean,
  totalAreaM2?: number
) => {
  return useMemo(() => {
    if (!geoFeatures || !legend) {
      return {}
    }

    const legendMap: { [color: string]: string } = {}

    const actualTotalAreaM2 = totalAreaM2 ?? propertyAreas.totalGeoAreaM2
    let totalProRataArea = 0
    if (dontProRata) {
      totalProRataArea = actualTotalAreaM2
    } else {
      totalProRataArea =
        (propertyAreas.totalSurveyAreaM2 / propertyAreas.totalGeoAreaM2) *
        actualTotalAreaM2
    }

    const totalArea: { [id: string]: 0 } = {}

    for (const feature of Object.values(geoFeatures.features)) {
      const propertyValue =
        getPropertyValue(feature.properties, gisProperty) || ''
      if (totalArea[propertyValue] === undefined) {
        totalArea[propertyValue] = 0
      }
      totalArea[propertyValue] +=
        (areaProperty
          ? getPropertyValue(feature.properties, areaProperty)
          : undefined) ?? feature.properties.area
    }
    for (const propertyValue of Object.keys(totalArea)) {
      const description = legend.getDescriptor(propertyValue)
      let area =
        (totalArea[propertyValue] / actualTotalAreaM2) * totalProRataArea
      if (areaProperty !== undefined) {
        area = totalArea[propertyValue]
      }
      let areaString = ''
      if (displayM2) {
        areaString = formatArea(area, 'm2')
      } else {
        areaString = formatArea(area / 10000, 'ha')
      }
      legendMap[legend.getColor(propertyValue)] =
        `${description} - ${areaString}`
    }

    return legendMap
  }, [
    areaProperty,
    displayM2,
    dontProRata,
    geoFeatures,
    gisProperty,
    legend,
    propertyAreas.totalGeoAreaM2,
    propertyAreas.totalSurveyAreaM2,
    totalAreaM2,
  ])
}
