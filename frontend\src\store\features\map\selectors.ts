import { createSelector } from '@reduxjs/toolkit'
import area from '@turf/area'
import type { RootState } from '../..'
import { uiSelectors } from '../../ui'
import type { MapState } from './types'

export const getSelectedLayerOption = createSelector(
  (state: RootState) => state.map,
  (mapState: MapState) => {
    return mapState.layerSelection
  }
)

export const getElevationLimits = createSelector(
  (state: RootState) => state.map,
  (mapState: MapState) => {
    return mapState.elevationLayer.filter
  }
)

export const getLayerClickingEnabled = createSelector(
  (state: RootState) => state.map,
  (mapState: MapState) => {
    return mapState.enableLayerClicking
  }
)

export const getEnableTitleOutline = createSelector(
  (state: RootState) => state.map,
  (mapState: MapState) => {
    return mapState.enableTitleOutline
  }
)

export const transparentLayers = createSelector(
  (state: RootState) => state.map,
  (mapState: MapState) => {
    return mapState.transparentLayers
  }
)

export const getLayerFillOpacity = createSelector(
  (state: RootState) => state.map,
  (mapState: MapState) => {
    return mapState.transparentLayers ? 0 : 0.2
  }
)

export const getEnableByAssetFilters = (state: RootState) => {
  return state.map.enableByAssetFilters
}

export const getTotalCoveredArea = createSelector(
  (state: RootState) => state.map,
  (mapState: MapState) => {
    if (
      mapState.layerSelection.filter.enable &&
      mapState.layerSelection.filter.overlayFeature
    ) {
      const overlayArea: number | undefined =
        mapState.layerSelection.filter.overlayFeature.properties.area
      return overlayArea ?? area(mapState.layerSelection.filter.overlayFeature)
    }
    return undefined
  }
)

export const getMeasurementFeatures = createSelector(
  (state: RootState) => state.map,
  (mapState: MapState) => {
    return mapState.measurementLayer.geoFeatures
  }
)
