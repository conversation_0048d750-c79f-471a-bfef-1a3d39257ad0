import { Route, Routes } from 'react-router-dom'
import ProtectedRoute from '@components/ProtectedRoute'
import DataExplorerView from '@components/riskRadar/DataExplorerView'
import ExportedScenariosView from '@components/riskRadar/ExportedScenariosView'
import LossModels from '@components/riskRadar/LossModels'
import RiskRadarLayout from '@components/riskRadar/RiskRadarLayout'
import RiskRadarMap from '@components/riskRadar/RiskRadarMap'
import ScenarioResultsView from '@components/riskRadar/ScenarioResultsView'
import RiskRadarScenarioSettingsCard from '@components/riskRadar/ScenarioSettings'
import LossModelPage from './LossModelPage'

const RiskRadarPage = () => {
  return (
    <ProtectedRoute requiredEntitlements={['client:riskradar:*']}>
      <Routes>
        <Route element={<RiskRadarLayout />}>
          <Route index element={<RiskRadarScenarioSettingsCard />} />
          <Route path="models" element={<LossModels />} />
          <Route path="models/:lossModelId" element={<LossModelPage />} />
          <Route path="models/new" element={<LossModelPage />} />
          <Route path="analytics" element={<ScenarioResultsView />} />
          <Route path="data" element={<DataExplorerView />} />
          <Route path="exports" element={<ExportedScenariosView />} />
        </Route>
        <Route element={<RiskRadarLayout cover />}>
          <Route path="map" element={<RiskRadarMap />} />
        </Route>
      </Routes>
    </ProtectedRoute>
  )
}

export default RiskRadarPage
