.dvr-search {
  hr {
    margin: 6px 0;
    border-color: $grey-1;
  }

  td {
    .dvr-search-text {
      font-weight: 600;
    }

    .dvr-owner {
      @include flex;
      color: $grey-4;
      align-items: center;

      &::before {
        content: 'OWNER';
        width: 75px;
        color: $grey-3;
        font-size: smaller;
      }
    }

    .dvr-search-ul {
      padding: 0;
      margin: 0;

      li {
        @include detail-li;

        &.tla-name::before {
          content: 'TLA';
        }

        &.legal-desc::before {
          content: 'DESC.';
        }

        &.land-use-desc::before {
          content: 'USE';
        }

        &.land-zone-desc::before {
          content: 'ZONE';
        }

        &.val-date::before {
          content: 'DATE';
        }

        &.cv::before {
          content: 'VALUE';
        }

        &.lv::before {
          content: 'LAND';
        }

        &.iv::before {
          content: 'IMPROV.';
        }

        &.land-area::before {
          content: 'AREA';
        }

        &.floor-area::before {
          content: 'FLOOR';
        }
      }
    }

    span.unlinked {
      color: $west-coast-sunset;
      font-weight: 500;
    }

    span.prospect {
      font-style: italic;
    }
  }
}
