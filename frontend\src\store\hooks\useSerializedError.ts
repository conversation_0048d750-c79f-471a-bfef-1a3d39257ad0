import type { SerializedError } from '@reduxjs/toolkit'
import type { FetchBaseQueryError } from '@reduxjs/toolkit/dist/query'
import type { FieldData } from 'rc-field-form/lib/interface'

type ExtendedSerializedError =
  | FetchBaseQueryError
  | (SerializedError & { data: string | Record<string, string>[] })

export const useSerializedError = (
  error: FetchBaseQueryError | SerializedError | undefined
) => {
  const { data } = { ...(error as ExtendedSerializedError) }
  return data
}

/**
 * @deprecated util/error
 */
export const useFormError = (
  error: FetchBaseQueryError | SerializedError | undefined
) => {
  const data = (useSerializedError(error) ?? {}) as Record<
    string,
    Array<string>
  >
  const fieldErrors: FieldData[] = []
  // biome-ignore lint: deprecated
  Object.keys(data).forEach((name) => {
    fieldErrors.push({ name, errors: data[name] })
  })
  return fieldErrors
}
