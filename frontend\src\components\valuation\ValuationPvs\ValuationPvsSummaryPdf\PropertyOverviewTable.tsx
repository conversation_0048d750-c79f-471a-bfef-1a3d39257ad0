import { flow, startCase } from 'lodash'
import { get } from 'lodash/fp'
import { PdfList, PdfParagraph } from '@components/pdf'
import type { ValuationsPvsSummary } from '@store/services/sdk'
import { formatAddress, formatNumber } from '@util/labels'
import KeyValueSummaryTable from './KeyValueSummaryTable'
import { estateDescriptionLegalSubstring } from './helpers'
import useLuc from './useLuc'

type Props = {
  valuation: ValuationsPvsSummary
}

const columns = [
  { key: 'key', title: '', isHeader: true },
  { key: 'value', title: '', weighting: 3 },
]

const PropertyOverviewTable = ({ valuation }: Props) => {
  const luc = useLuc(valuation.anzUnion)

  const rows = [
    { key: 'Address', value: formatAddress(valuation.fullAddress) },
    {
      key: 'Location',
      value: valuation.locationDescription?.replace(
        /^This property is located /,
        ''
      ),
    },
    { key: 'TLA', value: valuation.tlaName },
    {
      key: 'Legal Area',
      value: `${valuation.totalHectares} ha`,
    },
    {
      key: 'Legal Description',
      value:
        valuation.titleMatchingRatingValuation?.legalDesc ||
        valuation.titles.features
          .map(
            flow(
              get('properties.estateDescription'),
              estateDescriptionLegalSubstring
            )
          )
          .join(', '),
    },
    {
      key: 'Records of Title',
      value: valuation.titles.features
        .map((feature) => feature.properties.titleNo)
        .join(', '),
    },
    { key: 'Tier', value: valuation.tier },
    {
      key: 'Soils',
      value: <SoilDescription valuation={valuation} />,
    },
    {
      key: 'Land Use Classification',
      value: (
        <PdfList
          size="s"
          items={luc.map(
            ({ luc, area, definition }) =>
              `LUC ${luc} – ${area} ha \n${definition}`
          )}
        />
      ),
    },
    { key: 'Land Classes', value: valuation.landClassText },
    {
      key: 'Elevation',
      value: valuation.elevationDescription?.replace(
        /^The property has a m/,
        'M'
      ),
    },
    {
      key: 'Rainfall',
      value: valuation.seasonalRainfall?.total
        ? `${formatNumber(valuation.seasonalRainfall.total * 1e4, {
            maximumFractionDigits: 0,
          })} mm`
        : undefined,
    },
  ]

  return <KeyValueSummaryTable wrap={true} columns={columns} rows={rows} />
}

const SoilDescription = ({ valuation }: Props) => {
  if (valuation.smapDescriptions.length > 0) {
    return (
      <>
        <PdfParagraph size="s">
          The underlying land consists of the following soil families:
        </PdfParagraph>
        <PdfList size="s" items={valuation.smapDescriptions} />
      </>
    )
  }
  if (valuation.fslSoilDescriptions.length > 0) {
    return (
      <>
        <PdfParagraph size="s">
          The underlying land consists of the following soil families (FSL)
        </PdfParagraph>
        <PdfList size="s" items={valuation.fslSoilDescriptions} />
      </>
    )
  }
  return (
    <PdfParagraph size="s">
      Soil families data is not available for this property.
    </PdfParagraph>
  )
}

export default PropertyOverviewTable
