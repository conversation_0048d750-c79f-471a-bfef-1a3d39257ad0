import { PdfTable } from '@components/pdf'
import type { ValuationsPvsSummary } from '@store/services/sdk'
import { formatNumber } from '@util/labels'
import { estateDescriptionLegalSubstring } from './helpers'

interface Props {
  titleArea?: ValuationsPvsSummary['savedTitleArea']
  titles: ValuationsPvsSummary['titles']
}

const columns = [
  { key: 'legalDescription', weighting: 0.3 },
  { key: 'identifier', weighting: 0.15 },
  { key: 'registeredOwner', weighting: 0.3 },
  { key: 'estate', weighting: 0.15 },
  {
    key: 'area',
    title: 'Area (ha)',
    weighting: 0.1,
  },
]

const LegalDescriptionTable = ({ titleArea, titles }: Props) => {
  return (
    <PdfTable
      striped
      columns={columns}
      rows={titles.features.map((title) => ({
        // Not sure how to get this information, this won't work a lot of the time
        legalDescription: estateDescriptionLegalSubstring(
          title.properties.estateDescription
        ),
        identifier: title.properties?.titleNo,
        registeredOwner: title.properties?.owners,
        estate: title.properties?.estateDescription?.split(', ')[0],
        area: formatNumber(title.properties?.areaHa, {
          maximumFractionDigits: 4,
        }),
      }))}
      summary={[
        { title: 'Total', isHeader: true },
        { title: titleArea, index: 4 },
      ]}
    />
  )
}

export default LegalDescriptionTable
