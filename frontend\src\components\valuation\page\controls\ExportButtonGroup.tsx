import { DownloadOutlined } from '@ant-design/icons'
import { Menu } from 'antd'
import ButtonGroup from 'antd/lib/button/button-group'
import React, { useMemo } from 'react'
import type { Valuation } from '@types'
import { toURL } from '../../../../util'
import { DropdownHoverButton } from '../../../generic/DropdownHoverButton'

export interface ExportButtonGroupProps {
  valuation: Valuation
}

export const ExportButtonGroup = ({ valuation }: ExportButtonGroupProps) => {
  const [exportKml, exportXlsx] = useMemo(() => {
    const exportKml = toURL(
      `/api/address/${valuation.addressId}/valuations/${valuation.valuationId}/kml_export/`
    )
    const exportXlsx = toURL(
      `/api/address/${valuation.addressId}/valuations/${valuation.valuationId}/export/`
    )
    return [exportKml, exportXlsx]
  }, [valuation])

  const downloadOverlay = useMemo(() => {
    return (
      <Menu>
        <Menu.Item key="KML_EXPORT">
          <a href={exportKml}>Export KML</a>
        </Menu.Item>
      </Menu>
    )
  }, [exportKml])

  return (
    <ButtonGroup>
      <DropdownHoverButton
        mainIcon={<DownloadOutlined />}
        type="primary"
        overlay={downloadOverlay}
        onClick={() => {
          window.open(exportXlsx, 'name')
        }}
        title="Export XLSX"
      />
    </ButtonGroup>
  )
}
