import { skipToken } from '@reduxjs/toolkit/dist/query'
import type { GeoJsonObject } from 'geojson'
import React from 'react'
import { GeoJSON as GeoJSONLeaflet } from 'react-leaflet'
import { shallowEqual, useSelector } from 'react-redux'
import type { RootState } from '../../../../../../store'
import { addressesSelectors } from '../../../../../../store/features/address'
import { useGetAddressServiceCentreDistancesQuery } from '../../../../../../store/services/address'

export interface DistancesLayerProps {
  addressId: string
}

export const DistancesLayer = (props: DistancesLayerProps) => {
  const { addressId } = props

  const distancePanelState = useSelector(
    (state: RootState) => addressesSelectors.getPanelState(state, 'distances'),
    shallowEqual
  )

  const { data: serviceCentres } = useGetAddressServiceCentreDistancesQuery(
    distancePanelState.layerVisible ? addressId : skipToken
  )

  if (!(distancePanelState.layerVisible && serviceCentres)) return null

  return (
    <GeoJSONLeaflet
      data={serviceCentres as GeoJsonObject}
      style={{ color: 'orange', weight: 2 }}
    />
  )
}
