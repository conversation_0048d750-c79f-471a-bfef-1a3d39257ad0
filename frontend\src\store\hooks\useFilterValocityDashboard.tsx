import { useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { uiSelectors } from '@store/ui'
import {
  clearValocitySaleFilterState,
  updateValocitySaleFilterState,
} from '@store/ui/actions'

const pageName = 'valocityDashboard'

export const RURAL_LANDUSES = [
  'PRIMARY INDUSTRY / MARKET. GARDENS AND ORCHARDS',
  'PRIMARY INDUSTRY / STORE SHEEP',
  'PRIMARY INDUSTRY / SPECIALIST LIVESTOCK',
  'PRIMARY INDUSTRY / DAIRYING',
  'PRIMARY INDUSTRY / MULTI-USE WITHIN PRIMARY INDUSTRY',
  'PRIMARY INDUSTRY / ARABLE FARMING',
  'PRIMARY INDUSTRY / VACANT',
  'PRIMARY INDUSTRY / MINERAL EXTRACTION',
  'PRIMARY INDUSTRY / STOCK FATTENING',
  'PRIMARY INDUSTRY / FORESTRY',
]

const LIFESTYLE_LANDUSES = [
  'LIFESTYLE / MULTI UNIT',
  'LIFESTYLE / MULTI-USE WITHIN LIFESTYLE',
  'LIFESTYLE / SINGLE UNIT',
  'LIFESTYLE / VACANT',
]

const useFilterValocityDashboard = () => {
  const dispatch = useDispatch()

  const user = useSelector(uiSelectors.getUser)

  const filterRural = useCallback(() => {
    dispatch(
      updateValocitySaleFilterState({
        pageName: pageName,
        update: {
          landUses: RURAL_LANDUSES,
          regionIn: user?.assignedTlas ?? [],
          irrelevantDateIsnull: true,
          linked: false,
        },
      })
    )
  }, [dispatch, user?.assignedTlas])

  const filterLifestyle = useCallback(() => {
    dispatch(
      updateValocitySaleFilterState({
        pageName: pageName,
        update: {
          landUses: LIFESTYLE_LANDUSES,
          regionIn: user?.assignedTlas ?? [],
          irrelevantDateIsnull: true,
        },
      })
    )
  }, [dispatch, user?.assignedTlas])

  const resetFilters = useCallback(() => {
    dispatch(clearValocitySaleFilterState({ pageName: pageName }))
  }, [dispatch])

  return {
    filterRural,
    filterLifestyle,
    resetFilters,
  }
}

export default useFilterValocityDashboard
