import { useMemo } from 'react'
import { useUserCurrentRetrieveQuery } from '@store/services/sdk'

export const matches = (entitlement: string, required: string): boolean => {
  const patterns = required
    .split('|')
    .map((part) => new RegExp(`^${part.replace(/\*/g, '.*')}$`))
  return patterns.some((regex) => regex.test(entitlement))
}

const useEntitlements = (requiredEntitlements?: string[]): boolean => {
  const { data: user } = useUserCurrentRetrieveQuery()

  const isEntitled = useMemo(() => {
    if (!requiredEntitlements) return true
    if (!user) return false
    return requiredEntitlements.every((required) =>
      user.entitlements.some((entitlement) => matches(entitlement, required))
    )
  }, [requiredEntitlements, user])

  return isEntitled
}

export default useEntitlements
