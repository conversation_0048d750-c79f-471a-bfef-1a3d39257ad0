import type { AreaConfig } from '@ant-design/charts'
import { useMemo } from 'react'
import { createLineAnnotation } from '@components/customer/helpers'
import type { EmissionBenchmarkingRetrieveApiResponse } from '@store/services/sdk'

type Annotations = AreaConfig['annotations']

const useEmissionBenchmarkData = (
  benchmarking: EmissionBenchmarkingRetrieveApiResponse | undefined
) => {
  const lineChartData = useMemo(() => {
    const {
      pct10th,
      pct20th,
      pct30th,
      pct40th,
      pct50th,
      pct60th,
      pct70th,
      pct80th,
      pct90th,
    } = { ...benchmarking }
    return [
      { value: Number(pct10th), measure: '10th' },
      { value: Number(pct20th), measure: '20th' },
      { value: Number(pct30th), measure: '30th' },
      { value: Number(pct40th), measure: '40th' },
      { value: Number(pct50th), measure: '50th' },
      { value: Number(pct60th), measure: '60th' },
      { value: Number(pct70th), measure: '70th' },
      { value: Number(pct80th), measure: '80th' },
      { value: Number(pct90th), measure: '90th' },
    ]
  }, [benchmarking])

  const annotations: Annotations = useMemo(() => {
    const { customerEmissions, lowerQuartile, median, upperQuartile } = {
      ...benchmarking,
    }

    const arr: Annotations = []

    if (customerEmissions) {
      arr.push({
        type: 'line',
        start: ['min', Number(customerEmissions)],
        end: ['max', Number(customerEmissions)],
        style: {
          stroke: '#007dba',
          lineWidth: 1,
        },
        text: {
          style: {
            fill: '#007dba',
          },
          content: `Customer (${Number(customerEmissions)?.toLocaleString(
            'en-NZ',
            {
              maximumFractionDigits: 1,
            }
          )} CO2eq Tonnes)`,
        },
      })
    }

    if (lowerQuartile && median && upperQuartile) {
      return [
        ...arr,
        {
          type: 'regionFilter',
          start: ['min', Number(lowerQuartile)],
          end: ['max', '0'],
          color: '#65d448',
        },
        {
          type: 'regionFilter',
          start: ['min', Number(median)],
          end: ['max', Number(lowerQuartile)],
          color: '#a5ad00',
        },
        {
          type: 'regionFilter',
          start: ['min', Number(upperQuartile)],
          end: ['max', Number(median)],
          color: '#d17a00',
        },
        {
          type: 'regionFilter',
          start: ['min', 'max'],
          end: ['max', Number(upperQuartile)],
          color: '#f4664a',
        },
        createLineAnnotation(Number(median), 1, '#888', 'Median'),
        createLineAnnotation(Number(lowerQuartile), 1, '#a5ad00', 'LQ'),
        createLineAnnotation(Number(upperQuartile), 1, '#d17a00', 'UQ'),
      ]
    }

    return []
  }, [benchmarking])

  return {
    lineChartData,
    annotations,
  }
}

export default useEmissionBenchmarkData
