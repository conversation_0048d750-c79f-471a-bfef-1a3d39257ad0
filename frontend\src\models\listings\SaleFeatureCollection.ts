import type { Feature, FeatureCollection, Point } from 'geojson'
import type { ViewportListingProperties } from './Listing'

export type ValocityListingFeature = Feature<Point, ViewportListingProperties>

export interface ValocityListingFeatureCollection extends FeatureCollection {
  features: ValocityListingFeature[]
}

export interface ValocitySaleProperties {
  fullAddress: string
  cnt: number
  grossSalesPrice: number
  improvementsValue: number
  totalHa: number
  saleDate: string
  saleType: string
  vendorBank: string
  purchaserBank: string
  vendor: string
  purchaser: string
  saleId?: string
  address?: string
  linkedSales?: string[]
  linkedToListing: boolean
}

export type ValocitySaleFeature = Feature<Point, ValocitySaleProperties>

export interface ValocitySaleFeatureCollection extends FeatureCollection {
  features: ValocitySaleFeature[]
}
