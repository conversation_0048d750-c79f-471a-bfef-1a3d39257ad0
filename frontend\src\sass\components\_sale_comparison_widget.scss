.ant-collapse-header {
  display: flex;
  flex-direction: row;
}

.sale-comparison-header {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  h2 > .comparable-identifier {
    margin-left: -5px;
    margin-right: 10px;
  }

  .header-summary-values {
    justify-self: flex-end;
    color: $anz-ocean-blue;
    text-transform: uppercase;
    font-weight: 500;

    display: flex;
    flex-direction: column;
    margin: -10px;
    padding: 0;

    .summary-value {
      .summary-value-label {
        ::after {
          content: ':';
        }
      }
    }
  }
}

.selected > .ant-collapse-header {
  background-color: rgba(210, 210, 210, 0.5);
}

.comparable-sale-pane {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-flow: wrap;
  width: 100%;

  .comparable-sale-column {
    max-width: 800px;
  }

  .comparable-sale-adjustment-column {
    flex-grow: 1;
    margin: 10px;
  }

  .adjustment-select {
    width: 100%;
  }
}

.adjustments {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 15px;
}

.overall-adjustment-statistic {
  .ant-statistic-content {
    font-size: 16px;
  }
}

.adjustment {
  .superior {
    color: rgb(10, 200, 100);
  }

  .comparable {
  }

  .inferior {
    color: red;
  }
}

.comparable-identifier {
  text-shadow: 1px 1px rgba(200, 200, 200, 0.6);
  color: $anz-ocean-blue;
}

.comparable-benchmark-label {
  color: black;
}
