.ant-table-wrapper:not(.unstyle) {
  table {
    font-size: 12px;
  }

  table .data {
    font-size: 12px;
  }

  td > div {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  th,
  .ant-table-thead > tr > th,
  .ant-table-small .ant-table-thead > tr > th {
    font-weight: 700;
    background-color: tint($grey, 75);
    color: $grey;
  }

  .selected-valuation {
    background-color: tint($primary, 90);
  }

  .striped-row {
    background-color: tint($grey, 90);
  }

  .mortgagee-list {
    display: flex;
    flex-direction: column;
  }

  .ant-table-small.ant-table .ant-table-expanded-row-level-1 > .ant-table-cell {
    padding: 0px;
  }

  .ant-table.ant-table-small
    .ant-table-tbody
    .ant-table-wrapper:only-child
    .ant-table {
    margin: 0px;
  }

  .main-search-container,
  .no-header-table .ant-table-tbody {
    background-color: #fafafa;
    border-top: solid 1px #eee;
  }

  .main-search-container,
  .no-header-table .ant-table-tbody > tr > td {
    border: solid 1px #eee;
  }

  .main-search-container,
  .no-header-table .ant-table-tbody > tr:nth-child(even) > td {
    background-color: #fff;
  }

  .main-search-container,
  .no-header-table .ant-table-footer {
    margin-top: $half;
    border: solid 1px #eee;
  }

  .no-header-table .deleted-title {
    background-color: 'red';
  }

  .no-header-table {
    max-height: 100% !important;
  }

  .selected-row,
  tr.selected-row > td,
  tr.selected-row:hover > td {
    background-color: rgba(0, 255, 10, 0.05) !important;
  }

  .ant-table-row.new-feature-row > td:first-child::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 5px;
    height: 100%;
    background-color: rgba(80, 250, 130, 0.8);
  }

  .sub-value::before {
    content: '\21b3';
    padding-right: 5px;
  }

  .sub-value {
    font-size: 8pt;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
