import { type PayloadAction, createSlice, isAnyOf } from '@reduxjs/toolkit'
import type { LatLngBounds, LatLngLiteral } from 'leaflet'
import { DEFAULT_ZOOM } from '@components/explorer/helpers'
import { mapApi } from '@store/services/map'
import { enhancedApi as sdkApi } from '@store/services/sdk'
import type { MapBaseLayer } from '@store/ui/types'
import type { LayerType } from '@models/gis/LayerType'
import type { GeoFeatureMap } from '../../../types'
import type { MapState, UpdateLayerSelectionActionPayload } from './types'

const name = 'map'

const initialState = (): MapState => ({
  baseLayer: 'OpenStreetMap',
  bounds: undefined,
  center: { lng: 175.1070803, lat: -37.4225643 },
  layersLoading: 0,
  menuFeatureId: undefined,
  menuPosition: {
    x: 0,
    y: 0,
  },
  menuType: undefined,
  zoom: DEFAULT_ZOOM,

  enableLayerClicking: true,
  layerSelection: {
    layerType: 'titles' as LayerType,
    selectedField: '',
    filter: {
      enable: false,
    },
  },
  elevationLayer: {
    current: {
      minElevation: 0,
      maxElevation: 0,
    },
    filter: {
      minElevation: 0,
      maxElevation: 0,
      elevationStep: 25,
    },
  },
  enableByAssetFilters: true,
  enableTitleOutline: true,
  transparentLayers: false,
  measurementLayer: {
    geoFeatures: {},
  },
  mapCentre: undefined,
  // ...getStoredState(),
})

function setMenuOpenOfType(menuType: MapState['menuType']) {
  return function setMenuOpen(
    state: MapState,
    {
      payload,
    }: PayloadAction<{ id: number; position: MapState['menuPosition'] }>
  ) {
    state.menuFeatureId = payload.id
    state.menuPosition = payload.position
    state.menuType = menuType
  }
}

export const mapSlice = createSlice({
  name,
  initialState,
  reducers: {
    setBaseLayer(state, { payload }: PayloadAction<MapBaseLayer>) {
      state.baseLayer = payload
    },
    setBounds(state, { payload }: PayloadAction<LatLngBounds>) {
      state.bounds = payload?.toBBoxString()
    },
    setCenter(state, { payload }: PayloadAction<LatLngLiteral>) {
      state.center = payload
    },
    setZoom(state, { payload }: PayloadAction<number>) {
      state.zoom = payload
    },
    toggleTransparentLayers(state: MapState) {
      state.transparentLayers = !state.transparentLayers
    },
    updateLayerSelection(
      state: MapState,
      { payload }: PayloadAction<UpdateLayerSelectionActionPayload>
    ) {
      if (payload.layerType) {
        state.layerSelection.layerType = payload.layerType
      }
      if (payload.selectedField) {
        state.layerSelection.selectedField = payload.selectedField
      }
      if (payload.overlayFeature !== undefined) {
        state.layerSelection.filter.enable = true
        state.layerSelection.filter.overlayFeature = payload.overlayFeature
        state.layerSelection.filter.boundingFeature = payload.boundingFeature
      } else if (payload.clearPreviousFilter) {
        state.layerSelection.filter = { enable: false }
      }
      state.layerSelection.byAsset = payload.byAsset
    },
    resetLayerSelectionFilter(state: MapState) {
      state.layerSelection.filter = {
        enable: false,
      }
    },
    toggleLayerClicking(
      state: MapState,
      { payload }: PayloadAction<{ enable: boolean }>
    ) {
      state.enableLayerClicking = payload.enable
    },
    toggleTitleOutline(
      state: MapState,
      { payload }: PayloadAction<{ enable: boolean }>
    ) {
      state.enableTitleOutline = payload.enable
    },
    toggleByAssetFilters(
      state: MapState,
      { payload }: PayloadAction<{ enable: boolean }>
    ) {
      state.enableByAssetFilters = payload.enable
    },
    updateElevationLayerLimits(
      state: MapState,
      { payload }: PayloadAction<{ minElevation: number; maxElevation: number }>
    ) {
      state.elevationLayer.current = { ...payload }
      state.elevationLayer.filter = { ...payload, elevationStep: 25 }
    },
    updateElevationLayerFilter(
      state: MapState,
      {
        payload,
      }: PayloadAction<{
        minElevation?: number
        maxElevation?: number
        elevationStep?: number
      }>
    ) {
      if (payload.minElevation !== undefined) {
        state.elevationLayer.filter.minElevation = payload.minElevation
      }
      if (payload.maxElevation !== undefined) {
        state.elevationLayer.filter.maxElevation = payload.maxElevation
      }
      if (payload.elevationStep !== undefined) {
        state.elevationLayer.filter.elevationStep = payload.elevationStep
      }
    },
    updateElevationLayerStep(
      state: MapState,
      { payload }: PayloadAction<{ step: number }>
    ) {
      state.elevationLayer.filter.elevationStep = payload.step
    },
    updateMeasurementFeatures(
      state: MapState,
      { payload }: PayloadAction<{ featureMap: GeoFeatureMap }>
    ) {
      state.measurementLayer.geoFeatures = payload.featureMap
    },
    setMapCentre(
      state: MapState,
      { payload }: PayloadAction<{ centre: [number, number] }>
    ) {
      state.mapCentre = payload.centre
    },
    setMenuClosed(state) {
      state.menuType = undefined
    },
    setAddressMenuOpen: setMenuOpenOfType('address'),
    setSaleMenuOpen: setMenuOpenOfType('sale'),
  },
  extraReducers: (builder) => {
    builder.addMatcher(
      isAnyOf(
        mapApi.endpoints.getViewportAddresses.matchPending,
        mapApi.endpoints.getViewportSales.matchPending,
        mapApi.endpoints.getViewportTitles.matchPending,
        sdkApi.endpoints.explorerTitlesList.matchPending
      ),
      (state) => {
        state.layersLoading += 1
      }
    )
    builder.addMatcher(
      isAnyOf(
        mapApi.endpoints.getViewportAddresses.matchFulfilled,
        mapApi.endpoints.getViewportAddresses.matchRejected,
        mapApi.endpoints.getViewportSales.matchFulfilled,
        mapApi.endpoints.getViewportSales.matchRejected,
        mapApi.endpoints.getViewportTitles.matchFulfilled,
        mapApi.endpoints.getViewportTitles.matchRejected,
        sdkApi.endpoints.explorerTitlesList.matchPending,
        sdkApi.endpoints.explorerTitlesList.matchRejected
      ),
      (state) => {
        state.layersLoading -= 1
      }
    )
  },
})

export const mapReducer = mapSlice.reducer
