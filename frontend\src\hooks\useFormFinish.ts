import { type FormInstance, message } from 'antd'
import type { ValidateErrorEntity } from 'rc-field-form/lib/interface'
import { useCallback } from 'react'
import { createErrorMessage, getErrorMessageText } from '@util/error'

export function handleFinishFailed<F>(
  { errorFields }: ValidateErrorEntity<F>,
  callback?: (errorFields: ValidateErrorEntity<F>['errorFields']) => void
) {
  console.error('error', JSON.stringify(errorFields))
  callback?.(errorFields)
  void message.error(
    'Validation failed, please ensure all fields have no errors.'
  )
}

function useFormFinish<F, R>(
  form: FormInstance<F>,
  save?: (values: F) => Promise<R>,
  options?: {
    successMessage?: string
  }
) {
  const onFinish = useCallback(
    async (values: F) => {
      if (!save) return
      try {
        const res = await save(values)
        await message.success(options?.successMessage ?? 'Saved successfully.')
        return res
      } catch (err) {
        const errorMessage = createErrorMessage(err)
        await message.error(errorMessage, 6)
        return null
      }
    },
    [save, options]
  )

  const onFinishFailed = useCallback(
    (errors: ValidateErrorEntity<F>) => {
      handleFinishFailed(errors, form?.setFields)
    },
    [form]
  )

  return {
    onFinish,
    onFinishFailed,
  }
}

export default useFormFinish
