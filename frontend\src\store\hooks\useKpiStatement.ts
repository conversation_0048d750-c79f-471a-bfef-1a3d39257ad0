import { useMemo } from 'react'
import type { CustomerKpiListApiResponse } from '@store/services/sdk'
import { formatDate } from '@util'

const HISTORICAL_ANALYSIS_REPORT: Record<
  string,
  Record<
    string,
    {
      value: string
      isNegative?: boolean
      isSubtotal?: boolean
      isTotal?: boolean
    }[]
  >
> = {
  C: {},
  P: {},
  R: {
    Income: [
      { value: 'TOTAL_STOCK_SALES' },
      { value: 'TOTAL_STOCK_PURCHASES', isNegative: true },
      { value: 'NET_STOCK_SALES', isSubtotal: true },
      { value: 'PIPFRUIT_INCOME' },
      { value: 'GRAPES_STONEFRUIT_INCOME' },
      { value: 'KIWIFRUIT_INCOME' },
      { value: 'MILK_INCOME' },
      { value: 'WOOL_INCOME' },
      { value: 'VELVET_INCOME' },
      { value: 'CROPPING_INCOME' },
      { value: 'OTHER_INCOME' },
      { value: 'HORTICULTURE_INCOME' },
      { value: 'SUNDRY_FARM_INCOME' },
      { value: 'FARM_CASH_IN', isSubtotal: true },
      { value: 'OFF_FARM_INCOME' },
      { value: 'COST_OF_GOODS_SOLD' },
      { value: 'GROSS_CASH_IN', isSubtotal: true },
    ],
    Expenses: [
      { value: 'WAGES' },
      { value: 'ANIMAL_HEALTH' },
      { value: 'BREEDING_AND_TESTING' },
      { value: 'CONTRACTING' },
      { value: 'ELECTRICITY' },
      { value: 'FEED_HAY_SILAGE_GRAIN' },
      { value: 'FERTILISER_AND_LIME' },
      { value: 'FREIGHT' },
      { value: 'GRAZING' },
      { value: 'POLLINATION' },
      { value: 'POST_HARVEST_EXPENSES' },
      { value: 'SEED' },
      { value: 'SHED_EXPENSES' },
      { value: 'WEED_AND_PEST' },
      { value: 'REPAIRS_AND_MAINTENANCE' },
      { value: 'VEHICLE_EXPENSES' },
      { value: 'ADMINISTRATION' },
      { value: 'STANDING_CHARGES' },
      { value: 'MISCELLANEOUS_OTHER_FWE' },
      { value: 'TOTAL_FARM_WORKING_EXPENSES', isSubtotal: true },
      { value: 'CASHFLOW_SEASONAL_INTEREST' },
      { value: 'OTHER_CURRENT_LIABILITY_INTEREST' },
      { value: 'TERM_INTEREST' },
      { value: 'RENT_AND_BAILMENT' },
      { value: 'TOTAL_INTEREST_AND_RENT', isSubtotal: true },
      { value: 'PERSONAL_DRAWINGS' },
      { value: 'SCHOOL_FEES' },
      { value: 'PERSONAL_INSURANCES' },
      { value: 'PERSONAL_EXPENDITURE', isSubtotal: true },
      { value: 'INCOME_TAX' },
      { value: 'NETT_GST' },
      { value: 'TOTAL_TAXATION', isSubtotal: true },
      { value: 'PCCR', isTotal: true },
    ],
    'Capital Items': [
      { value: 'NETT_PLANT_REPLACEMENT' },
      { value: 'OTHER_CAPITAL_EXPENDITURE', isNegative: true },
      { value: 'CAPITAL_INTRODUCED' },
      { value: 'CASH_RESULT', isTotal: true },
    ],
    'Adjustment for Non-Cash Items': [
      {
        value: 'DEPRECIATION_VEHICLES_PLANT_AND_MACHINERY',
        isNegative: true,
      },
      { value: 'FARM_CASH_IN' },
      { value: 'TOTAL_LIVESTOCK_VALUE_CHANGE' },
      { value: 'TOTAL_PRODUCE_ON_HAND_VALUE_CHANGE' },
      { value: 'TOTAL_FARM_INCOME', isSubtotal: true },
      { value: 'TOTAL_GROSS_INCOME', isSubtotal: true },
      { value: 'PCAR', isTotal: true },
    ],
  },
  W: {
    'Income Statement': [
      { value: 'SALES_REVENUES', isSubtotal: true },
      { value: 'COSTOF_SALES' },
      { value: 'MANUFACTURING_WAGES_SALARIES' },
      { value: 'TOTAL_COSTOF_SALES', isSubtotal: true },
      { value: 'GROSS_PROFIT', isSubtotal: true },
      { value: 'SELL_MARKETING_EXP' },
      { value: 'WAGES_SALARIES' },
      { value: 'OP_LEASE_RENT_EXP' },
      { value: 'BAD_DEBT_EXPENSE' },
      { value: 'OTHER_OP_EXPENSES' },
      {
        value: 'OPER_EXP_EXCL_DEP_AMOR',
        isSubtotal: true,
      },
      { value: 'OTHER_INCOME_EXPENSE', isSubtotal: true },
      { value: 'EBITDA', isTotal: true },
      { value: 'TOTAL_DEPRECIATION', isSubtotal: true },
      { value: 'TOTAL_AMORTISATION', isSubtotal: true },
      { value: 'EBIT', isTotal: true },
      { value: 'TOTAL_INT_EXP', isSubtotal: true },
      { value: 'NPB_TBF_SIGF_ITEMS', isTotal: true },
      { value: 'TOTAL_INCOME_TAX', isSubtotal: true },
      { value: 'TOTAL_SIGF_ITEMS', isSubtotal: true },
      { value: 'NET_PROFIT', isTotal: true },
    ],
    'Balance Sheet': [
      { value: 'TOTAL_TANGIBLE_ASSETS', isSubtotal: true },
      { value: 'TOTAL_INTANGIBLES_ASSETS', isSubtotal: true },
      { value: 'TOTAL_ASSETS', isTotal: true },
      { value: 'TOTAL_LIABILITIES', isSubtotal: true },
      { value: 'TOT_EQUITY_RESERVES', isSubtotal: true },
      { value: 'TOT_EQUITY_RES_LIABS', isTotal: true },
    ],
  },
}

export type StructuredKpi = {
  [key: string]: number | boolean | undefined | string
  isHeading?: boolean
  isNegative?: boolean
  isSubtotal?: boolean
  isTotal?: boolean
}

const useKpiStatement = (data: CustomerKpiListApiResponse, segment: string) => {
  const years = useMemo(() => {
    if (!data) {
      return []
    }
    return Array.from(new Set(data.map(({ periodTo }) => periodTo)))
      .sort()
      .map((date) => formatDate(date))
  }, [data])

  const arr = useMemo(() => {
    if (!data) return []

    const structuredData: Record<string, StructuredKpi[]> = {}
    try {
      const sectionKeys = Object.keys(HISTORICAL_ANALYSIS_REPORT[segment])
      for (const section of sectionKeys) {
        const measureData: Record<string, StructuredKpi> = {}

        const arr = [...(data ?? [])].filter(({ measure }) =>
          HISTORICAL_ANALYSIS_REPORT[segment][section]
            .map((x) => x.value)
            .includes(measure)
        )

        for (const { periodTo, label, measure, value } of arr) {
          const year = formatDate(periodTo)
          if (!measureData[measure]) {
            const { isNegative, isSubtotal, isTotal } = {
              ...HISTORICAL_ANALYSIS_REPORT[segment][section].find(
                ({ value }) => value === measure
              ),
            }
            if (measure) {
              measureData[measure] = {
                measure: label,
                isNegative,
                isSubtotal,
                isTotal,
              }
            }
          }
          measureData[measure][year] = Number(value)
        }

        structuredData[section] = HISTORICAL_ANALYSIS_REPORT[segment][section]
          .map((x) => x.value)
          .filter((measure) => Object.keys(measureData).includes(measure))
          .map((measure) => measureData[measure])
      }
    } catch {
      return []
    }

    return (
      Object.keys(structuredData)
        .map((section) => [
          { measure: section, isHeading: true },
          ...structuredData[section],
        ])
        // TODO: replace with flat(2) and test
        // biome-ignore lint:
        .reduce((acc, cur) => [...acc, ...cur], [])
        .flat()
    )
  }, [data, segment])

  return {
    pivot: arr,
    years,
  }
}

export default useKpiStatement
