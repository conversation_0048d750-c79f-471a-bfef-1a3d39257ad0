import React from 'react'
import { shallowEqual, useSelector } from 'react-redux'
import { useGetSelectedComparableSale } from '@store/hooks/useGetSelectedComparableSale'
import { uiSelectors } from '../../../../../store/ui'
import { ComparableSalePane } from '../../../../assets/comparison/ComparableSalePane'
import { SelectedSalePane } from './SelectedSalePane'

export interface SalesRightPaneProps {
  valuationId: string
}

export const SalesRightPane = ({ valuationId }: SalesRightPaneProps) => {
  const salesAndListingsCurrentTab = useSelector(
    uiSelectors.getSalesAndListingsCurrentTab,
    shallowEqual
  )
  const selectedSale = useSelector(uiSelectors.getSelectedSale, shallowEqual)
  const selectedSaleRowIndex = useSelector(
    uiSelectors.getSelectedSaleRowIndex,
    shallowEqual
  )
  const selectedComparableSaleId = useSelector(
    uiSelectors.getSelectedComparableSaleId,
    shallowEqual
  )
  const valuationAdjustment = useSelector(
    uiSelectors.getComparableSaleAdjustmentSum
  )

  const selectedComparableSale = useGetSelectedComparableSale(
    valuationId,
    selectedComparableSaleId
  )

  if (salesAndListingsCurrentTab === 'COMPARISON') {
    return selectedComparableSale ? (
      <ComparableSalePane
        comparableSale={selectedComparableSale}
        valuationAdjustment={valuationAdjustment}
      />
    ) : null
  }
  return selectedSale ? (
    <SelectedSalePane sale={selectedSale} saleRowIndex={selectedSaleRowIndex} />
  ) : null
}
