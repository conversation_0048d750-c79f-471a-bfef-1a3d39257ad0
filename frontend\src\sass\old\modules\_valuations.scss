@import 'valuation/_chart-container.scss';
@import 'valuation/_trading-group-emissions.scss';

.no-tg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9998;
  display: flex;
  justify-content: center;
  align-items: center;

  .ant-alert-warning {
    margin: $half $half 0 $half;
  }

  .button-group {
    margin-left: $half;
    display: flex;
    margin-bottom: $half;
    margin-top: -$quarter;

    button:not(:last-child) {
      margin-right: $quarter;
    }
  }

  .no-tg-card {
    width: 500px;
  }

  p:last-child {
    padding-bottom: 0;
    margin-bottom: 0;
  }
}

.export-card {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.widget-div {
  padding: $full $full $full $full;

  &:empty {
    padding: 0;
  }
}

.trading-group-info {
  margin-bottom: $full;
}

.valuation-dvr:not(:last-child) {
  margin-bottom: $full;
}

.resource-consent-table {
  span {
    font-weight: 600;
    color: #666;
  }

  .ant-alert {
    p {
      padding: 0;
      margin: 0;
    }
  }

  .resource-consents-text {
    ul {
      list-style-position: outside;

      li {
        color: #777;
        font-style: italic;

        &::before {
          display: inline-block;
          color: #bcbcbc;
          font-weight: 600;
          width: 75px;
          font-style: normal;
        }

        &.issue::before {
          content: 'ISSUE';
        }

        &.expiry::before {
          content: 'EXPIRY';
        }

        &.holder::before {
          content: 'HOLDER';
        }

        &.location::before {
          content: 'ADDRESS';
        }

        &.description::before {
          content: 'DESC.';
        }
      }
    }
  }

  .resource-consents-tags {
    display: flex;
    flex-flow: row wrap;
    align-items: center;

    .ant-tag {
      &:not(:last-child) {
        margin-bottom: 4px;
      }
    }
  }
}

.valuation-header {
  padding: $half;
  padding-bottom: $quarter;
}

.valuation-sub-header {
  padding-left: 24px;
  margin-left: 0.8em;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  a:hover,
  a:active {
    text-decoration: none;
  }
}

.transfer-valuation-modal {
  .transfer-valuation-modal-items {
    width: 100%;

    .transfer-valuation-modal-item:not(:last-child) {
      margin-bottom: $half;
    }

    .transfer-valuation-alert {
      @include row-flex;
      justify-content: space-between;

      .transfer-valuation-alert-text {
        margin-top: $quarter;
      }
    }
  }
}
