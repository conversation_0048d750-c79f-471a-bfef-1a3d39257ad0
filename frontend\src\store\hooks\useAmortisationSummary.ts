import { flow } from 'lodash'
import { useMemo } from 'react'
import type { Facility } from '@store/services/sdk'
import { LOAN_GRAPH_FIELDS } from '@util/const'
import { access } from '@util/helpers'

type StackedBarData = {
  year: number
  value: number | null
  measure: string
}

const GRAPH_FIELDS = LOAN_GRAPH_FIELDS.reduce<Record<string, Facility[]>>(
  (record, cur) => {
    record[cur] = []
    return record
  },
  {}
)

const groupByAccountType = (
  record: Record<string, Facility[]>,
  facility: Facility
) => ({
  ...record,
  [facility.accountType]: [...record[facility.accountType], facility],
})

const atOrWithinTenure = (year: number) => (facility: Facility) =>
  Number(facility.endYear) >= year

const withinTenure = (year: number) => (facility: Facility) =>
  Number(facility.endYear) > year

const sumByAmortisation =
  (years: number) =>
  (
    initial: number | null,
    { accountType, limitFloat, annualAmortisation }: Facility
  ) => {
    const sum = initial ?? 0

    if (accountType === 'Limit') return sum + limitFloat

    return (
      sum + Math.max(limitFloat - years * Number(annualAmortisation ?? 0), 0)
    )
  }

const graphDataByYear =
  (measure: string, facilities: Facility[]) =>
  (data: StackedBarData[], year: number, i: number): StackedBarData[] => {
    const sumByYear = sumByAmortisation(i)
    const emptyValue = null

    const ceiling = facilities
      .filter(atOrWithinTenure(year))
      .reduce(sumByYear, emptyValue)

    const floor = facilities
      .filter(withinTenure(year))
      .reduce(sumByYear, emptyValue)

    const values = [...new Set([ceiling, floor])].map((value) => ({
      value,
      measure,
      year,
    }))

    return [...data, ...values]
  }

function useAmortisationSummary(facilities: Facility[]) {
  const timeline = useMemo(() => {
    const maxYear = Math.max(
      ...facilities.map(flow(access('maturityYear'), Number))
    )
    const currentYear = new Date().getFullYear()
    const yearDiff = Math.abs(currentYear - maxYear)
    const length = yearDiff > 5 && Number.isFinite(yearDiff) ? yearDiff : 5

    return Array.from({ length }, (_, idx) => currentYear + idx)
  }, [facilities])

  const amortisations: StackedBarData[] = useMemo(
    () =>
      Object.entries(
        facilities.reduce(groupByAccountType, GRAPH_FIELDS)
      ).flatMap(([measure, measureFacilities]) =>
        timeline.reduce(graphDataByYear(measure, measureFacilities), [])
      ),
    [facilities, timeline]
  )

  return amortisations
}

export default useAmortisationSummary
