import type { FeatureCollection, Geometry } from 'geojson'
import type { LandAsset } from '@models/assets/LandAsset'
import type { TitleFeatureCollection } from '@models/title/TitleFeatureCollection'
import type { Address, Valuation } from '../../types'
import type { DistrictValuationRoll } from '../dvr/DistrictValuationRoll'

export interface ValuationSummary {
  valuation: Valuation
  address: Address
  dvr: DistrictValuationRoll[]
  titles: TitleFeatureCollection
  nonTitledLand: FeatureCollection<Geometry, LandAsset>
}
