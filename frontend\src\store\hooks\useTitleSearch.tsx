import { skipToken } from '@reduxjs/toolkit/dist/query'
import { Col, Input, Row, Slider } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import { Widget } from '@components/generic/Widget'
import type { PaginatedResponse } from '@models/generic/PaginatedResponse'
import type { TitleFeature } from '@models/title/TitleFeatureCollection'
import { ADDRESS_SEARCH_PLACEHOLDER } from '@util/language'
import { useDebounce } from '@util/useDebounce'
import { useSearchTitleQuery } from '../services/title'

const { Search } = Input

export interface TitleSearchPayload {
  match: string
  page: number
  addressId: string
  radius: number
}

// This is a cleaned up hook which should eventually replace all title searches
export const useTitleSearch = (skip?: boolean, id?: string) => {
  // TODO: TitleSearchSQLGenerator doesn't actually return all the columns present on TitleFeature!
  const [searchResults, setSearchResults] =
    useState<PaginatedResponse<TitleFeature[]>>()

  const [latLng, setLatLng] = useState<{
    lat: number | undefined
    lng: number | undefined
  }>({
    lat: undefined,
    lng: undefined,
  })
  const [page, setPage] = useState<number>(1)
  const [match, setMatch] = useState<string>('')
  const [radius, setRadius] = useState<number>(5)
  const debouncedMatch = useDebounce(match, 200)

  const shouldSkip =
    (latLng?.lat === undefined && latLng?.lng === undefined) ||
    latLng === undefined ||
    skip

  const { data, isFetching } = useSearchTitleQuery(
    shouldSkip
      ? skipToken
      : {
          match: debouncedMatch || '',
          page,
          latLng,
          radius: radius * 1e3,
        }
  )

  useEffect(() => {
    setSearchResults(data)
  }, [data])

  const searchElem = useMemo(() => {
    return (
      <Widget key={id} title="Radius Title Search">
        <Row style={{ margin: '0.5em 0 1em 0' }}>
          <Col span={18}>
            <Slider
              step={5}
              value={radius}
              onChange={(e) => {
                setRadius(e)
                setPage(1)
              }}
              max={50}
              min={0}
            />
          </Col>
          <Col span={4}>
            <Input
              addonAfter="km"
              type="number"
              style={{ minWidth: '100px' }}
              value={radius}
              onChange={(e) => {
                const input = e.target.value.replace(/[^0-9]/g, '')
                setRadius(Number(input))
              }}
            />
          </Col>
        </Row>
        <Search
          onChange={(e) => {
            setMatch(e.target.value)
            setPage(1)
          }}
          value={match}
          loading={isFetching}
          placeholder={ADDRESS_SEARCH_PLACEHOLDER}
        />
      </Widget>
    )
  }, [isFetching, radius, match, id])

  return {
    searchElem,
    searchResults,
    page,
    setPage,
    setLatLng,
    setMatch,
    isFetching,
    radius,
  }
}
