import { Descriptions, Input, Space, Table } from 'antd'
import React from 'react'
import type {
  TitleFeature,
  TitleFeatureCollection,
} from '@models/title/TitleFeatureCollection'

interface ValuationPageTitleWidgetProps {
  valuationTitles: TitleFeatureCollection | undefined
  memorialState: { [titleId: string]: boolean }
  toggleTitle: (titleNumber: string) => void
}

export const ValuationPageTitleWidget = (
  props: ValuationPageTitleWidgetProps
) => {
  const { valuationTitles, memorialState, toggleTitle } = props

  return (
    <div>
      <div className="agrigis-table">
        <Table
          size="small"
          dataSource={valuationTitles?.features ?? []}
          rowKey={(row) => row.properties.titleNo}
          columns={[
            {
              dataIndex: ['properties', 'titleNo'],
              title: 'Title',
              render: (text: string, record: TitleFeature) => {
                return (
                  <Space>
                    <Input
                      type="checkbox"
                      value={record.properties.titleNo}
                      defaultChecked={memorialState[record.properties.titleNo]}
                      onClick={(e) => {
                        toggleTitle((e.target as HTMLInputElement).value)
                      }}
                    />
                    <span>{text}</span>
                  </Space>
                )
              },
            },
            {
              dataIndex: ['properties', 'estateDescription'],
              title: 'Description',
            },
            {
              dataIndex: ['properties', 'surveyArea'],
              title: 'Area (Ha)',
              render: (text: string) => {
                return text ? (Number(text) / 1e4).toFixed(2) : ''
              },
            },
          ]}
          expandable={{
            expandedRowRender: (record: TitleFeature) => (
              <Descriptions
                column={1}
                bordered
                className="agrigis-table-expanded-row"
              >
                {record.properties.owners ? (
                  <Descriptions.Item label="Owners">
                    {record.properties.owners}
                  </Descriptions.Item>
                ) : (
                  <></>
                )}
                {record.properties.status ? (
                  <Descriptions.Item label="Status">
                    {record.properties.status}
                  </Descriptions.Item>
                ) : (
                  <></>
                )}
                {record.properties.guaranteeStatus ? (
                  <Descriptions.Item label="Guarantee Status">
                    {record.properties.guaranteeStatus}
                  </Descriptions.Item>
                ) : (
                  <></>
                )}
                {record.properties.spatialExtentsShared ? (
                  <Descriptions.Item label="Spatial Extents Shared?">
                    {record.properties.spatialExtentsShared}
                  </Descriptions.Item>
                ) : (
                  <></>
                )}
              </Descriptions>
            ),
          }}
          summary={() => {
            return (
              <>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={1} />
                  <Table.Summary.Cell index={2}> </Table.Summary.Cell>
                  <Table.Summary.Cell index={3}> </Table.Summary.Cell>
                  <Table.Summary.Cell index={4}>
                    {(
                      Number(
                        (valuationTitles?.features ?? [])
                          .map((x) => x.properties.surveyArea)
                          .reduce((a, b) => a + b, 0)
                      ) / 1e4
                    ).toFixed(2)}
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </>
            )
          }}
        />
      </div>
    </div>
  )
}
