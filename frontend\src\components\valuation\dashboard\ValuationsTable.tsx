import { CheckCircleFilled, SearchOutlined } from '@ant-design/icons'
import { Descriptions, Space, Table, Tag } from 'antd'
import React, { useCallback, useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import type { RootState } from '../../../store'
import { uiSelectors } from '../../../store/ui'
import { setFilterValue } from '../../../store/ui/actions'
import { Widget } from '../../generic/Widget'
import type { ValuationSearch } from '@store/services/sdk'

interface ValuationsTableProps {
  valuations: ValuationSearch[]
  valuationsCount: number
  creatorDisabled?: boolean
  isLoading: boolean
}

const selector = (state: RootState, props: ValuationsTableProps) => {
  const type = props?.creatorDisabled ? 'user' : 'valuations'
  return {
    filterState: uiSelectors.getFilterState(state, type),
    layoutState: uiSelectors.getLayoutState(state, 'valuationDashboard'),
    ...props,
  }
}

export const ValuationsTable = (props: ValuationsTableProps) => {
  const dispatch = useDispatch()

  const filterDispatch = useCallback(
    (payload: { type: string; value: string | number }) => {
      dispatch(
        setFilterValue({
          pageName: props?.creatorDisabled ? 'user' : 'valuations',
          filterKey: payload?.type,
          filterValue: payload?.value,
        })
      )
    },
    [dispatch, props?.creatorDisabled]
  )

  const {
    valuations,
    valuationsCount,
    creatorDisabled,
    filterState,
    isLoading,
  } = useSelector((state: RootState) => selector(state, props))

  const elem = useMemo(() => {
    let columns = [
      {
        dataIndex: 'address',
        title: 'Address',
        render: (text: string, record: ValuationSearch) => {
          return (
            <Space>
              <Link
                className="action-text"
                to={`/valuation/${record.addressId}/${record.valuationId}/`}
              >
                {record.completedDate ? (
                  <CheckCircleFilled
                    style={{
                      color: 'rgba(0, 150, 0, 0.75)',
                    }}
                  />
                ) : (
                  ''
                )}{' '}
                {text}
              </Link>
              {record.valuationType === 'RVR' ? (
                <Tag color="gold">RVR</Tag>
              ) : null}
            </Space>
          )
        },
      },
      {
        dataIndex: 'tradingGroupName',
        title: 'Trading Group',
        render: (text: string, record: ValuationSearch) => {
          if (!text) {
            return ''
          }
          return (
            <Link to={`/trading-groups/${record.tradingGroupId ?? ''}`}>
              {text}
            </Link>
          )
        },
      },
      {
        dataIndex: 'highestAndBestUse',
        title: 'Best Use',
        render: (highestAndBestUse: string | undefined) => {
          return highestAndBestUse ?? 'Unknown'
        },
      },
      {
        dataIndex: 'createdDate',
        title: 'Created',
        render: (text: string) => {
          return text ? text.toString().slice(0, 10) : ''
        },
      },
      {
        dataIndex: 'creatorName',
        title: 'Creator',
        creatorDisabled: true,
      },
      {
        dataIndex: 'totalHa',
        title: 'Total Ha',
        render: (_: string, record: ValuationSearch) =>
          Number(record?.totalHa ?? 0)
            .toFixed(2)
            .toLocaleString(),
      },
    ]

    if (creatorDisabled) {
      columns = columns.filter((x) => !x.creatorDisabled)
    }

    return (
      <Widget icon={<SearchOutlined />} title="Search Results">
        <div className="agrigis-table">
          <Table
            rowKey={(row) => row.valuationId}
            size="small"
            loading={isLoading}
            columns={columns}
            pagination={{
              simple: true,
              total: valuationsCount || 0,
              pageSize: 15,
              current: filterState.page ?? 1,
              onChange: (e) => {
                filterDispatch({
                  type: 'page',
                  value: Number(e),
                })
              },
            }}
            dataSource={valuations || []}
            expandable={{
              expandedRowRender: (record) => (
                <Descriptions
                  column={1}
                  bordered
                  className="agrigis-table-expanded-row"
                >
                  <Descriptions.Item label="Creator">
                    {record.creatorName}
                  </Descriptions.Item>
                  <Descriptions.Item label="Valuation Description">
                    {record.valuationName}
                  </Descriptions.Item>
                  <Descriptions.Item label="Valuation Reference">
                    {record.valuationReference}
                  </Descriptions.Item>
                </Descriptions>
              ),
            }}
          />
        </div>
      </Widget>
    )
  }, [
    creatorDisabled,
    isLoading,
    valuationsCount,
    filterState.page,
    valuations,
    filterDispatch,
  ])

  return elem
}
