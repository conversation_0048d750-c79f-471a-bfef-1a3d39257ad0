import { useCallback, useMemo, useState } from 'react'
import { not } from '@util/helpers'

// Run on error from horrible feature type
type ItemId = number | string
type Item = { id?: ItemId }

const matchingId = (match: Item) => (item: Item) =>
  String(match.id) === String(item.id)

const useToggleItems = <T extends Item>(items: T[]) => {
  const [toggled, setToggled] = useState<T[]>([...new Set(items)])

  const toggleItem = useCallback((itemToToggle: T) => {
    setToggled((prevToggled) => {
      const matchItem = matchingId(itemToToggle)
      const hasItem = prevToggled.some(matchItem)
      if (hasItem) {
        return prevToggled.filter(not(matchItem))
      }
      return [...prevToggled, itemToToggle]
    })
  }, [])

  const toggleItems = useCallback(
    (toggleItems: T[]) => {
      toggleItems.forEach(toggleItem)
    },
    [toggleItem]
  )

  const setItems = useCallback((toggleItems: T[]) => {
    setToggled(toggleItems)
  }, [])

  const clear = useCallback(() => {
    setToggled([])
  }, [])

  const toggledIds = useMemo(
    () => toggled.map(({ id }) => String(id)),
    [toggled]
  )

  return {
    clear,
    toggled,
    toggledIds,
    toggleItem,
    toggleItems,
    setItems,
  }
}

export default useToggleItems
