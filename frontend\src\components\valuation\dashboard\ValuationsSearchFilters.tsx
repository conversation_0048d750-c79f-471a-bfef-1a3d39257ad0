import { DeleteOutlined, FilterOutlined } from '@ant-design/icons'
import { Button, DatePicker, Input, Select, message } from 'antd'
import moment from 'moment'
import React, { useCallback, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { REGION_OPTIONS } from '@components/sales/helpers'
import type { RootState } from '../../../store'
import { useHighestAndBestUseOptions } from '../../../store/features/assets/hooks'
import { useGetValuersQuery } from '../../../store/services/valuations'
import { uiSelectors } from '../../../store/ui'
import { clearFilter, setFilterValue } from '../../../store/ui/actions'
import { useDebounce } from '../../../util/useDebounce'
import { ButtonWidget } from '../../generic'
import { Widget } from '../../generic/Widget'

interface ValuationsSearchFiltersProps {
  creatorDisabled?: boolean
}
const { RangePicker } = DatePicker

const selector = (state: RootState, props: ValuationsSearchFiltersProps) => {
  const layoutState = uiSelectors.getLayoutState(state, 'valuationDashboard')
  const type = layoutState.selectedView === 'USER' ? 'user' : 'valuations'
  return {
    filterState: uiSelectors.getFilterState(state, type),
    ...props,
    type,
  }
}

export const ValuationsSearchFilters = (
  props: ValuationsSearchFiltersProps
) => {
  const { creatorDisabled, filterState, type } = useSelector(
    (state: RootState) => selector(state, props)
  )

  const dispatch = useDispatch()

  const filterDispatch = useCallback(
    (payload: { type: string; value: string }) => {
      dispatch(
        setFilterValue({
          pageName: type,
          filterKey: payload?.type,
          filterValue: payload?.value,
        })
      )
    },
    [dispatch, type]
  )

  const highestAndBestUseOptions = useHighestAndBestUseOptions()

  const { data: valuers } = useGetValuersQuery(undefined)

  const [creatorsOptions, setCreatorsOptions] = useState<string[]>()

  const [match, setMatch] = useState<string>('')
  const [addressMatch, setAddressMatch] = useState<string>('')

  const debouncedMatch = useDebounce(match, 200)
  const debouncedAddressMatch = useDebounce(addressMatch, 200)

  useEffect(() => {
    filterDispatch({ type: 'match', value: debouncedMatch })
  }, [debouncedMatch, filterDispatch])

  useEffect(() => {
    filterDispatch({ type: 'addressMatch', value: debouncedAddressMatch })
  }, [debouncedAddressMatch, filterDispatch])

  useEffect(() => {
    setCreatorsOptions((prev) => {
      if (!prev || prev.length === 0) {
        return valuers?.map((x) => x.username) || []
      }
      return prev || []
    })
  }, [valuers])

  return (
    <Widget
      title="Filters"
      icon={<FilterOutlined />}
      extra={
        <ButtonWidget>
          <Button
            type="default"
            icon={<DeleteOutlined />}
            onClick={() => {
              setMatch('')
              setAddressMatch('')
              dispatch(clearFilter({ pageName: type }))
              void message.success('Settings successfully cleared.')
            }}
          >
            Reset Filters
          </Button>
        </ButtonWidget>
      }
    >
      <div className="agrigis-filters">
        <div className="control-container select">
          <span className="control-name">Created Date</span>
          <RangePicker
            value={[
              moment(filterState.minCreatedDate),
              moment(filterState.maxCreatedDate),
            ]}
            onChange={(_dates, dateStrings) => {
              if (dateStrings[0]) {
                filterDispatch({
                  type: 'minCreatedDate',
                  value: dateStrings[0],
                })
              }
              if (dateStrings[1]) {
                filterDispatch({
                  type: 'maxCreatedDate',
                  value: dateStrings[1],
                })
              }
            }}
          />
        </div>
        <div className="control-container select">
          <span className="control-name">Completed Date</span>
          <RangePicker
            value={[
              moment(filterState.minCompletedDate),
              moment(filterState.maxCompletedDate),
            ]}
            onChange={(_dates, dateStrings) => {
              if (dateStrings[0]) {
                filterDispatch({
                  type: 'minCompletedDate',
                  value: dateStrings[0],
                })
              }
              if (dateStrings[1]) {
                filterDispatch({
                  type: 'maxCompletedDate',
                  value: dateStrings[1],
                })
              }
            }}
          />
        </div>
        <div className="control-container select">
          <span className="control-name">Highest & Best Use</span>
          <Select
            placeholder="Select one or more..."
            size="small"
            mode="multiple"
            defaultValue={[]}
            options={highestAndBestUseOptions}
            onChange={(e) => {
              filterDispatch({
                type: 'highestAndBestUses',
                value: e,
              })
            }}
            value={filterState.highestAndBestUses}
          />
        </div>
        <div className="control-container select">
          <span className="control-name">TLA/District</span>
          <Select
            placeholder="Select one or more..."
            size="small"
            mode="multiple"
            options={REGION_OPTIONS}
            onChange={(e) => {
              filterDispatch({ type: 'tla', value: e })
            }}
            value={filterState.tla}
          />
        </div>
        <div className="control-container select">
          <span className="control-name">State</span>
          <Select
            placeholder="Select a state"
            options={[
              { title: 'ALL', value: 'ALL' },
              { title: 'PENDING', value: 'PENDING' },
              { title: 'COMPLETED', value: 'COMPLETED' },
            ]}
            onChange={(e) => {
              filterDispatch({
                type: 'valuationState',
                value: e,
              })
            }}
            value={filterState.valuationState}
          />
        </div>
        {!creatorDisabled ? (
          <div className="control-container select">
            <span className="control-name">Creators</span>
            <Select
              placeholder="Select one or more..."
              size="small"
              mode="multiple"
              defaultValue={[]}
              options={creatorsOptions?.map((x) => {
                return { value: x.toLowerCase(), label: x }
              })}
              onChange={(e) => {
                filterDispatch({ type: 'creators', value: e })
              }}
              value={filterState.creators}
            />
          </div>
        ) : (
          <></>
        )}
        <div className="control-container full">
          <span className="control-name">Keyword Search</span>
          <Input
            onChange={(e) => setMatch(e.target.value)}
            placeholder={
              filterState?.match
                ? filterState?.match
                : 'Search by Trading Group, Valuation Name or Valuation Reference...'
            }
          />
        </div>
        <div className="control-container full">
          <span className="control-name">Address Search</span>
          <Input
            onChange={(e) => setAddressMatch(e.target.value)}
            placeholder={
              filterState?.addressMatch
                ? filterState?.addressMatch
                : 'Start typing an address...'
            }
          />
        </div>
      </div>
    </Widget>
  )
}
