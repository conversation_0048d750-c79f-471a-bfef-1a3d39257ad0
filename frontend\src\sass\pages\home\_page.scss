@media (max-width: 1280px) {
  .home-page-right-pane {
    display: none;
  }

  .home-page-pane-widget {
    flex: 100%;
  }
}

.Layout {
  // @extend .sales-page;
  overflow-x: hidden;
  display: flex;
  flex-direction: row;
  height: 100%;
  justify-content: space-between;

  > div {
    border-left: 1px solid $grey-1;
  }

  .home-page-right-pane {
    overflow-x: hidden;
  }

  .home-page-left-pane,
  .home-page-right-pane {
    height: 100%;
    transition: all 0.5s ease;
    overflow-y: scroll;
  }

  .home-page-pane-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .resizeable-container {
    transition: none !important;

    > .home-page-pane-container {
      display: block;
    }
  }

  .home-page-pane-widget {
    flex-grow: 1;
    background-color: rgba(255, 255, 255, 0.9);
    margin: $half $quarter;
  }

  .home-page-pane-inner-widget {
    &:not(:last-child) {
      margin-bottom: $half;
    }

    &:not(:first-child) {
      margin-top: $full;
    }
  }

  .home-page-parent-wrapper {
    display: flex;
    align-items: flex-start;

    > div {
      flex: 50%;
      flex-grow: 1;
    }

    a {
      font-size: 13px;
    }
  }

  .home-page-getting-started {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    ul {
      li {
        &:before {
          content: '>';
          margin-right: 8px;
        }
      }
    }

    > .agrigis-widget {
      flex: 40%;
      margin: $quarter;
      flex-grow: 1;
    }
  }

  .home-page-wrapper {
    display: flex;
    padding: 0 $half;
  }
}

.search-box-text {
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
  margin-bottom: $quarter;
}

.search-checkbox {
  margin-right: 10px;
}
