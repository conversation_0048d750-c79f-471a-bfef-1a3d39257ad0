import { ExclamationCircleOutlined } from '@ant-design/icons'
import { Alert, Descriptions, Table } from 'antd'
import React, { useMemo } from 'react'
import { useCallback } from 'react'
import type { TitleFeatureCollection } from '@models/title/TitleFeatureCollection'
import type { DistrictValuationRoll } from '../../../../../models/dvr/DistrictValuationRoll'
import { Widget } from '../../../../generic/Widget'

interface ValuationPageDvWidgetProps {
  valuationDvr: DistrictValuationRoll[]
  valuationTitles: TitleFeatureCollection | undefined
}

export const ValuationPageDvWidget = (props: ValuationPageDvWidgetProps) => {
  const { valuationDvr, valuationTitles } = props

  const getSum = useCallback(
    (key: keyof DistrictValuationRoll) => {
      return (valuationDvr || [])
        .map((x) => x[key])
        .reduce((a, b) => Number(a) + Number(b), 0)
    },
    [valuationDvr]
  )

  const missingTitles = useMemo(() => {
    const titles: { [titleNo: string]: number } = {}
    for (const title of valuationTitles?.features || []) {
      titles[title.properties.titleNo] = 1
    }
    for (const dvr of valuationDvr) {
      for (const title of dvr.linzTitles || []) {
        if (!titles[title.titleNo]) {
          titles[title.titleNo] = 1
        } else {
          titles[title.titleNo]++
        }
      }
    }
    return Object.keys(titles).filter((k) => titles[k] === 1)
  }, [valuationDvr, valuationTitles])

  const missingTitleAlert = useMemo(() => {
    if (missingTitles.length === 0) {
      return <></>
    }

    return (
      <Alert
        type="warning"
        icon={<ExclamationCircleOutlined />}
        showIcon={true}
        className="agrigis-alert"
        message="Potentially Missing Titles"
        description={
          <span>
            The titles which have been selected do not include all titles
            defined by the district valuation roll(s). Please double check your
            title groups are correct, the missing titles are:
            {missingTitles ? (
              <ol>
                {missingTitles.map((x) => (
                  <li key={x}>{x}</li>
                ))}
              </ol>
            ) : (
              ''
            )}
          </span>
        }
      />
    )
  }, [missingTitles])

  return (
    <>
      {missingTitleAlert}
      <Widget title="Council Valuation Details">
        <div className="agrigis-table">
          <Table
            size="small"
            rowKey={(row) => row.dvrId}
            dataSource={valuationDvr}
            pagination={false}
            columns={[
              { dataIndex: 'valRef', title: 'Val Ref' },
              { dataIndex: 'valDate', title: 'Date' },
              { dataIndex: 'landUseDesc', title: 'Land Use' },
            ]}
            expandable={{
              expandedRowRender: (record) => (
                <Descriptions
                  column={1}
                  bordered
                  className="agrigis-table-expanded-row"
                >
                  <Descriptions.Item label="Legal Desc">
                    {record.legalDesc}
                  </Descriptions.Item>
                  <Descriptions.Item label="Address">
                    {record.fullAddress}
                  </Descriptions.Item>
                  <Descriptions.Item label="Improvement">
                    {record.improvement}
                  </Descriptions.Item>
                  <Descriptions.Item label="Land Zone">
                    {record.landZoneDesc}
                  </Descriptions.Item>
                  <Descriptions.Item label="Council">
                    {record.tlaName}
                  </Descriptions.Item>
                  <Descriptions.Item label="Titles">
                    {record.linzTitles?.map((x) => x.titleNo).join(', ')}
                  </Descriptions.Item>
                </Descriptions>
              ),
            }}
          />
        </div>
      </Widget>
      <Widget title="Council Valuation Summary">
        <div className="agrigis-table">
          <Table
            size="small"
            rowKey={(row) => row.dvrId}
            pagination={false}
            dataSource={valuationDvr}
            columns={[
              { dataIndex: 'valRef', title: 'Val Ref' },
              {
                dataIndex: 'iv',
                title: 'IMPROV. ($K)',
                render: (text: string) => {
                  return text ? (Number(text) / 1e3).toLocaleString() : ''
                },
              },
              {
                dataIndex: 'lv',
                title: 'Land ($K)',
                render: (text: string) => {
                  return text ? (Number(text) / 1e3).toLocaleString() : ''
                },
              },
              {
                dataIndex: 'cv',
                title: 'CV ($K)',
                render: (text: string) => {
                  return text ? (Number(text) / 1e3).toLocaleString() : ''
                },
              },
              {
                dataIndex: 'landArea',
                title: 'Area (Ha)',
                render: (text: string) => {
                  return text
                    ? Number((Number(text) / 1e4).toFixed(2)).toLocaleString()
                    : ''
                },
              },
              { dataIndex: 'floorArea', title: 'Floor (M2)' },
            ]}
            summary={() => {
              return (
                <>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={1}>
                      <i>Total</i>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={2}>
                      {(Number(getSum('iv')) / 1e3).toLocaleString()}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3}>
                      {(Number(getSum('lv')) / 1e3).toLocaleString()}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={4}>
                      {(Number(getSum('cv')) / 1e3).toLocaleString()}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={5}>
                      {Number(Number(getSum('landArea')) / 1e4)
                        .toFixed(2)
                        .toLocaleString()}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={6}>
                      {getSum('floorArea')?.toLocaleString()}
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                </>
              )
            }}
          />
        </div>
      </Widget>
    </>
  )
}
