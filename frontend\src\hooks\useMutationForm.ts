import type {
  BaseQueryFn,
  MutationDefinition,
} from '@reduxjs/toolkit/dist/query'
import type { UseMutation } from '@reduxjs/toolkit/dist/query/react/buildHooks'
import { Form, message } from 'antd'
import type { ValidateErrorEntity } from 'rc-field-form/lib/interface'
import { useCallback } from 'react'
import { getQueryErrorMessage } from '@util/error'

/**
  * Pass an RTK mutation hook to return Ant Design Form instance and form submission handlers.
 * @example
 *
    const { form, ...formProps } = useMutationForm(useDocumentPartialUpdateMutation)
    return <Form form={form} {...formProps} }} />
 */
function useMutationForm<U, V extends BaseQueryFn, W>(
  hook: UseMutation<MutationDefinition<U, V, string, W>>,
  options?: {
    onSuccess?: (response: W) => void
    reset?: boolean
  }
) {
  const [mutation, status] = hook()
  const [form] = Form.useForm<U>()

  const onFinish = useCallback(
    async (...args: Parameters<typeof mutation>) => {
      try {
        const res = await mutation(...args).unwrap()
        await message.success('Saved successfully.')

        if (options?.onSuccess) options.onSuccess(res)
        if (options?.reset) form.resetFields()

        const recordUploadInput = document.getElementById(
          'addRecordFormUploadInput'
        ) as HTMLInputElement
        if (recordUploadInput) {
          recordUploadInput.value = ''
        }
      } catch (err) {
        const errorMessage = getQueryErrorMessage(err)
        await message.error(errorMessage)
      }
    },
    [form, mutation, options]
  )

  const onFinishFailed = useCallback(
    ({ errorFields }: ValidateErrorEntity<U>) => {
      form.setFields(errorFields)
      void message.error(
        'Validation failed, please ensure all fields have no errors.'
      )
    },
    [form]
  )

  return {
    form,
    status,
    onFinish,
    onFinishFailed,
  }
}

export default useMutationForm
