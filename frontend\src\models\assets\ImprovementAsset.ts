import { BN, fromBigNumber, toBigNumber } from '@types'
import type { Asset } from './Asset'
import type { ImprovementType } from './ValuationTypes'

export interface SerializableImprovementAsset extends Asset {
  id?: number
  secondaryHighestAndBestUseType?: number

  areaOverrideM2: string

  improvementType: ImprovementType | undefined

  customImprovementType?: string

  dollarPerUnit: string

  value: string

  dateBuilt: string

  improvementBuildingOutlines: number[]

  comments: string

  condition: string

  useCustomUnit: boolean

  metricUnit: string
  metricValue?: string
}

export function deserializeImprovementAsset(
  improvementAsset: SerializableImprovementAsset
) {
  const { areaOverrideM2, dollarPerUnit, value, metricValue, ...rest } =
    improvementAsset

  return {
    ...rest,
    metricValue: metricValue ? new BN(metricValue) : undefined,
    ...toBigNumber({
      areaOverrideM2,
      dollarPerUnit,
      value,
    }),
  }
}

export type ImprovementAsset = ReturnType<typeof deserializeImprovementAsset>

export function serializeImprovementAsset(improvementAsset: ImprovementAsset) {
  const { areaOverrideM2, dollarPerUnit, value, metricValue, ...rest } =
    improvementAsset

  return {
    ...rest,
    metricValue: metricValue ? metricValue.toString() : undefined,
    ...fromBigNumber({
      areaOverrideM2,
      dollarPerUnit,
      value,
    }),
  }
}

export const updateImprovementAssetSummaryValues = () => {
  return
}

export function improvementAssetMatches(
  first: ImprovementAsset,
  second: ImprovementAsset
) {
  if (first.improvementType?.id !== second.improvementType?.id) {
    return false
  }
  if (first.customImprovementType !== second.customImprovementType) {
    return false
  }
  if (first.dateBuilt !== second.dateBuilt) {
    return false
  }
  if (first.condition !== second.condition) {
    return false
  }
  for (const outlineId of first.improvementBuildingOutlines) {
    if (second.improvementBuildingOutlines.indexOf(outlineId) === -1) {
      return false
    }
  }
  return true
}
