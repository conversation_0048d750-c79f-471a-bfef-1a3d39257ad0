import { DeleteOutlined } from '@ant-design/icons'
import { Button, Space, Table, message } from 'antd'
import React, { useCallback, useState } from 'react'
import { Widget } from '@components/generic/Widget'
import { useGenericFileUpload } from '@store/hooks/useFileUpload'
import {
  useDeleteConsentFileMutation,
  useGetConsentFilesQuery,
  useGetValuationSummaryQuery,
  useUploadConsentFileMutation,
} from '@store/services/valuations'
import type { ConsentFile } from '@models/valuations/ConsentFile'
import { toURL } from '@util'
import type { FileListState } from '@util/types'

export interface ValuationConsentRelatedDocumentWidgetProps {
  valuationId: string
  disabled?: boolean
}

export const ValuationConsentRelatedDocumentWidget = ({
  valuationId,
  disabled,
}: ValuationConsentRelatedDocumentWidgetProps) => {
  const { data: valuationSummary } = useGetValuationSummaryQuery(valuationId)

  const [uploading, setUploading] = useState(false)
  const [uploadConsentFile] = useUploadConsentFileMutation()
  const [deleteConsentFile] = useDeleteConsentFileMutation()

  const doUploadFiles = useCallback(
    async (fileListState: FileListState) => {
      setUploading(true)
      for (const fileState of Object.values(fileListState)) {
        const { file, description } = fileState
        if (file.size / 1e6 > 20) {
          void message.error(
            'File failed to upload, please ensure files are less than 20MB'
          )
          continue
        }
        await uploadConsentFile({
          valuationId,
          file,
          fileDescription: description,
        })
      }
      setUploading(false)
    },
    [valuationId, uploadConsentFile]
  )

  const deleteFile = useCallback(
    async (consentFile: ConsentFile) => {
      try {
        await deleteConsentFile({ valuationId, consentFile }).unwrap()
      } catch (_) {
        void message.error('Unknown error occurred deleting file.')
      }
    },
    [deleteConsentFile, valuationId]
  )

  const { data: consentFiles } = useGetConsentFilesQuery(valuationId)

  const [fileUploadElem, uploadFiles, dirty, cancelUpload] =
    useGenericFileUpload(false, doUploadFiles, uploading)

  return (
    <Widget
      title="Consent Documents"
      extra={
        <Space style={{ marginRight: '10px' }} direction="horizontal">
          {dirty ? (
            <>
              {/* biome-ignore lint: needs testing  */}
              <Button onClick={uploadFiles as any} disabled={disabled}>
                Save
              </Button>
              <Button onClick={cancelUpload} disabled={disabled}>
                Cancel
              </Button>
            </>
          ) : null}
        </Space>
      }
    >
      {valuationSummary?.valuation?.completedDate || disabled
        ? null
        : fileUploadElem}
      <div className="agrigis-table">
        <Table
          pagination={false}
          size="small"
          rowKey={(row) => row.id}
          dataSource={consentFiles}
          columns={[
            {
              dataIndex: 'fileName',
              title: 'Filename',
              render: (text: string, record: ConsentFile) => {
                return (
                  <a
                    href={toURL(
                      `/api/v2/valuations/${valuationId}/consent_files/${record.id}/`
                    )}
                  >
                    {text}
                  </a>
                )
              },
            },
            { dataIndex: 'fileDescription', title: 'Description' },
            { dataIndex: 'fileSize', title: 'File Size' },
            { dataIndex: 'fileType', title: 'File Type' },
            {
              key: 'actions',
              title: 'Actions',
              align: 'center',
              render: (_text: string, record: ConsentFile) => {
                return (
                  <Space>
                    <DeleteOutlined
                      disabled={disabled}
                      onClick={() => deleteFile(record)}
                      style={{
                        color: 'rgba(200, 0, 0, 1)',
                      }}
                    />
                  </Space>
                )
              },
            },
          ]}
        />
      </div>
    </Widget>
  )
}
