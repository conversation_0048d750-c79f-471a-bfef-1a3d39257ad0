import { Helmet } from 'react-helmet'
import { useParams } from 'react-router-dom'
import EsstEditView from '@components/esst/EsstEditView'
import EsstLayout from '@components/esst/EsstLayout'
import { LinkBack } from '@components/anz/Link'

export default function EsstEditPage() {
  const { esstId } = useParams()

  return (
    <>
      <Helmet>
        <title>Edit ESST Report: {esstId}</title>
      </Helmet>
      <EsstLayout actions={<LinkBack to="../../">Back to List</LinkBack>}>
        <EsstEditView />
      </EsstLayout>
    </>
  )
}
