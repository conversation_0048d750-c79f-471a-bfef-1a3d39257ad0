import React from 'react'
import { Helmet } from 'react-helmet'
import ProtectedRoute from '@components/ProtectedRoute'
import CreateRVRView from '@components/rvr/CreateRVRView'

const CreateValuationPage = () => {
  return (
    <>
      <Helmet>
        <title>Create New RVR</title>
      </Helmet>
      <ProtectedRoute requiredEntitlements={['client:propertyflow:rvr:create']}>
        <CreateRVRView />
      </ProtectedRoute>
    </>
  )
}

export default CreateValuationPage
