import { PdfTable, PdfText } from '@components/pdf'
import type { ValuationsPvsSummary } from '@store/services/sdk'
import ClimateText from './ClimateText'

interface Props {
  valuation: ValuationsPvsSummary
}

const columns = [
  { key: 'key', title: '', isHeader: true },
  { key: 'value', title: '', weighting: 3 },
]

const LocationTable = ({ valuation }: Props) => {
  const rows = [
    {
      key: 'Location',
      value: valuation.locationDescription,
    },
    {
      key: 'Climate',
      value: (
        <ClimateText size="s" seasonalRainfall={valuation.seasonalRainfall} />
      ),
    },
    {
      key: 'Aspect',
      value: valuation.aspectSummaries.join('\n'),
    },
    {
      key: 'Elevation',
      value: <PdfText size="s">{valuation.elevationDescription}</PdfText>,
    },
  ]

  return <PdfTable striped columns={columns} rows={rows} />
}

export default LocationTable
