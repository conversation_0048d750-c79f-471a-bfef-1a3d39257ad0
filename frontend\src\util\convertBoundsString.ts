import type { LatLngBoundsLiteral } from 'leaflet'
import { isLatLngTuple } from '@util/guards'

const convertBoundsString = (value: string | undefined) => {
  if (!value) return

  const [minLat, minLng, maxLat, maxLng] = value
    .split(',')
    .map((x) => Number(x))
  const minBounds = [minLng, minLat]
  const maxBounds = [maxLng, maxLat]
  if (!isLatLngTuple(minBounds) || !isLatLngTuple(maxBounds)) return

  return [minBounds, maxBounds] as LatLngBoundsLiteral
}

export default convertBoundsString
