import { Spin, message } from 'antd'
import React, { useCallback, useMemo, useState } from 'react'
import { FileInput } from '@components/generic'
import type { FileListState } from '@util/types'
import { useUploadSaleFileMutation } from '../services/sale'

/**
 * @deprecated Needs to be separated into component/hook
 */
export const useFileUpload = (
  buttonEnabled?: boolean,
  saleId?: string
): [JSX.Element, (saleId: string) => Promise<void>] => {
  const [fileListState, setFileListState] = useState<FileListState>({})

  const [uploading, setUploading] = useState<boolean>(false)

  const [uploadSaleFile] = useUploadSaleFileMutation()

  const uploadFiles = useCallback(
    async (saleId: string) => {
      for (const fileState of Object.values(fileListState)) {
        const { file, description } = fileState
        const formData = new FormData()
        const blob = new File([file], file.name, { type: file.type })

        if (!saleId) {
          throw Error('Invalid Sale selected for upload.')
        }

        try {
          if (blob.size / 1e6 > 20) {
            throw Error()
          }
        } catch (_) {
          message.error(
            'File failed to upload, please ensure it is less than 20MB.'
          )
        }

        formData.append('blob', blob, file.name)
        formData.append('saleId', saleId)
        formData.append('fileDescription', description)

        setUploading(true)
        await uploadSaleFile({ saleId, formData })
        setUploading(false)
      }
    },
    [fileListState, uploadSaleFile]
  )

  const elem = useMemo(() => {
    if (uploading) {
      return <Spin />
    }
    return (
      <FileInput
        fileListState={fileListState}
        setFileListState={setFileListState}
        buttonEnabled={buttonEnabled}
        uploadFiles={async () => {
          if (!saleId) {
            throw new Error('saleId not defined but button was enabled')
          }
          await uploadFiles(saleId)
          setFileListState({})
        }}
      />
    )
  }, [uploading, buttonEnabled, fileListState, saleId, uploadFiles])

  return [elem, uploadFiles]
}

/**
 * @deprecated Needs to be separated into component/hook
 */
export const useGenericFileUpload = (
  buttonEnabled?: boolean,
  uploadFiles?: (
    fileListState: FileListState,
    id?: string | number
  ) => Promise<void>,
  isUploading?: boolean,
  fileUploadHint?: string
): [
  JSX.Element,
  (id?: string | number) => Promise<void>,
  boolean,
  () => void,
] => {
  const [fileListState, setFileListState] = useState<FileListState>({})
  const dirty = useMemo(
    () => Object.values(fileListState).length !== 0,
    [fileListState]
  )

  const uploadFilesWrapper = useCallback(
    async (id?: string | number) => {
      await uploadFiles?.(fileListState, id)
      setFileListState({})
    },
    [fileListState, uploadFiles]
  )

  const cancelUpload = () => setFileListState({})

  const elem = useMemo(() => {
    return (
      <FileInput
        isFetching={isUploading}
        fileListState={fileListState}
        setFileListState={setFileListState}
        fileUploadHint={fileUploadHint}
        buttonEnabled={buttonEnabled}
        uploadFiles={async () => {
          await uploadFilesWrapper()
        }}
      />
    )
  }, [
    buttonEnabled,
    fileListState,
    uploadFilesWrapper,
    isUploading,
    fileUploadHint,
  ])

  return [elem, uploadFilesWrapper, dirty, cancelUpload]
}
