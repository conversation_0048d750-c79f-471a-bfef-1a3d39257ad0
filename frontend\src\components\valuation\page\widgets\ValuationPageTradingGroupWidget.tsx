import { InfoCircleOutlined } from '@ant-design/icons'
import { Alert, Tabs } from 'antd'
import { useMemo } from 'react'
import { TradingGroupBenchmarkCard } from '@components/tradingGroup/TradingGroupBenchmarkCard'
import { TradingGroupCustomersCard } from '@components/tradingGroup/TradingGroupCustomersCard/TradingGroupCustomersCard'
import { TradingGroupEmissionsBenchmarkCard } from '@components/tradingGroup/TradingGroupEmissionsBenchmarkCard'
import { TradingGroupInformationCard } from '@components/tradingGroup/TradingGroupInformationCard'
import type { Valuation } from '@types'

export interface ValuationPageTradingGroupWidgetProps {
  valuation: Valuation
}

export const ValuationPageTradingGroupWidget = ({
  valuation,
}: ValuationPageTradingGroupWidgetProps) => {
  const tradingGroupId = valuation.tradingGroupId ?? 'prospect'
  const isProspect = useMemo(() => {
    return tradingGroupId === 'prospect'
  }, [tradingGroupId])

  return (
    <>
      {!isProspect ? (
        <Tabs defaultActiveKey="INFO">
          <Tabs.TabPane tab="Information" key="INFO">
            <TradingGroupInformationCard
              tradingGroupId={valuation.tradingGroupId}
              type="inner"
              size="small"
            />
            <TradingGroupCustomersCard
              tradingGroupId={valuation.tradingGroupId}
              type="inner"
              size="small"
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="Benchmarks" key="KPIS">
            <TradingGroupBenchmarkCard
              type="inner"
              size="small"
              tradingGroupId={valuation.tradingGroupId}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="Emissions" key="EMISSIONS">
            <TradingGroupEmissionsBenchmarkCard
              tradingGroupId={valuation.tradingGroupId}
              type="inner"
              size="small"
            />
          </Tabs.TabPane>
        </Tabs>
      ) : (
        <Alert
          type="warning"
          showIcon={true}
          icon={<InfoCircleOutlined />}
          className="agrigis-alert"
          message="No trading group selected"
          description="This valuation has been linked to a prospect or a sale, trading group information is not available for this record."
        />
      )}
    </>
  )
}
