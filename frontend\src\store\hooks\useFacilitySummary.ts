import moment from 'moment'
import { useMemo } from 'react'
import type { Facility } from '@store/services/sdk'

const SORTED = ['Loan', 'Green Loan', 'Limit']

const useFacilitySummary = (facilities: Facility[] | undefined) => {
  const { amortisationSummary, fundingSummary } = useMemo(() => {
    const arr = [...(facilities ?? [])]

    const sortedKeys: string[] = ['Loan', 'Green Loan', 'Limit']

    const amort: Record<(typeof sortedKeys)[number], Record<number, number>> = {
      Limit: {},
      'Green Loan': {},
      Loan: {},
    }

    const maxYear = Math.max(
      ...arr
        .map(({ maturityDate }) => Number(maturityDate?.slice(0, 4)))
        .filter((x) => x)
    )
    const currentYear = new Date().getFullYear()
    const yearDiff = Math.abs(currentYear - maxYear)
    const arrLength = yearDiff > 5 && Number.isFinite(yearDiff) ? yearDiff : 5

    const years = new Array(arrLength)
      .fill(1)
      .map((_, idx) => Number(currentYear) + Number(idx))

    for (const { accountType, ...f } of arr) {
      const _accountType = accountType
      years?.forEach((year, idx) => {
        if (!amort[_accountType][year]) {
          amort[_accountType][year] = 0
        }
        if (accountType === 'Limit') {
          amort[_accountType][year] += Number(f.limit)
        } else {
          let newBal = Number(f.limit) - idx * Number(f.annualAmortisation)
          if (newBal < 0) {
            newBal = 0
          }
          amort[_accountType][year] += newBal
        }
      })
    }

    type StackedBarData = {
      year: number
      value?: number
      secondaryValue?: number
      measure: string
    }

    const amortReshaped: StackedBarData[] = []
    // biome-ignore lint: Needs to be refactored
    sortedKeys.forEach((k) =>
      years.map((year: number) => {
        const row: StackedBarData = {
          year,
          value: amort[k][year],
          measure: k,
        }

        if (k !== 'Limit') {
          amortReshaped.push(row)
        }
      })
    )

    const amortLimit: StackedBarData[] = []
    // biome-ignore lint: Needs to be refactored
    sortedKeys.forEach((k) =>
      years.map((year: number) => {
        const row: StackedBarData = {
          year,
          secondaryValue: amort[k][year],
          measure: k,
        }

        if (k === 'Limit') {
          amortLimit.push(row)
        }
      })
    )

    const summary: Record<(typeof sortedKeys)[number], number> = {
      Limit: 0,
      'Green Loan': 0,
      Loan: 0,
    }

    for (const f of arr) {
      summary[f.accountType] += Number(f.limit)
    }

    return {
      amortisationSummary: [amortReshaped, amortLimit],
      fundingSummary: SORTED.map((k) => ({
        type: k,
        value: summary[k],
      })),
    }
  }, [facilities])

  const maturitySummary = useMemo(() => {
    const currentYear = new Date().getFullYear()
    const nextYear = currentYear + 1
    const years = new Array(6).fill(1).map((_, idx) => {
      const year = nextYear + idx
      return `01/01/${year}`
    })

    const graphData: Record<string, number> = years.reduce<
      Record<string, number>
    >((acc, cur) => {
      acc[cur] = 0
      return acc
    }, {})

    for (let i = 0; i < years.length; i++) {
      const start = new Date(years[i - 1])
      const end = new Date(years[i])

      if (i > 0) {
        const found = [...(facilities ?? [])].filter(({ maturityDate }) => {
          if (!maturityDate) return false
          const facilityDate = moment(maturityDate, 'DD-MM-YYYY').toDate()
          return facilityDate > start && facilityDate <= end
        })
        graphData[moment(end).format('DD/MM/YYYY')] += found
          .map(({ limit }) => Number(limit))
          .reduce((acc, cur) => acc + cur, 0)
      }
    }

    return Object.keys(graphData).map((year) => ({
      year,
      value: graphData[year],
    }))
  }, [facilities])

  if (!facilities) {
    return {
      maturitySummary: undefined,
      amortisationSummary: undefined,
      fundingSummary: undefined,
    }
  }

  return {
    maturitySummary,
    amortisationSummary,
    fundingSummary,
  }
}

export default useFacilitySummary
