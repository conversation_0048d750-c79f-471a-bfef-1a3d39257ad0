import { Row, Space, Table } from 'antd'
import React, { useMemo } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import { useGetSelectedComparableSale } from '@store/hooks/useGetSelectedComparableSale'
import { useGetValuationSummaryQuery } from '@store/services/valuations'
import type { ComparableSale } from '../../../../../models/assets/ComparableSale'
import {
  useGetComparableSalesQuery,
  useGetValuationTypesQuery,
} from '../../../../../store/services/assets'
import { uiActions, uiSelectors } from '../../../../../store/ui'
import { formatDateFromIso, formatDollarValue } from '../../../../../util'
import { ComparableSaleAdjustmentWidget } from '../../../../assets/comparison/ComparableSaleAdjustmentWidget'

export interface SalesComparisonWidgetProps {
  valuationId: string
}

export const SalesComparisonWidget = (props: SalesComparisonWidgetProps) => {
  const { valuationId } = props

  const selectedComparableSaleId = useSelector(
    uiSelectors.getSelectedComparableSaleId,
    shallowEqual
  )

  const { data: comparableSales } = useGetComparableSalesQuery(valuationId)
  const selectedComparableSale = useGetSelectedComparableSale(
    valuationId,
    selectedComparableSaleId
  )

  return (
    <React.Fragment>
      {selectedComparableSale ? (
        <ComparableSaleAdjustmentWidget
          valuationId={valuationId}
          comparableSale={selectedComparableSale}
        />
      ) : (
        <ComparableSaleTable
          valuationId={valuationId}
          comparableSales={comparableSales}
        />
      )}
    </React.Fragment>
  )
}

interface ComparableSaleTableProps {
  valuationId: string
  comparableSales: ComparableSale[] | undefined
}

const ComparableSaleTable = (props: ComparableSaleTableProps) => {
  const { valuationId, comparableSales } = props

  const dispatch = useDispatch()

  const { data: bestUseTypes } = useGetValuationTypesQuery()

  const { data: valuationSummary } = useGetValuationSummaryQuery(valuationId)

  const columns = useMemo(() => {
    return [
      {
        title: 'Address',
        dataIndex: ['linkedSale', 'fullAddress'],
        render: (fullAddress: string, row: ComparableSale) => {
          return (
            <Space direction="vertical">
              <div>{fullAddress}</div>
              {row.saleHighestAndBestUseType ? (
                <div>
                  H&BS:{' '}
                  {bestUseTypes?.[row.saleHighestAndBestUseType]
                    ?.highestAndBestUse ?? ''}
                </div>
              ) : null}
            </Space>
          )
        },
      },
      {
        title: 'Comparability',
        dataIndex: [],
        render: (_value: unknown, row: ComparableSale) => {
          return (
            <>
              <Row>
                Overall: {row.overallComparability}{' '}
                {row.overallComparability !== 'Comparable'
                  ? `(${row.totalAdjustmentPercent.abs().toFixed(2)} %)`
                  : ''}
              </Row>
              <Row>
                LWB: {row.lwbComparability}{' '}
                {row.lwbComparability !== 'Comparable'
                  ? `(${row.lwbAdjustmentPercent.abs().toFixed(2)} %)`
                  : ''}
              </Row>
            </>
          )
        },
      },
      {
        title: 'Subject H&BS',
        dataIndex: ['highestAndBestUseType'],
        render: (highestAndBestUseType: number | undefined) => {
          return highestAndBestUseType
            ? bestUseTypes?.[highestAndBestUseType]?.highestAndBestUse
            : valuationSummary?.valuation.highestAndBestUseType
                ?.highestAndBestUse
        },
      },
      {
        title: 'MV ($K)',
        dataIndex: ['linkedSale', 'grossSalesPrice'],
        render: (grossSalesPrice?: number) => {
          return formatDollarValue(grossSalesPrice ?? 0)
        },
      },
      {
        title: 'Sale Date',
        dataIndex: ['linkedSale', 'saleDate'],
        render: (saleDate?: string) => {
          return formatDateFromIso(saleDate?.toString())
        },
      },
    ]
  }, [
    bestUseTypes,
    valuationSummary?.valuation.highestAndBestUseType?.highestAndBestUse,
  ])

  return (
    <div className="agrigis-table">
      <Table
        columns={columns}
        dataSource={comparableSales}
        pagination={false}
        rowKey={(row) => row.id}
        onRow={(row) => ({
          onClick: () =>
            dispatch(
              uiActions.setSelectedComparableSale({
                comparableSale: row,
              })
            ),
        })}
      />
    </div>
  )
}
