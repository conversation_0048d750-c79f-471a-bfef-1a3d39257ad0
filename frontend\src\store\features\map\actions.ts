import { createAction } from '@reduxjs/toolkit'
import type { GeoFeature } from '../../../types'
import { mapSlice } from './slice'

export interface MapFeatureClickedActionPayload {
  feature: GeoFeature
  selectedField: string
  layerType: string
}

export interface MapFeaturesClickedActionPayload {
  features: GeoFeature[]
  layerType: string
}

export const {
  toggleLayerClicking,
  toggleByAssetFilters,
  updateLayerSelection,
  updateElevationLayerLimits,
  updateElevationLayerFilter,
  updateElevationLayerStep,
  resetLayerSelectionFilter,
  toggleTitleOutline,
  toggleTransparentLayers,
  updateMeasurementFeatures,
  setMapCentre,
} = mapSlice.actions

export const mapFeatureClicked = createAction(
  'map/mapFeatureClicked',
  (payload: MapFeatureClickedActionPayload) => {
    return {
      payload,
    }
  }
)

export const mapFeaturesClicked = createAction(
  'map/mapFeaturesClicked',
  (payload: MapFeaturesClickedActionPayload) => {
    return {
      payload,
    }
  }
)
