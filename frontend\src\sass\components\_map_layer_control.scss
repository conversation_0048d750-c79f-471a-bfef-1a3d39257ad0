.ant-tabs-tabpane > .radio-options {
  width: auto;
  min-width: auto;
  padding-left: 0px;
  padding-right: 0px;
  padding-top: 10px;
  padding-bottom: 10px;

  > h6 {
    padding-top: 5px;
  }
}

.physical-radio-control {
  .ant-tabs-nav-wrap {
    padding: 0px;
    margin: 0px;
  }

  .ant-tabs-tab-btn {
    text-transform: capitalize;
  }
}

.physical-radio-control.leaflet-control-layers:hover {
  min-width: 350px;
}

.physical-properties-select {
  display: none;
}

.physical-properties-select:hover {
  display: block;
}

.physical-radio-control.leaflet-control-layers-expanded {
  min-width: 350px;
}

.ant-tabs-tabpane.physical-properties-tab {
  padding: 0px;
  align-items: flex-start;
}

.physical-map-cascade-popup {
  position: static !important;
}

.cascade-popup {
  > div {
    position: static !important;
  }
}
