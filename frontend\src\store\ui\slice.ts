import { PayloadAction, createSlice } from '@reduxjs/toolkit'
import { message } from 'antd'
import BigNumber from 'bignumber.js'
import { defaultsDeep, set } from 'lodash'
import moment from 'moment'
import { StraightLineMeasurement } from '@components/map/types'
import sdk, { Sale } from '@store/services/sdk'
import { ComparableSale } from '@models/assets/ComparableSale'
import {
  ComparableSaleAdjustment,
  serializeComparableSaleAdjustment,
  serializeComparableSaleAssetMetricAdjustment,
} from '@models/assets/ComparableSaleAdjustment'
import { SaleEditStateType } from '@models/sales/util/SaleEditState'
import { ZERO } from '@types'
import { LayerContext, LayerStyles } from '../features/map/types'
import { AgriGISMapViewPort } from '../hooks/useViewport'
import {
  SelectedID,
  SelectedIDType,
  addSelectedId,
  addSelectedIds,
  setSelectedItemsByType,
  toggleSelectedId,
} from './helpers'
import {
  ComparableSalesState,
  ExplorerFilters,
  GreenPageState,
  GreenPageTab,
  InfoState,
  MapBaseLayer,
  MapContainerRect,
  MapState,
  SaleSearchFilters,
  SavedSettings,
  SelectionBookmark,
  SetSaleEditStatePayload,
  SetSalesAndListingsCurrentTabPayload,
  ToggleSaleInSaleEditState,
  TradingGroupPageTab,
  UIState,
  UserWithSettings,
  ValocitySaleSearchFilters,
} from './types'

export const explorerFilterInitialState: ExplorerFilters = {
  saleStartDate: moment('2016-01-01'),
  saleEndDate: null,
  keyword: '',
}

export const DEFAULT_SALES_AND_LISTING_STATE: ComparableSalesState = {
  layerVisibility: false,
  currentTab: 'COMPARISON',
  selectedSale: undefined,
  selectedSaleRowIndex: undefined,
  selectedComparableSaleId: undefined,
  comparableSaleAdjustments: [],
  comparableSaleAssetMetricAdjustments: [],
  sales: {
    saleEdit: {},
  },
}

export const initialUIState: UIState = {
  salesAndListings: DEFAULT_SALES_AND_LISTING_STATE,
  user: undefined,
  loaded: false,
  saved: {
    recent: {
      valuations: [],
      addresses: [],
      tradingGroups: [],
      sales: [],
      listings: [],
    },
    saleSearchFilters: {},
    valocitySaleSearchFilters: {},
    filters: {},
    layouts: {
      addressDashboard: {
        leftPaneWidth: 600,
        rightPaneWidth: 600,
      },
      map: {
        anzUnionTransparency: 0.25,
        anzUnionKey: 'luc',
        titlesOpacity: 0,
        order: [
          'addresses',
          'sales',
          'listings',
          'valocityListings',
          'consents',
          'anzUnion',
          'titles',
        ],
      },
      saleDashboard: {
        leftPaneWidth: 600,
        rightPaneWidth: 600,
        selectedView: 'QUEUE',
        currentTab: 'FRONTLINE',
      },
      listingDashboard: {
        leftPaneWidth: 600,
        rightPaneWidth: 600,
        selectedView: 'QUEUE',
        currentTab: 'FRONTLINE',
      },
      valuationDashboard: {
        leftPaneWidth: 600,
        rightPaneWidth: 600,
        selectedView: 'USER',
      },
      valuationPage: {
        width: 600,
      },
      createSalePage: {
        width: 600,
      },
      editSalePage: {
        width: 600,
      },
      homePage: {
        width: 600,
      },
      createValuationPage: {
        width: 600,
      },
      superNav: {
        latestPage: 'sale',
      },
    },
    map: {
      bookmarks: [],
      selectedBookmarkId: null,
      viewport: undefined,
      previousBaseLayer: 'ESRI Satellite',
      baseLayer: 'ESRI Satellite',
      selected: {
        consentIds: [],
        addressIds: [],
        saleIds: [],
        listingIds: [],
        valocityListingIds: [],
        valocitySaleIds: [],
        tab: undefined,
      },
      filters: {
        ...explorerFilterInitialState,
      },
      widgetState: {
        addresses: true,
        BookmarksWidget: false,
        salesListings: true,
        valocityListings: false,
      },
      toolState: {
        draft: false,
        straightLineMeasure: undefined,
      },
      layerState: {
        addresses: true,
        sales: true,
        listings: true,
        showPropertyTooltip: false,
        showPropertyAddresses: true,
        showPropertyOwners: true,
        showPropertyAreas: true,
        showPropertyTradingGroupNames: true,
        showSaleTooltip: false,
        showSaleAddresses: true,
        showSaleAreas: true,
        showSaleDates: true,
        showSaleVendors: true,
        showSalePurchasers: true,
        showConsentCategory: true,
        showConsentIrisId: true,
        showConsentSubtype: true,
        showConsentLocation: true,
        showConsentExpiryDate: true,
        showConsentCommencementDate: true,
        showConsentHolder: true,
        showConsentDescription: true,
      },
      addressColors: {},
      saleColors: {},
      layerStyles: {
        address: {},
        sale: {},
      },
    },
  },
  temporal: {
    tradingGroup: {
      drawer: {
        visible: false,
      },
      tab: 'overview',
    },
    green: {
      drawer: {
        visible: false,
        projectId: undefined,
        annualReportingId: undefined,
      },
      tab: 'returns',
    },
    map: {
      infoState: {
        title: 'No Object Selected',
        content: undefined,
        type: undefined,
        selectedId: undefined,
      },
      mapContainerRect: {
        x: 0,
        y: 0,
        width: 0,
        height: 0,
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
      },
    },
  },
}

function initialState() {
  return initialUIState
}

export const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setMapContainerRect: (
      state: UIState,
      { payload }: PayloadAction<{ rect: MapContainerRect }>
    ) => {
      state.temporal.map.mapContainerRect = payload.rect
    },
    setTradingGroupTab: (
      state: UIState,
      { payload }: PayloadAction<{ tab: TradingGroupPageTab }>
    ) => {
      state.temporal.tradingGroup.tab = payload.tab
    },
    addBookmark: (
      state: UIState,
      { payload }: PayloadAction<{ bookmark: SelectionBookmark }>
    ) => {
      state.saved.map.bookmarks = [
        ...(state.saved.map?.bookmarks || []),
        payload.bookmark,
      ]
    },
    removeBookmark: (
      state: UIState,
      { payload }: PayloadAction<{ bookmark: SelectionBookmark }>
    ) => {
      state.saved.map.bookmarks = state.saved.map?.bookmarks?.filter(
        (bookmark) => bookmark.id !== payload.bookmark.id
      )
    },
    updateBookmark: (
      state: UIState,
      { payload }: PayloadAction<{ bookmark: SelectionBookmark }>
    ) => {
      state.saved.map.bookmarks = (state.saved.map?.bookmarks || []).map(
        (bookmark) => ({
          ...bookmark,
          ...(payload.bookmark.id === bookmark.id && payload.bookmark),
        })
      )
    },
    setSelectedBookmarkId: (
      state: UIState,
      {
        payload = { id: null },
      }: PayloadAction<{ id: SelectionBookmark['id'] | null }>
    ) => {
      state.saved.map.selectedBookmarkId = payload.id
    },
    setMapState: (
      state: UIState,
      { payload }: PayloadAction<{ state: MapState }>
    ) => {
      state.saved.map = payload.state
    },
    setSavedSettings: (
      state: UIState,
      { payload }: PayloadAction<{ state: SavedSettings }>
    ) => {
      state.loaded = true
      state.saved = payload.state
    },
    setExplorerFilters: (
      state: UIState,
      { payload }: PayloadAction<Partial<ExplorerFilters>>
    ) => {
      if (!state.saved.map.filters) {
        state.saved.map.filters = {
          ...initialUIState.saved.map.filters,
        }
      }
      Object.keys(payload).forEach((k: any) => {
        const value = payload[k as keyof ExplorerFilters]
        state.saved.map.filters[k as keyof ExplorerFilters] = value as any
      })
    },
    setFilterValue: (
      state: UIState,
      {
        payload,
      }: PayloadAction<{
        pageName: string
        filterKey: string
        filterValue: any
      }>
    ) => {
      if (!state.saved.filters) {
        state.saved.filters = {}
      }
      if (!state.saved.filters[payload?.pageName]) {
        state.saved.filters[payload?.pageName] = {}
      }
      if (payload?.filterKey !== 'page') {
        state.saved.filters[payload?.pageName].page = 1
      }
      state.saved.filters[payload?.pageName][payload?.filterKey] =
        payload?.filterValue
    },
    setFilter: (
      state: UIState,
      {
        payload,
      }: PayloadAction<{
        pageName: string
        filterValues: { [filterKey: string]: any }
      }>
    ) => {
      if (!state.saved.filters) {
        state.saved.filters = {}
      }
      state.saved.filters[payload.pageName] = {
        page: 1,
        ...payload.filterValues,
      }
    },
    setBaseLayer: (
      state: UIState,
      { payload }: PayloadAction<{ baseLayer: MapBaseLayer }>
    ) => {
      state.saved.map.previousBaseLayer = state.saved.map.baseLayer
      state.saved.map.baseLayer = payload.baseLayer
    },
    setLayoutValue: (
      state: UIState,
      {
        payload,
      }: PayloadAction<{
        pageName: string
        layoutKey: string
        layoutValue: any
      }>
    ) => {
      if (!state.saved.layouts) {
        state.saved.layouts = {}
      }
      if (!state.saved.layouts[payload?.pageName]) {
        state.saved.layouts[payload?.pageName] = {}
      }

      if (
        ['leftPaneWidth', 'rightPaneWidth', 'width'].includes(payload.layoutKey)
      ) {
        if (payload.layoutValue < 300) {
          state.saved.layouts[payload?.pageName][payload?.layoutKey] = 300
          void message.error('Pane minimum reached.')
          return
        }
        if (payload.layoutValue > 1920) {
          state.saved.layouts[payload?.pageName][payload?.layoutKey] = 1920
          void message.error('Pane maximum reached.')
          return
        }
      }
      state.saved.layouts[payload?.pageName][payload?.layoutKey] =
        payload?.layoutValue
    },
    setRecentEntry: (
      state: UIState,
      {
        payload,
      }: PayloadAction<{
        categoryName: string
        value: Pick<object & { id: string | number | undefined }, 'id'>
      }>
    ) => {
      if (!state.saved.recent[payload.categoryName]) {
        state.saved.recent[payload.categoryName] = []
      }
      let arr: Array<typeof payload.value> = [
        ...state.saved.recent[payload.categoryName].filter(
          (x) =>
            x.id &&
            payload.value?.id &&
            // eslint-disable-next-line @typescript-eslint/no-unsafe-call
            x.id.toString() !== payload.value?.id?.toString()
        ),
      ]
      if (payload.value.id) {
        arr.unshift(payload.value)
        arr = arr.slice(0, 9)
      }
      state.saved.recent[payload.categoryName] = arr
    },
    clearFilter: (
      state: UIState,
      { payload }: PayloadAction<{ pageName: string }>
    ) => {
      state.saved.filters[payload?.pageName] = {}
    },
    setUser: (
      state: UIState,
      { payload }: PayloadAction<{ user: UserWithSettings | undefined }>
    ) => {
      state.user = payload.user
    },
    setViewport: (
      state: UIState,
      { payload }: PayloadAction<{ viewport: AgriGISMapViewPort }>
    ) => {
      if (!state.saved.map) {
        state.saved.map = { ...initialUIState.saved.map }
      }
      state.saved.map.viewport = payload.viewport
    },
    setBounds: (
      state: UIState,
      { payload }: PayloadAction<AgriGISMapViewPort['bounds']>
    ) => {
      set(state, 'ui.saved.map.viewport.bounds', payload)
    },
    setSelectedAddressIds: (
      state: UIState,
      { payload }: PayloadAction<{ addressIds: string[] }>
    ) => {
      state.saved.map.selected.addressIds = payload.addressIds.filter(
        (x) => x != null
      )
    },
    setSelectedSaleIds: (
      state: UIState,
      { payload }: PayloadAction<{ saleIds: string[] }>
    ) => {
      state.saved.map.selected.saleIds = payload.saleIds.filter(
        (x) => x != null
      )
    },
    setSelectedListingIds: (
      state: UIState,
      { payload }: PayloadAction<{ listingIds: string[] }>
    ) => {
      state.saved.map.selected.listingIds = payload.listingIds.filter(
        (x) => x != null
      )
    },
    setSelectedValocitySaleIds: (
      state: UIState,
      { payload }: PayloadAction<{ valocitySaleIds: string[] }>
    ) => {
      state.saved.map.selected.valocitySaleIds = payload.valocitySaleIds.filter(
        (x) => x !== null
      )
    },
    setSelectedValocityListingIds: (
      state: UIState,
      { payload }: PayloadAction<{ valocityListingIds: string[] }>
    ) => {
      state.saved.map.selected.valocityListingIds =
        payload.valocityListingIds.filter((x) => x !== null)
    },
    setStoredStraightLines: (
      state: UIState,
      { payload }: PayloadAction<{ lines: StraightLineMeasurement[] }>
    ) => {
      state.saved.map.toolState.straightLineMeasure = payload.lines
    },
    updateLineDraftState: (
      state: UIState,
      { payload }: PayloadAction<{ draft: boolean }>
    ) => {
      state.saved.map.toolState.draft = payload.draft
    },
    setSelectedTab: (
      state: UIState,
      { payload }: PayloadAction<{ tab: string }>
    ) => {
      state.saved.map.selected.tab = payload.tab
    },
    setInfoState: (
      state: UIState,
      {
        payload,
      }: PayloadAction<
        InfoState & Required<Pick<InfoState, 'selectedId' | 'title' | 'type'>>
      >
    ) => {
      state.temporal.map.infoState = payload
    },
    setSelectedAddressColor: (
      state: UIState,
      { payload }: PayloadAction<{ addressId: string; color: string }>
    ) => {
      if (!state.saved.map.addressColors[payload.addressId]) {
        state.saved.map.addressColors[payload.addressId] = 'blue'
      }
      state.saved.map.addressColors[payload.addressId] = payload.color
    },
    setSelectedEmptyByType(state, { payload }: PayloadAction<SelectedIDType>) {
      setSelectedItemsByType(state, payload, [])
    },
    setSelectedIdsByType(
      state,
      { payload }: PayloadAction<{ type: SelectedIDType; ids: SelectedID[] }>
    ) {
      setSelectedItemsByType(state, payload.type, payload.ids)
    },
    setLayerStyles: (
      state: UIState,
      {
        payload,
      }: PayloadAction<{
        context: LayerContext
        id: number
        updates: LayerStyles
      }>
    ) => {
      // Don't feel like combining these is a great idea, but the initialState is replaced by the query result, so tracking the default state in a single function seems like the best option for now
      const { id, context, updates } = payload
      if (!state.saved.map.layerStyles) {
        state.saved.map.layerStyles = { address: {}, sale: {} }
      }
      state.saved.map.layerStyles[context]![id] = {
        ...state.saved.map.layerStyles[context]?.[id],
        ...updates,
      }
    },
    setSelectedSaleColor: (
      state: UIState,
      { payload }: PayloadAction<{ saleId: string; color: string }>
    ) => {
      if (!state.saved.map.saleColors[payload.saleId]) {
        state.saved.map.saleColors[payload.saleId] = 'blue'
      }
      state.saved.map.saleColors[payload.saleId] = payload.color
    },
    setWidgetState: (
      state: UIState,
      { payload }: PayloadAction<{ widgetKey: string; widgetState: boolean }>
    ) => {
      const { widgetKey, widgetState } = payload
      if (!state.saved.map.widgetState[widgetKey]) {
        state.saved.map.widgetState[widgetKey] = false
      }
      state.saved.map.widgetState[widgetKey] = widgetState
    },
    setLayerState: (
      state: UIState,
      { payload }: PayloadAction<{ layerKey: string; layerState: boolean }>
    ) => {
      const { layerKey, layerState } = payload
      if (!state.saved.map.layerState[layerKey]) {
        state.saved.map.layerState[layerKey] = false
      }
      state.saved.map.layerState[layerKey] = layerState
    },
    toggleSelectedValocitySaleId: (
      state: UIState,
      { payload }: PayloadAction<string>
    ) => {
      toggleSelectedId(state, payload, 'valocitySaleIds')
    },
    toggleSelectedValocityListingId: (
      state: UIState,
      { payload }: PayloadAction<string>
    ) => {
      toggleSelectedId(state, payload, 'valocityListingIds')
    },
    addSelectedAddressId: (
      state: UIState,
      { payload }: PayloadAction<string>
    ) => {
      addSelectedId(state, payload, 'addressIds')
    },
    addSelectedAddressIds: (
      state: UIState,
      { payload }: PayloadAction<string[]>
    ) => {
      addSelectedIds(state, payload, 'addressIds')
    },
    toggleSelectedAddressId: (
      state: UIState,
      { payload }: PayloadAction<string>
    ) => {
      toggleSelectedId(state, payload, 'addressIds')
    },
    addSelectedSaleId: (state: UIState, { payload }: PayloadAction<string>) => {
      addSelectedId(state, payload, 'saleIds')
    },
    toggleSelectedSaleId: (
      state: UIState,
      { payload }: PayloadAction<string>
    ) => {
      toggleSelectedId(state, payload, 'saleIds')
    },
    addSelectedListingId: (
      state: UIState,
      { payload }: PayloadAction<string>
    ) => {
      addSelectedId(state, payload, 'listingIds')
    },
    addSelectedListingIds: (
      state: UIState,
      { payload }: PayloadAction<string[]>
    ) => {
      addSelectedIds(state, payload, 'listingIds')
    },
    toggleSelectedListingId: (
      state: UIState,
      { payload }: PayloadAction<string>
    ) => {
      toggleSelectedId(state, payload, 'listingIds')
    },
    toggleSelectedIdByType: (
      state: UIState,
      {
        payload: { id, type },
      }: PayloadAction<{ id: string; type: SelectedIDType }>
    ) => {
      toggleSelectedId(state, id, type)
    },
    setSalesAndListingCurrentTab: (
      state: UIState,
      { payload }: PayloadAction<SetSalesAndListingsCurrentTabPayload>
    ) => {
      const { currentTab } = payload
      state.salesAndListings.currentTab = currentTab
    },
    setSelectedSale: (
      state: UIState,
      { payload }: PayloadAction<{ sale: Sale; saleRowIndex?: number }>
    ) => {
      state.salesAndListings.selectedSale = payload.sale
      state.salesAndListings.selectedSaleRowIndex = payload.saleRowIndex
    },
    setSelectedComparableSale: (
      state: UIState,
      { payload }: PayloadAction<{ comparableSale: ComparableSale | undefined }>
    ) => {
      state.salesAndListings.selectedComparableSaleId =
        payload.comparableSale?.id
      state.salesAndListings.comparableSaleAdjustments =
        payload.comparableSale?.adjustments?.map(
          serializeComparableSaleAdjustment
        ) ?? []
      state.salesAndListings.comparableSaleAssetMetricAdjustments =
        payload.comparableSale?.assetMetricAdjustments?.map(
          serializeComparableSaleAssetMetricAdjustment
        ) ?? []
    },
    toggleSalesAndListingLayerVisibility: (
      state: UIState,
      { payload }: PayloadAction<{ visible: boolean }>
    ) => {
      state.salesAndListings.layerVisibility = payload.visible
    },
    setSaleEditState: (
      state: UIState,
      { payload }: PayloadAction<SetSaleEditStatePayload>
    ) => {
      const { saleEdit } = payload
      try {
        state.salesAndListings.sales.saleEdit = saleEdit
      } catch (e) {
        console.error('error setting saleEditState', e)
      }
    },
    toggleSaleInSaleEditState: (
      state: UIState,
      { payload }: PayloadAction<ToggleSaleInSaleEditState>
    ) => {
      const stateMap: { [currentState: string]: SaleEditStateType } = {
        EXISTING: 'REMOVE',
        REMOVE: 'EXISTING',
        NEW: 'ADD',
        ADD: 'NEW',
      }

      const { saleId, ...rest } = payload

      const draftState = { ...state.salesAndListings.sales.saleEdit }
      const draftEdit = {
        ...draftState[saleId],
        state: stateMap[draftState[saleId].state],
        ...rest,
      }
      draftState[saleId] = draftEdit
      state.salesAndListings.sales.saleEdit = draftState
    },
    setComparableSaleAdjustments: (
      state: UIState,
      { payload }: PayloadAction<{ adjustments: ComparableSaleAdjustment[] }>
    ) => {
      state.salesAndListings.comparableSaleAdjustments =
        payload.adjustments.map(serializeComparableSaleAdjustment)
    },
    updateSaleAssetMetricAdjustment: (
      state: UIState,
      {
        payload,
      }: PayloadAction<{
        remove?: boolean
        landAssetMetric: number
        comparableSubjectAep: BigNumber
        metricAEP: BigNumber
      }>
    ) => {
      const existingIndex =
        state.salesAndListings.comparableSaleAssetMetricAdjustments.findIndex(
          (adjustment) => adjustment.landAssetMetric === payload.landAssetMetric
        )
      if (payload.remove && existingIndex !== -1) {
        state.salesAndListings.comparableSaleAssetMetricAdjustments.splice(
          existingIndex,
          1
        )
        return
      }

      const productivityAdjustmentPercent = payload.comparableSubjectAep.gt(0)
        ? payload.metricAEP
            .minus(payload.comparableSubjectAep)
            .div(payload.comparableSubjectAep)
            .multipliedBy(100)
        : ZERO
      // TODO: function
      let comparability = 'Comparable'
      if (productivityAdjustmentPercent.gt(0)) {
        comparability = 'Superior'
      } else if (productivityAdjustmentPercent.lt(0)) {
        comparability = 'Inferior'
      }

      if (existingIndex !== -1) {
        state.salesAndListings.comparableSaleAssetMetricAdjustments[
          existingIndex
        ].comparableSubjectAep = payload.comparableSubjectAep.toString()
        state.salesAndListings.comparableSaleAssetMetricAdjustments[
          existingIndex
        ].productivityAdjustmentPercent = productivityAdjustmentPercent
          .abs()
          .toString()

        state.salesAndListings.comparableSaleAssetMetricAdjustments[
          existingIndex
        ].comparability = comparability
      } else {
        state.salesAndListings.comparableSaleAssetMetricAdjustments.push({
          id: undefined,
          landAssetMetric: payload.landAssetMetric,
          comparableSubjectAep: payload.comparableSubjectAep.toString(),
          productivityAdjustmentPercent: productivityAdjustmentPercent
            .abs()
            .toString(),
          comparability: comparability,
        })
      }
    },
    toggleGreenDrawer: (state: UIState) => {
      state.temporal.green.drawer.visible = !state.temporal.green.drawer.visible
    },
    openGreenDrawer: (state: UIState) => {
      state.temporal.green.drawer.visible = true
    },
    closeGreenDrawer: (state: UIState) => {
      state.temporal.green.drawer.visible = false
    },
    setGreenDrawerState: (
      state: UIState,
      { payload }: PayloadAction<GreenPageState['drawer']>
    ) => {
      state.temporal.green.drawer = payload
    },
    setGreenTab: (
      state: UIState,
      { payload }: PayloadAction<{ tab: GreenPageTab }>
    ) => {
      state.temporal.green.tab = payload.tab
    },
    updateSaleFilterState: (
      state: UIState,
      {
        payload,
      }: PayloadAction<{ pageName: string; update: Partial<SaleSearchFilters> }>
    ) => {
      state.saved.saleSearchFilters[payload.pageName] = {
        ...state.saved.saleSearchFilters[payload.pageName],
        ...payload.update,
      }
      if (!state.saved.saleSearchFilters[payload.pageName].useDateFilter) {
        state.saved.saleSearchFilters[payload.pageName].saleDateGte = undefined
        state.saved.saleSearchFilters[payload.pageName].saleDateLte = undefined
      }
    },
    updateValocitySaleFilterState: (
      state: UIState,
      {
        payload,
      }: PayloadAction<{
        pageName: string
        update: Partial<ValocitySaleSearchFilters>
      }>
    ) => {
      state.saved.valocitySaleSearchFilters[payload.pageName] = {
        ...state.saved.valocitySaleSearchFilters[payload.pageName],
        ...payload.update,
      }
      if (
        !state.saved.valocitySaleSearchFilters[payload.pageName].useDateFilter
      ) {
        state.saved.valocitySaleSearchFilters[payload.pageName].saleDateGte =
          undefined
        state.saved.valocitySaleSearchFilters[payload.pageName].saleDateLte =
          undefined
      }
    },
    clearSaleFilterState: (
      state: UIState,
      { payload }: PayloadAction<{ pageName: string }>
    ) => {
      state.saved.saleSearchFilters[payload.pageName] = { useDateFilter: false }
    },
    clearValocitySaleFilterState: (
      state: UIState,
      { payload }: PayloadAction<{ pageName: string }>
    ) => {
      state.saved.valocitySaleSearchFilters[payload.pageName] = {
        useDateFilter: false,
      }
    },
  },
  extraReducers: (builder) => {
    builder.addMatcher(
      sdk.endpoints.userCurrentRetrieve.matchFulfilled,
      (state, { payload }) => {
        set(state, 'saved', defaultsDeep(payload.settings, { ...state.saved }))
      }
    )
  },
})

export const uiReducer = uiSlice.reducer
