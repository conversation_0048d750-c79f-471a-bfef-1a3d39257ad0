import { Helmet } from 'react-helmet'
import { useParams } from 'react-router-dom'
import EsstLayout from '@components/esst/EsstLayout'
import EsstView from '@components/esst/EsstView'
import { LinkBack } from '@components/anz/Link'
import { LANGUAGE } from '@components/esst/const'

export default function EsstPage() {
  const { esstId } = useParams()

  return (
    <>
      <Helmet>
        <title>ESST {esstId}</title>
      </Helmet>
      <EsstLayout
        actions={<LinkBack to="..">{LANGUAGE.BACK_TO_LIST}</LinkBack>}
      >
        <EsstView />
      </EsstLayout>
    </>
  )
}
