import { type PayloadAction, createSlice, nanoid } from '@reduxjs/toolkit'
import { type Feature, Geometry } from 'geojson'
import { BuildingOutline } from '@models/BuildingOutline'
import type { AssetType } from '../../../models/assets/Asset'
import { mapActions } from '../map'

export interface AssetsState {
  valuationId: string
  ui: {
    assetLayer: {
      show: boolean
      selectedAssetType: AssetType | 'All'
      selectedAssetId?: string | number
      selectedAssetField: string
    }
    edits: {
      assetUnderEditId?: string | number
      assetType: AssetType
      showForm: boolean
      dirty: boolean
      addRemainingGeometry: boolean
      editState: 'NEW' | 'EXISTING'
      changes: {
        delete: {
          [assetId: string]: boolean
        }
      }
      selectedBuildingOutlines: number[]
      // if editing a single asset, this object is used to update the geometry when it is modified on the map
      // since we cannot hook that up to the actual geometry (in one of the above `add`, `delete` or `edit` states)
      featureUnderEdit: {
        dirty: boolean
        userEdited: boolean
        geoFeatureMap: Record<string, Feature>
        remainingFeatureOriginal: Feature | undefined
      }
    }
  }
}

export const defaultAssetEditChangesState: AssetsState['ui']['edits']['changes'] =
  {
    delete: {},
  }

export const defaultFeatureUnderEditState: AssetsState['ui']['edits']['featureUnderEdit'] =
  {
    dirty: false,
    userEdited: false,
    geoFeatureMap: {},
    remainingFeatureOriginal: undefined,
  }

export const initialAssetsState: AssetsState = {
  valuationId: '-1',
  ui: {
    assetLayer: {
      show: false,
      selectedAssetType: 'All',
      selectedAssetField: 'assetType',
      selectedAssetId: undefined,
    },
    edits: {
      assetUnderEditId: undefined,
      editState: 'NEW',
      assetType: 'Carbon',
      showForm: false,
      addRemainingGeometry: false,
      selectedBuildingOutlines: [],
      dirty: false,
      changes: { ...defaultAssetEditChangesState },
      featureUnderEdit: { ...defaultFeatureUnderEditState },
    },
  },
}

export interface BeginAssetCreationActionPayload {
  addressId: string
}

export interface UpdateEditSingleGeometryActionPayload {
  newGeometry: Feature
  remainingFeatureOriginal?: Feature
}

export interface UpdateEditGeometryMapActionPayload {
  geometryMap: Record<string, Feature>
  remainingFeatureOriginal?: Feature
  userEdited?: boolean
}

export interface ToggleAssetForEditingActionPayload {
  editState: 'NEW' | 'EXISTING'
  id: string
  assetId?: string | number
  assetType: AssetType
  geometry: Feature['geometry']
  addRemainingGeometry?: boolean
  buildingOutlineIds?: number[]
}

// intersectLayer: defaultAsyncState({ type: 'FeatureCollection', features: [] }),

export type UpdateEditGeometryActionPayload =
  | UpdateEditSingleGeometryActionPayload
  | UpdateEditGeometryMapActionPayload

export const assetsSlice = createSlice({
  name: 'assets',
  initialState: initialAssetsState,
  reducers: {
    beginAssetEdit(
      state: AssetsState,
      { payload }: PayloadAction<{ assetType: AssetType }>
    ) {
      state.ui.edits.dirty = true
      state.ui.edits.showForm = true
      state.ui.edits.assetType = payload.assetType
      /*
            state.ui.edits.changes[payload.assetType].add[randomId] =
                emptyGeoFeature(
                    {
                        assetType: payload.assetType,
                    },
                    randomId
                );
            */
    },
    toggleAssetField(
      state: AssetsState,
      { payload }: PayloadAction<{ assetField: string }>
    ) {
      state.ui.assetLayer.selectedAssetField = payload.assetField
    },
    updateFeatureUnderEdit(
      state: AssetsState,
      { payload }: PayloadAction<UpdateEditGeometryActionPayload>
    ) {
      if ('newGeometry' in payload) {
        // if we do it this way we have to be careful that mixing creating geometries and selected geometries doesnt break things
        const id = nanoid()
        state.ui.edits.featureUnderEdit.geoFeatureMap = {
          ...state.ui.edits.featureUnderEdit.geoFeatureMap,
          [id]: {
            ...payload.newGeometry,
            id,
          },
        }
        state.ui.edits.featureUnderEdit.userEdited = true
      } else {
        state.ui.edits.featureUnderEdit.geoFeatureMap = payload.geometryMap
        state.ui.edits.featureUnderEdit.userEdited = payload.userEdited ?? true
        state.ui.edits.featureUnderEdit.remainingFeatureOriginal =
          payload.remainingFeatureOriginal
      }
      state.ui.edits.featureUnderEdit.dirty = true
    },
    toggleAssetLayer(
      state: AssetsState,
      {
        payload,
      }: PayloadAction<{
        show?: boolean
        assetType?: AssetType
        selectedAssetField?: string
        selectedAssetId?: string | number
      }>
    ) {
      if (payload.show !== undefined) {
        state.ui.assetLayer.show = payload.show
      } else {
        state.ui.assetLayer.show = !state.ui.assetLayer.show
      }
      if (payload.assetType !== undefined) {
        if (state.ui.assetLayer.selectedAssetType !== payload.assetType) {
          state.ui.assetLayer.show = true
        }
        state.ui.assetLayer.selectedAssetType = payload.assetType
      }
      // TODO: remove this, it's just used for the Planting asset layer in AssetMapLayer and the way it's implemented here is old and done really badly.
      // it's like a half way between approach
      if (payload.selectedAssetField !== undefined) {
        state.ui.assetLayer.selectedAssetField = payload.selectedAssetField
      } else {
        state.ui.assetLayer.selectedAssetField = 'assetType'
      }
      state.ui.assetLayer.selectedAssetId = payload.selectedAssetId
    },
    toggleAssetForEditing(
      state: AssetsState,
      { payload }: PayloadAction<ToggleAssetForEditingActionPayload>
    ) {
      state.ui.edits.assetType = payload.assetType
      state.ui.edits.assetUnderEditId = payload.assetId
      state.ui.edits.addRemainingGeometry =
        payload.addRemainingGeometry ?? false
      state.ui.edits.showForm = true
      state.ui.edits.dirty = true
      state.ui.edits.editState = payload.editState
      const newFeature: Feature = {
        type: 'Feature',
        geometry: payload.geometry,
        id: payload.id,
        properties: {},
      }
      const id = nanoid()
      state.ui.edits.featureUnderEdit.geoFeatureMap = {
        [id]: {
          ...newFeature,
        },
      }
      if (payload.buildingOutlineIds) {
        state.ui.edits.selectedBuildingOutlines =
          state.ui.edits.selectedBuildingOutlines.concat(
            payload.buildingOutlineIds
          )
      }
    },
    stageAssetForDeletion(
      state: AssetsState,
      { payload }: PayloadAction<{ id: string; assetType: AssetType }>
    ) {
      state.ui.edits.assetType = payload.assetType
      state.ui.edits.dirty = true
      state.ui.edits.changes.delete[payload.id] = true
    },
    cancelPendingEdits(state: AssetsState) {
      state.ui.edits.dirty = false
      state.ui.edits.showForm = false
      state.ui.edits.addRemainingGeometry = false
      state.ui.edits.changes = { ...defaultAssetEditChangesState }
      state.ui.edits.selectedBuildingOutlines = []
      state.ui.edits.assetUnderEditId = undefined
      state.ui.edits.featureUnderEdit = {
        ...defaultFeatureUnderEditState,
      }
    },
    buildingOutlineClicked(
      state: AssetsState,
      {
        payload,
      }: PayloadAction<{
        geometry: Feature['geometry']
        buildingOutlineIds: number[]
      }>
    ) {
      if (state.ui.edits.dirty && state.ui.edits.assetType === 'Improvement') {
        const id = nanoid()
        state.ui.edits.featureUnderEdit.dirty = true
        state.ui.edits.showForm = true
        state.ui.edits.featureUnderEdit.geoFeatureMap[id] = {
          id,
          type: 'Feature',
          geometry: payload.geometry,
          properties: {},
        }
        state.ui.edits.selectedBuildingOutlines =
          state.ui.edits.selectedBuildingOutlines.concat(
            payload.buildingOutlineIds
          )
      }
    },
    commitPendingEditsComplete(state: AssetsState) {
      state.ui.edits.changes = { ...defaultAssetEditChangesState }
      state.ui.edits.featureUnderEdit = {
        ...defaultFeatureUnderEditState,
      }
      state.ui.edits.dirty = false
      state.ui.edits.addRemainingGeometry = false
      state.ui.edits.showForm = false
      state.ui.edits.selectedBuildingOutlines = []
      state.ui.edits.assetUnderEditId = undefined
    },
    setAddRemainingGeometry(
      state: AssetsState,
      { payload }: PayloadAction<{ addRemainingGeometry: boolean }>
    ) {
      state.ui.edits.addRemainingGeometry = payload.addRemainingGeometry
    },
  },
  extraReducers: (builder) => {
    builder.addCase(mapActions.mapFeatureClicked, (state, action) => {
      if (state.ui.edits.dirty && state.ui.edits.assetType === 'Land') {
        const id = nanoid()
        state.ui.edits.featureUnderEdit.userEdited = true
        state.ui.edits.featureUnderEdit.dirty = true
        state.ui.edits.featureUnderEdit.geoFeatureMap[id] =
          action.payload.feature
      }
    })
    builder.addCase(mapActions.mapFeaturesClicked, (state, action) => {
      if (state.ui.edits.dirty && state.ui.edits.assetType === 'Land') {
        state.ui.edits.featureUnderEdit.userEdited = true
        for (const feature of action.payload.features) {
          const id = nanoid()
          state.ui.edits.featureUnderEdit.dirty = true
          state.ui.edits.featureUnderEdit.geoFeatureMap[id] = feature
        }
      }
    })
  },
})

export const reducer = assetsSlice.reducer
