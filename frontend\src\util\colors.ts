import { clamp } from 'lodash'
import type { LayerLegend, LegendValue } from '../types/GISLayerDescriptors'

export const colors = ['#d50000', '#f9ff06', '#c000ff', '#0207ff'].reverse()

export function toRGB(color: string) {
  const hex = Number.parseInt(color.replace(/#/g, ''), 16)
  const r = hex >> 16
  const g = (hex >> 8) & 0xff
  const b = hex & 0xff
  return {
    r,
    g,
    b,
  }
}

export function fromRGB(rgb: { r: number; g: number; b: number }) {
  return `#${(((1 << 24) + (rgb.r << 16) + (rgb.g << 8) + rgb.b) | 0)
    .toString(16)
    .slice(1)}`
}

// expects frac between 0 and 1
export function lerpTwoColors(
  firstColor: string,
  secondColor: string,
  frac: number
) {
  const fraction = clamp(frac, 0, 1)
  const first = toRGB(firstColor)
  const second = toRGB(secondColor)

  return fromRGB({
    r: first.r + fraction * (second.r - first.r),
    g: first.g + fraction * (second.g - first.g),
    b: first.b + fraction * (second.b - first.b),
  })
}

export function renormalize(value: number, start: number, end: number): number {
  if (value <= start) {
    return 0
  }
  if (value >= end) {
    return 1
  }

  return (value - start) / (end - start)
}

export function lerpColors(
  colors: string[],
  value: number | null,
  minValue: number,
  maxValue: number
) {
  // let frac = value === null ? 0 : (value / maxValue);
  let frac = 0
  if (maxValue !== minValue) {
    frac = value === null ? 0 : (value - minValue) / (maxValue - minValue)
  }
  if (frac > 1) {
    frac = 1
  } else if (frac <= 0) {
    frac = 0
  }

  const divisor = colors.length - 1

  let startIndex = Math.floor(frac * divisor)
  if (startIndex >= divisor) {
    startIndex = divisor - 1
  } else if (startIndex < 0) {
    startIndex = 0
  }

  const newFrac = (frac - startIndex / divisor) * divisor
  const firstColor = colors[startIndex]
  const secondColor = colors[startIndex + 1]

  return lerpTwoColors(firstColor, secondColor, newFrac)
}

export function elevationColor(
  elevation: number,
  minElevation: number,
  maxElevation: number
) {
  return lerpColors(colors, elevation, minElevation, maxElevation)
}

export const createLegendWithColors = (
  name: string,
  fieldValuesToColor: { [fieldValue: string]: string },
  valueToLabel?: (value: string) => string
): LayerLegend => {
  const legendEntries: { [key: string]: LegendValue } = Object.entries(
    fieldValuesToColor
  ).reduce(
    (accum: { [key: string]: LegendValue }, [fieldValue]: [string, string]) => {
      const label = (
        valueToLabel ? valueToLabel(fieldValue) : fieldValue
      )?.toString()
      accum[fieldValue] = {
        label,
        description: label,
        color: fieldValuesToColor[fieldValue],
      }
      return accum
    },
    {}
  )

  return {
    name,
    legendEntries,
    hasDescriptions: false,
    getDescriptor: (fieldValue) => legendEntries[fieldValue]?.description ?? '',
    getColor: (fieldValue?: string) =>
      legendEntries[fieldValue ?? '']?.color ?? '#fff',
  }
}

export const createLegend = (
  name: string,
  fieldValues: string[],
  valueToLabel?: (value: string) => string
): LayerLegend => {
  const DEFAULT_COLOR_RANGE = [
    '#101010',
    '#d50000',
    '#f9ff06',
    '#c000ff',
    '#0207ff',
  ].reverse()

  const numValues = fieldValues.length
  const colorMap = fieldValues.reduce(
    (accum: { [value: string]: string }, fieldValue: string, index: number) => {
      accum[fieldValue] = lerpColors(
        DEFAULT_COLOR_RANGE,
        index,
        0,
        numValues - 1
      )
      return accum
    },
    {}
  )

  const legendEntries: { [key: string]: LegendValue } = fieldValues.reduce(
    (accum: { [key: string]: LegendValue }, fieldValue: string) => {
      const label = (
        valueToLabel ? valueToLabel(fieldValue) : fieldValue
      )?.toString()
      accum[fieldValue] = {
        label,
        description: label,
        color: colorMap[fieldValue],
      }
      return accum
    },
    {}
  )

  return {
    name,
    legendEntries,
    hasDescriptions: false,
    getDescriptor: (fieldValue) => legendEntries[fieldValue]?.description ?? '',
    getColor: (fieldValue?: string) =>
      legendEntries[fieldValue ?? '']?.color ?? '#fff',
  }
}
