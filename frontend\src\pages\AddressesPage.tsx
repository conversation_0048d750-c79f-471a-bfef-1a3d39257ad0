import { Helmet } from 'react-helmet'
import ProtectedRoute from '@components/ProtectedRoute'
import AddressesView from '@components/addresses/AddressesView'

const AddressesPage = () => (
  <>
    <Helmet>
      <title>Addresses Dashboard</title>
    </Helmet>
    <ProtectedRoute requiredEntitlements={['client:propertyflow:addresses:*']}>
      <AddressesView />
    </ProtectedRoute>
  </>
)

export default AddressesPage
