import { SaveOutlined } from '@ant-design/icons'
import { Button, DatePicker, Form, type FormInstance, Input } from 'antd'
import type React from 'react'
import { useEffect } from 'react'
import type { Valuation } from '../../../../types'
import { HighestAndBestUseInput } from '../../../assets/land/HighestAndBestUseInput'

interface RVRValuationFormProps {
  form: FormInstance<Valuation>
  handleSubmit: (formValues: Valuation) => void
  setFormValues?: React.Dispatch<React.SetStateAction<Valuation>>
}

export const RVRValuationForm = (props: RVRValuationFormProps) => {
  const { handleSubmit, form, setFormValues } = props

  useEffect(() => {
    form.setFields([{ name: 'valuationName', value: '' }])
  }, [form])

  return (
    <Form
      layout="vertical"
      form={form}
      onValuesChange={(formValues: Valuation) => {
        if (setFormValues) {
          // ??????
          form.setFieldsValue(formValues)
          setFormValues(form.getFieldsValue())
        }
      }}
      onFinish={(formValues: Valuation) => {
        handleSubmit(formValues)
      }}
      className="agrigis-form"
    >
      <Form.Item
        label="Highest & Best Use"
        name="highestAndBestUseTypeId"
        rules={[{ required: true }]}
      >
        <HighestAndBestUseInput />
      </Form.Item>
      <Form.Item
        label="Valuation Name"
        name="valuationName"
        rules={[{ required: true }]}
      >
        <Input />
      </Form.Item>
      <Form.Item
        label="Valuer / Firm"
        name="externalValuer"
        rules={[{ required: true }]}
      >
        <Input />
      </Form.Item>
      <Form.Item
        label="Primary Valuation Approach"
        name="primaryValuationApproach"
        rules={[{ required: true }]}
      >
        <Input />
      </Form.Item>
      <Form.Item
        label="Valuation Date"
        name="valuationDate"
        rules={[{ required: true }]}
      >
        <DatePicker style={{ width: '100%' }} />
      </Form.Item>
      <Form.Item>
        <hr />
        <Button icon={<SaveOutlined />} type="primary" htmlType="submit">
          Save
        </Button>
      </Form.Item>
    </Form>
  )
}
