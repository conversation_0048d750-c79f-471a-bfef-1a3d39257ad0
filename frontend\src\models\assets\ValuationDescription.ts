export interface ValuationDescription {
  valuationId: string | number
  highestAndBestUseType: number | string

  landClass?: string
  location?: string
  climate?: string
  improvements?: string

  irrigation?: string
  environmentalCompliance?: string
  dcCompliance?: string
  marketCircumstances?: string

  other?: string
}

export interface ValuationDescriptionMap {
  [bestUseId: string]: ValuationDescription
}
