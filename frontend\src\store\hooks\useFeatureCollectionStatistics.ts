import { useMemo } from 'react'
import { isFeatureCollection, isObjectLiteral } from '@util/guards'

const useFeatureCollectionStatistics = <T>(data: T, metric: string) => {
  const arr = useMemo(() => {
    if (!isFeatureCollection(data)) return []
    return [...(data.features ?? [])]
      .map(
        ({ properties }) =>
          isObjectLiteral(properties) &&
          metric in properties &&
          properties[metric]
      )
      .filter((x) => x) as number[]
  }, [data, metric])

  const { sum, count } = useMemo(() => {
    return {
      sum: arr.reduce((acc, cur) => acc + cur, 0),
      count: arr.length,
    }
  }, [arr])

  const statistics = useMemo(() => {
    const avg = sum / count
    return {
      avg,
      sum,
      count,
      min: Math.min(...arr),
      max: Math.max(...arr),
    }
  }, [arr, count, sum])

  return statistics
}

export default useFeatureCollectionStatistics
