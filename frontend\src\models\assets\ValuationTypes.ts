export interface ImprovementType {
  id: number
  improvementType: string
  defaultMetricUnit: string
  defaultMetricType: 'M2' | 'CUSTOM' | 'NOMINAL'
}

export interface LandClassType {
  id: number
  landClass: string
  color: string
  isProductive: boolean
  isJunk: boolean
  useTotalValue: boolean
}

export type PlantingVarieties = {
  id: number
  name: string
}

export interface HighestAndBestUseType {
  id: number
  bestUseCategory: string
  highestAndBestUse: string
  aepUnit: string
  aepRounding: number
  aepPrecision?: number
  validLandClasses: LandClassType[]
  fallbackLandClass: LandClassType
  validImprovementTypes: ImprovementType[]
  categories: string[]

  usesGrafting: boolean
  orchardTypes: string[]
  coverTypes: string[]
  plantingRegimes: string[]
  plantingVarieties: PlantingVarieties[]
}

export interface HighestAndBestUseTypeMap {
  [id: string]: HighestAndBestUseType
}

export const bestUseIsCompatible = (
  bestUseA: HighestAndBestUseType,
  bestUseB: HighestAndBestUseType
) => {
  return bestUseA.id === bestUseB.id || bestUseA.aepUnit === bestUseB.aepUnit
}

export const compatibleBestUses = (
  valuationTypes: HighestAndBestUseTypeMap,
  subjectBestUses: number[],
  saleBestUse: number
) => {
  const saleBestUseType = valuationTypes[saleBestUse]
  const compatibleBestUses = subjectBestUses
    .map((subjectBestUse) => valuationTypes[subjectBestUse])
    .filter((subjectBestUseType) =>
      bestUseIsCompatible(saleBestUseType, subjectBestUseType)
    )
    .map((subjectBestUseType) => subjectBestUseType.id)
  return compatibleBestUses
}

export const renderHighestAndBestUseTypeLabel = (
  highestAndBestUseType: HighestAndBestUseType
) => {
  if (
    highestAndBestUseType.bestUseCategory !==
    highestAndBestUseType.highestAndBestUse
  ) {
    return `${highestAndBestUseType.bestUseCategory} - ${highestAndBestUseType.highestAndBestUse}`
  }
  return highestAndBestUseType.bestUseCategory
}

const contourToLandClass: { [contour: string]: string[] } = {
  '0': [
    'Flat',
    'Flat – CP/Lateral',
    'Flat – Other Spray',
    'Flat – Sprinklers',
    'Flat – Border Dyke',
  ],
  '1': [
    'Rolling/Easy Hill',
    'Easy/Strongly Rolling – CP',
    'Easy/Strongly Rolling – Sprinklers',
  ],
  '2': ['Medium Hill', 'Medium Hill (unimproved)'],
  '3': ['Steep Hill', 'Steep Hill (unimproved)'],
  '-9999': [],
}

export const lookupLandClassesFromContourData = (
  bestUse: HighestAndBestUseType,
  underlyingContourValues: [string, number][]
): LandClassType[] => {
  const validLandClasses = Object.fromEntries(
    bestUse.validLandClasses.map((landClassType) => [
      landClassType.landClass,
      landClassType,
    ])
  )
  const dominantContourValue = underlyingContourValues?.[0]?.[0]

  if (dominantContourValue) {
    const contourLandClasses = contourToLandClass[dominantContourValue] ?? []
    return contourLandClasses
      .map((landClassName) => validLandClasses[landClassName])
      .filter((value) => value !== undefined)
  }
  return []
}

export interface SerializableHighestAndBestUseSummary {
  id: number

  valuationSummary: number
  highestAndBestUseType: number

  AEPByEffectiveHectare: string
  AEPByImprovementsValue: string
  LWBByEffectiveHectare: string
  LWBByTotalHectare: string
  LWBBy_AEP: string
  improvementsAreaM2: string
  improvementsMarketValue: string
  improvementsPercentageOfMarketValue: string
  marketValue: string
  marketValueByEffectiveHectare: string
  marketValueByTotalHectare: string
  marketValueBy_AEP: string

  pvRatio: string
  totalFarmIncome: string

  totalHectares: string
  totalEffectiveHectares: string
  totalIneffectiveHectares: string
  totalNonTitledHectares: string
  totalNonTitledEffectiveHectares: string
  totalNonTitledIneffectiveHectares: string
  total_AEP: string
  total_LWB: string

  totalCanopyHectares: string
  totalCanopyValue: string
  nonCanopyValue: string
  canopyValueByTotalHectare: string
  canopyValueByCanopyHectare: string
  orchardValueByCanopyHectare: string
  AEPByCanopyHectare: string
  canopyValueBy_AEP: string
}

// this replaces the BestUseSummaryTotals stuff - key is that the main calcs should be on backend (source of truth, and we only recompute on frontend for vizualization purposes)
export interface SerializableValuationSummary {
  id: number

  sale?: number
  valuation: number
  comparableSale: number | null

  highestAndBestUseSummary: SerializableHighestAndBestUseSummary[]

  improvementsAreaM2: string
  improvementsMarketValue: string
  marketValue: string
  pvRatio: string
  totalHectares: string
  totalEffectiveHectares: string
  totalIneffectiveHectares: string
  totalNonTitledHectares: string
  totalNonTitledEffectiveHectares: string
  totalNonTitledIneffectiveHectares: string
  totalUnallocatedHectares: string
  total_LWB: string

  totalAdjustmentPercent: string
  lwbAdjustmentPercent: string
}
