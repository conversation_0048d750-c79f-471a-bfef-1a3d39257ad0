import {
  aspectColors,
  aspectDescriptions,
  aspectKeys,
  contourColors,
  contourDescriptions,
  contourKeys,
  lucColors,
  lucDescriptions,
  lucKeys,
  particleSizeColors,
  particleSizeDescriptions,
  particleSizeKeys,
  vegetationColors,
  vegetationDescriptions,
  vegetationKeys,
} from '../variables/definitions'

export interface LegendValue {
  label: string
  color: string
}
export interface LayerLookup {
  [layerPropertyName: string]: LayerLegend
}

export function createDictionary(
  keys: string[],
  descriptions: string[],
  colors: string[]
) {
  const dictionary: { [key: string]: LegendValue } = {}
  if (keys.length !== descriptions.length || keys.length !== colors.length) {
    console.error(
      `ERROR: Lengths of arrays do not match - (${keys.length}, ${descriptions.length}, ${colors.length})`
    )
    return dictionary
  }
  for (let i = 0; i <= keys.length; i++) {
    dictionary[keys[i]] = {
      label: descriptions[i],
      color: colors[i],
    }
  }
  return dictionary
}

const contourLegend: LayerLegend = {
  name: 'Contour',
  legendEntries: createDictionary(
    contourKeys,
    contourDescriptions,
    contourColors
  ),
  getDescriptor(fieldValue: string) {
    if (
      fieldValue === undefined ||
      this.legendEntries[fieldValue] === undefined
    ) {
      return this.legendEntries['-9999'].label
    }
    return this.legendEntries[fieldValue].label
  },

  getColor(fieldValue?: string) {
    if (
      fieldValue === undefined ||
      this.legendEntries[fieldValue] === undefined
    ) {
      return this.legendEntries['-9999'].color
    }
    return this.legendEntries[fieldValue].color
  },
}

const particleSizeLegend: LayerLegend = {
  name: 'Soil',
  legendEntries: createDictionary(
    particleSizeKeys,
    particleSizeDescriptions,
    particleSizeColors
  ),
  getDescriptor(fieldValue: string) {
    if (
      fieldValue === undefined ||
      this.legendEntries[fieldValue] === undefined
    ) {
      return this.legendEntries['-9999'].label
    }
    return this.legendEntries[fieldValue].label
  },

  getColor(fieldValue?: string) {
    if (
      fieldValue === undefined ||
      this.legendEntries[fieldValue] === undefined
    ) {
      return this.legendEntries['-9999'].color
    }
    return this.legendEntries[fieldValue].color
  },
}

const vegetationLegend: LayerLegend = {
  name: 'Vegetation',
  legendEntries: createDictionary(
    vegetationKeys,
    vegetationDescriptions,
    vegetationColors
  ),
  getDescriptor(fieldValue: string) {
    if (
      fieldValue === undefined ||
      this.legendEntries[fieldValue] === undefined
    ) {
      return this.legendEntries['-9999'].label
    }
    return this.legendEntries[fieldValue].label
  },

  getColor(fieldValue?: string) {
    if (
      fieldValue === undefined ||
      this.legendEntries[fieldValue] === undefined
    ) {
      return this.legendEntries['-9999'].color
    }
    return this.legendEntries[fieldValue].color
  },
}

const lucLegend: LayerLegend = {
  name: 'Land Use Classification',
  legendEntries: createDictionary(lucKeys, lucDescriptions, lucColors),
  getDescriptor(fieldValue: string) {
    if (
      fieldValue === undefined ||
      this.legendEntries[fieldValue] === undefined
    ) {
      return this.legendEntries['-9999'].label
    }
    return this.legendEntries[fieldValue].label
  },
  hasDescriptions: false,
  getColor(fieldValue?: string) {
    if (
      fieldValue === undefined ||
      this.legendEntries[fieldValue] === undefined
    ) {
      return this.legendEntries['-9999'].color
    }
    return this.legendEntries[fieldValue].color
  },
}

const smapLegend: LayerLegend = {
  name: 'Smap',
  legendEntries: createDictionary(lucKeys, lucDescriptions, lucColors),
  getDescriptor(fieldValue: string) {
    if (
      fieldValue === undefined ||
      this.legendEntries[fieldValue] === undefined
    ) {
      return this.legendEntries['-9999'].label
    }
    return this.legendEntries[fieldValue].label
  },

  getColor(fieldValue?: string) {
    if (
      fieldValue === undefined ||
      this.legendEntries[fieldValue] === undefined
    ) {
      return this.legendEntries['-9999'].color
    }
    return this.legendEntries[fieldValue].color
  },
}

const landcareLucLegend: LayerLegend = {
  name: 'Land Use Classification',
  legendEntries: createDictionary(lucKeys, lucDescriptions, lucColors),
  getDescriptor(fieldValue: string) {
    if (
      fieldValue === undefined ||
      this.legendEntries[fieldValue] === undefined
    ) {
      return this.legendEntries['-9999'].label
    }
    return this.legendEntries[fieldValue].label
  },

  getColor(fieldValue?: string) {
    if (
      fieldValue === undefined ||
      this.legendEntries[fieldValue] === undefined
    ) {
      return this.legendEntries['-9999'].color
    }
    return this.legendEntries[fieldValue].color
  },
}

const aspectLegend: LayerLegend = {
  name: 'Aspect',
  legendEntries: createDictionary(aspectKeys, aspectDescriptions, aspectColors),
  getDescriptor(fieldValue: string) {
    if (
      fieldValue === undefined ||
      this.legendEntries[fieldValue] === undefined
    ) {
      return this.legendEntries['-9999'].label
    }
    return this.legendEntries[fieldValue].label
  },

  getColor(fieldValue?: string) {
    if (
      fieldValue === undefined ||
      this.legendEntries[fieldValue] === undefined
    ) {
      return this.legendEntries['-9999'].color
    }
    return this.legendEntries[fieldValue].color
  },
}

/*
const titlesLegend: LayerLegend = {
    name: 'Titles',
    legendEntries: createDictionary(lucKeys, lucDescriptions, lucColors),
    getDescriptor(fieldValue: string) {
        if (fieldValue === undefined || this.legendEntries[fieldValue] === undefined) {
            return this.legendEntries['-9999'].label;
        }
        return this.legendEntries[fieldValue].label;
    },

    getColor(fieldValue?: string) {
        if (fieldValue === undefined || this.legendEntries[fieldValue] === undefined) {
            return this.legendEntries['-9999'].color;
        }
        return this.legendEntries[fieldValue].color;
    }
}
*/

export interface LegendValue {
  label: string
  description?: string
  color: string
}

export interface LayerLegend {
  name: string
  legendEntries: { [key: string]: LegendValue }
  hasGlossary?: boolean
  getGlossary?: () => string | null
  hasDescriptions?: boolean
  getDescriptor(fieldValue: string): string
  getColor(fieldValue?: string): string
}

const layerDictionary: LayerLookup = {
  contour: contourLegend,
  ps: particleSizeLegend,
  vegetation: vegetationLegend,
  luc: lucLegend,
  smap: smapLegend,
  landcareLuc: landcareLucLegend,
  aspect: aspectLegend,
}

export const getLegend = (key: string) => {
  if (layerDictionary[key] !== undefined) {
    return layerDictionary[key]
  }

  return undefined
}
