import type {
  BaseQueryFn,
  MutationDefinition,
} from '@reduxjs/toolkit/dist/query'
import type { UseMutation } from '@reduxjs/toolkit/dist/query/react/buildHooks'
import { message } from 'antd'
import { useCallback, useMemo } from 'react'
import { getQueryErrorMessage } from '@util/error'

/**
 *
 * @param hook RTK destroy hook
 * @returns Destroy hook with first argument replaced by a function that calls it and displays an AntD message
 */
function useDestroyMutation<U, V extends BaseQueryFn, W>(
  hook: UseMutation<MutationDefinition<U, V, string, W>>
) {
  const [destroy, status] = hook()

  const destroyWithMessage = useCallback(
    async (...args: Parameters<typeof destroy>) => {
      try {
        await destroy(...args).unwrap()
        void message.success('Record deleted')
      } catch (exception) {
        void message.error(getQueryErrorMessage(exception))
      }
    },
    [destroy]
  )

  // Types aren't inferred correctly without this declaration
  const tuple: [typeof destroyWithMessage, typeof status] = useMemo(
    () => [destroyWithMessage, status],
    [destroyWithMessage, status]
  )

  return tuple
}

export default useDestroyMutation
