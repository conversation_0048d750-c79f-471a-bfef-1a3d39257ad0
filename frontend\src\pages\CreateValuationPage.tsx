import React from 'react'
import { Helmet } from 'react-helmet'
import ProtectedRoute from '@components/ProtectedRoute'
import CreateValuationView from '@components/valuation/CreateValuationView'

const CreateValuationPage = () => {
  return (
    <>
      <Helmet>
        <title>Create New Valuation</title>
      </Helmet>
      <ProtectedRoute requiredEntitlements={['client:propertyflow:ria:create']}>
        <CreateValuationView />
      </ProtectedRoute>
    </>
  )
}

export default CreateValuationPage
