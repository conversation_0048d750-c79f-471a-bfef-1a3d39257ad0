import { baseApi as api } from "../baseApi";
export const addTagTypes = ["agri-uplift", "finance"] as const;
const injectedRtkApi = api
  .enhanceEndpoints({
    addTagTypes,
  })
  .injectEndpoints({
    endpoints: (build) => ({
      financeAgriUpliftList: build.query<
        FinanceAgriUpliftListApiResponse,
        FinanceAgriUpliftListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/finance/agri-uplift/`,
          params: {
            creator: queryArg.creator,
            current_user: queryArg.currentUser,
            order_by: queryArg.orderBy,
            page: queryArg.page,
            report_type: queryArg.reportType,
            size: queryArg.size,
            status: queryArg.status,
            trading_group: queryArg.tradingGroup,
            trading_group_region: queryArg.tradingGroupRegion,
          },
        }),
        providesTags: ["agri-uplift"],
      }),
      financeAgriUpliftCreate: build.mutation<
        FinanceAgriUpliftCreateApiResponse,
        FinanceAgriUpliftCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/finance/agri-uplift/`,
          method: "POST",
          body: queryArg.agriUplift,
        }),
        invalidatesTags: ["agri-uplift"],
      }),
      financeAgriUpliftRetrieve: build.query<
        FinanceAgriUpliftRetrieveApiResponse,
        FinanceAgriUpliftRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/finance/agri-uplift/${queryArg.pk}/`,
        }),
        providesTags: ["agri-uplift"],
      }),
      financeAgriUpliftUpdate: build.mutation<
        FinanceAgriUpliftUpdateApiResponse,
        FinanceAgriUpliftUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/finance/agri-uplift/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.agriUplift,
        }),
        invalidatesTags: ["agri-uplift"],
      }),
      financeAgriUpliftPartialUpdate: build.mutation<
        FinanceAgriUpliftPartialUpdateApiResponse,
        FinanceAgriUpliftPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/finance/agri-uplift/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedAgriUplift,
        }),
        invalidatesTags: ["finance"],
      }),
      financeAgriUpliftDestroy: build.mutation<
        FinanceAgriUpliftDestroyApiResponse,
        FinanceAgriUpliftDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/finance/agri-uplift/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["agri-uplift"],
      }),
      financeAgriUpliftByTradingGroupIdRetrieve: build.query<
        FinanceAgriUpliftByTradingGroupIdRetrieveApiResponse,
        FinanceAgriUpliftByTradingGroupIdRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/finance/agri-uplift/${queryArg.pk}/by_trading_group_id/`,
        }),
        providesTags: ["agri-uplift"],
      }),
      financeAgriUpliftEligibleFacilitiesForList: build.query<
        FinanceAgriUpliftEligibleFacilitiesForListApiResponse,
        FinanceAgriUpliftEligibleFacilitiesForListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/finance/agri-uplift/eligible-facilities-for/${queryArg.pk}/`,
        }),
        providesTags: ["finance"],
      }),
      financeAgriUpliftProductRetrieve: build.query<
        FinanceAgriUpliftProductRetrieveApiResponse,
        FinanceAgriUpliftProductRetrieveApiArg
      >({
        query: () => ({ url: `/api/finance/agri-uplift/product/` }),
        providesTags: ["agri-uplift"],
      }),
    }),
    overrideExisting: false,
  });
export { injectedRtkApi as enhancedApi };
export type FinanceAgriUpliftListApiResponse =
  /** status 200  */ PaginatedAgriUpliftList;
export type FinanceAgriUpliftListApiArg = {
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  creator?: number[];
  currentUser?: boolean;
  /** Ordering
    
    * `expiryDate` - Expirydate
    * `-expiryDate` - Expirydate (descending)
    * `createdDatetime` - Createddatetime
    * `-createdDatetime` - Createddatetime (descending)
    * `updatedDatetime` - Updateddatetime
    * `-updatedDatetime` - Updateddatetime (descending)
    * `loanAmount` - Loanamount
    * `-loanAmount` - Loanamount (descending) */
  orderBy?: (
    | "-createdDatetime"
    | "-expiryDate"
    | "-loanAmount"
    | "-updatedDatetime"
    | "createdDatetime"
    | "expiryDate"
    | "loanAmount"
    | "updatedDatetime"
  )[];
  /** A page number within the paginated result set. */
  page?: number;
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  reportType?: number[];
  /** Number of results to return per page. */
  size?: number;
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  status?: ("completed" | "draft")[];
  tradingGroup?: string;
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  tradingGroupRegion?: number[];
};
export type FinanceAgriUpliftCreateApiResponse = /** status 201  */ AgriUplift;
export type FinanceAgriUpliftCreateApiArg = {
  agriUplift: AgriUplift;
};
export type FinanceAgriUpliftRetrieveApiResponse =
  /** status 200  */ AgriUplift;
export type FinanceAgriUpliftRetrieveApiArg = {
  /** A unique integer value identifying this agri uplift. */
  pk: number;
};
export type FinanceAgriUpliftUpdateApiResponse = /** status 200  */ AgriUplift;
export type FinanceAgriUpliftUpdateApiArg = {
  /** A unique integer value identifying this agri uplift. */
  pk: number;
  agriUplift: AgriUplift;
};
export type FinanceAgriUpliftPartialUpdateApiResponse =
  /** status 200  */ AgriUplift;
export type FinanceAgriUpliftPartialUpdateApiArg = {
  /** A unique integer value identifying this agri uplift. */
  pk: number;
  patchedAgriUplift: PatchedAgriUplift;
};
export type FinanceAgriUpliftDestroyApiResponse = unknown;
export type FinanceAgriUpliftDestroyApiArg = {
  /** A unique integer value identifying this agri uplift. */
  pk: number;
};
export type FinanceAgriUpliftByTradingGroupIdRetrieveApiResponse =
  /** status 200  */ AgriUplift;
export type FinanceAgriUpliftByTradingGroupIdRetrieveApiArg = {
  pk: string;
};
export type FinanceAgriUpliftEligibleFacilitiesForListApiResponse =
  /** status 200  */ AgriUpliftEligibleFacility[];
export type FinanceAgriUpliftEligibleFacilitiesForListApiArg = {
  pk: string;
};
export type FinanceAgriUpliftProductRetrieveApiResponse =
  /** status 200  */ AgriUpliftProduct;
export type FinanceAgriUpliftProductRetrieveApiArg = void;
export type TradingGroup = {
  tradingGroupId: string;
  regionName?: string;
  anzsic?: string;
  tradingGroupNumber: number;
  tradingGroupName: string;
  riskGroupId?: string | null;
  deleted: number;
  searchVector?: string | null;
  industry?: number | null;
};
export type CustomerEmissionsReportAttachment = {
  pk: number;
  attachment?: string;
  fileName: string;
  url: string;
  attachmentTypeId: number;
  reportId: number;
  reportTypeId: number;
};
export type CustomerEmissionsMetricType = {
  pk: number;
  name: string;
  unit: string;
  isSection?: boolean;
};
export type CustomerEmissionsMetric = {
  pk: number;
  metricTypeId: number;
  reportTypeId: number;
  value: string;
  metricType: CustomerEmissionsMetricType;
};
export type CustomerEmissionsReport = {
  attachments?: CustomerEmissionsReportAttachment[];
  pk: number;
  reportType: number;
  reportTypeName: string;
  status: "draft" | "completed";
  reportingPeriod?: string[] | null;
  metrics?: CustomerEmissionsMetric[];
};
export type AgriUpliftFacility = {
  pk: number;
  key: string;
  loanId: number;
  drawdownDate: string;
  expiration: string | null;
  eligible?: boolean;
  interestType: "Floating" | "Fixed";
};
export type AgriUpliftEmissionsSource = {
  pk: number;
  key: string;
  recordId: number;
  name: string;
  emissions?: CustomerEmissionsReport;
  facilities?: AgriUpliftFacility[];
  status?: "draft" | "completed";
};
export type AgriUpliftHistory = {
  userName: string;
  date: string;
};
export type AgriUplift = {
  pk: number;
  createdDatetime: string;
  updatedDatetime: string;
  tradingGroup: TradingGroup;
  tradingGroupId: string;
  emissionsSources: AgriUpliftEmissionsSource[];
  approvedMarginPercent?: string | null;
  status: "draft" | "completed";
  attestation?: boolean;
  loanAmount: string;
  expiryDate: string | null;
  creatorName: string;
  history: AgriUpliftHistory[];
};
export type PaginatedAgriUpliftList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: AgriUplift[];
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type PatchedAgriUplift = {
  pk?: number;
  createdDatetime?: string;
  updatedDatetime?: string;
  tradingGroup?: TradingGroup;
  tradingGroupId?: string;
  emissionsSources?: AgriUpliftEmissionsSource[];
  approvedMarginPercent?: string | null;
  status?: "draft" | "completed";
  attestation?: boolean;
  loanAmount?: string;
  expiryDate?: string | null;
  creatorName?: string;
  history?: AgriUpliftHistory[];
};
export type AgriUpliftEligibleFacility = {
  pk: number;
  key: string;
  accountSk: number;
  loanId: number;
  drawdownDate: string;
  accountNumber: string;
  accountType: string;
  entityName: string;
  limitDollars?: string;
  maturityDate?: string;
  hostProductIdentifier: string;
  productName: string;
  salesChannel: string;
  interestType: "Floating" | "Fixed";
  applicable: boolean;
};
export type CustomerEmissionsMetricTypeGroup = {
  pk: number;
  name: string;
  metricTypes: CustomerEmissionsMetricType[];
};
export type CustomerEmissionsReportTypeMetricType = {
  metricType: CustomerEmissionsMetricType;
  metricGroup: CustomerEmissionsMetricTypeGroup;
  sortOrder?: number | null;
  description?: string | null;
};
export type CustomerEmissionsAttachmentType = {
  pk: number;
  name: string;
  required: boolean;
};
export type CustomerEmissionsReportType = {
  pk: number;
  name: string;
  sourceName: string;
  metricTypes: CustomerEmissionsReportTypeMetricType[];
  attachmentTypes: CustomerEmissionsAttachmentType[];
};
export type AgriUpliftProduct = {
  eligibleProductIdentifier: "R200";
  reportTypes: CustomerEmissionsReportType[];
  lessDiscountPercent?: string;
};
export const {
  useFinanceAgriUpliftListQuery,
  useFinanceAgriUpliftCreateMutation,
  useFinanceAgriUpliftRetrieveQuery,
  useFinanceAgriUpliftUpdateMutation,
  useFinanceAgriUpliftPartialUpdateMutation,
  useFinanceAgriUpliftDestroyMutation,
  useFinanceAgriUpliftByTradingGroupIdRetrieveQuery,
  useFinanceAgriUpliftEligibleFacilitiesForListQuery,
  useFinanceAgriUpliftProductRetrieveQuery,
} = injectedRtkApi;
