import React from 'react'
import { Helmet } from 'react-helmet'
import ProtectedRoute from '@components/ProtectedRoute'
import { ExplorerView } from '@components/explorer'

const ExplorerPage = () => {
  return (
    <>
      <Helmet>
        <title>Explorer</title>
      </Helmet>
      <ProtectedRoute requiredEntitlements={['client:propertyflow:explorer:*']}>
        <ExplorerView />
      </ProtectedRoute>
    </>
  )
}

export default ExplorerPage
