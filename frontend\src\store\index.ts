import { configureStore } from '@reduxjs/toolkit'
import {
  type TypedUseSelectorHook,
  useDispatch,
  useSelector as useSelectorHook,
} from 'react-redux'
import { persistReducer, persistStore } from 'redux-persist'
import storage from 'redux-persist/lib/storage'
import {
  storeState,
  uiSaveSettingsToUser,
  uiSetSavedSettings,
} from './middleware'
import { rootReducer } from './rootReducer'
import { baseApi } from './services/baseApi'
import type { RootState } from './types'

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['customer'],
}

export const setupStore = (preloadedState?: Partial<RootState>) =>
  configureStore({
    reducer: persistReducer(persistConfig, rootReducer),
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        thunk: true,
        serializableCheck: false,
        immutableCheck: false,
      })
        .concat(baseApi.middleware)
        .concat(storeState)
        .concat(uiSetSavedSettings)
        .concat(uiSaveSettingsToUser),
    preloadedState,
  })

export const store = setupStore()
export const persistor = persistStore(store)

export type AppStore = ReturnType<typeof setupStore>
export type AppDispatch = AppStore['dispatch']

export const useSelector: TypedUseSelectorHook<RootState> = useSelectorHook
export const useAppDispatch: () => AppDispatch = useDispatch

export * from './types'
