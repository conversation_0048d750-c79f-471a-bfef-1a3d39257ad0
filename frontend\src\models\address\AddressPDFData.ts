import type { DistrictValuationRoll } from '../dvr/DistrictValuationRoll'
import type { PDFData } from '../generic/PDFData'
import type { AnzUnionFeatureCollection } from '../gis/AnzUnionFeatureCollection'
import type { TitleFeatureCollection } from '../title/TitleFeatureCollection'
import type { AddressFeature } from './AddressFeatureCollection'

export interface AddressPDFData extends PDFData {
  address: AddressFeature
  titles: TitleFeatureCollection
  districtValuationRoll: DistrictValuationRoll
  anzUnion: AnzUnionFeatureCollection
  summary: {
    landDescription: string
    serviceCentres: string
    elevation: string
    anzUnion: {
      luc: { [key: string]: number }
      ps: { [key: string]: number }
      vegetation: { [key: string]: number }
    }
  }
}
