import type { PageSummary } from './PageSummary'
import type { SaleSummary } from './SaleSummary'
import type { TimeSummary } from './TimeSummary'
import type { UserSummary } from './UserSummary'
import type { ValuationSummary } from './ValuationSummary'

export interface UsageSummary {
  pages: PageSummary[]
  users: UserSummary[]
  valuations: ValuationSummary[]
  time: TimeSummary[]
  sales: SaleSummary[]
}
