import {
  CheckOutlined,
  CopyFilled,
  DeleteOutlined,
  EditFilled,
  LoadingOutlined,
} from '@ant-design/icons'
import { <PERSON><PERSON>, Row, Space, message } from 'antd'
import ButtonGroup from 'antd/lib/button/button-group'
import React, { useCallback, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  useCloneValuationMutation,
  useUpdateValuationMutation,
} from '../../../../store/services/valuations'
import type { Valuation } from '../../../../types'
import { HoverButton } from '../../../generic/HoverButton'
import { PromptModal } from '../../../generic/PromptModal'
import { ValuationFormModal } from '../../ValuationFormModal'

const DELETE_VALUATION_MESSAGE =
  'Deleting this valuation will result in a loss of data associated with it.'
const COMPLETE_VALUATION_MESSAGE =
  "Completing this valuation will result in the valuation being 'locked' and placed in a read-only mode. You will not be able to make any further changes."

interface ValuationButtonGroupProps {
  valuation: Valuation
}

export const ValuerButtonGroup = (props: ValuationButtonGroupProps) => {
  const { valuation } = props

  const addressId = valuation.addressId
  const tradingGroupId = valuation.tradingGroupId ?? 'prospect'
  const valuationId = valuation.valuationId

  const [cloneValuationMutation] = useCloneValuationMutation()
  const [updateValuation] = useUpdateValuationMutation()

  const navigate = useNavigate()

  // TODO: move to backend / repeat on backend
  const disableCompleteModal =
    valuation.titleReviewStatus === undefined ||
    valuation.titleReviewStatus === null ||
    valuation.titleReviewStatus === 0 ||
    valuation.waterSecurityReviewStatus === null ||
    valuation.waterSecurityReviewStatus === undefined ||
    valuation.waterSecurityReviewStatus === 0 ||
    valuation.priorMarketValue === null ||
    valuation.priorMarketValue === undefined ||
    valuation.tier === null ||
    valuation.tier === undefined

  const [showForm, setShowForm] = useState(false)
  const handleCloseForm = () => setShowForm(false)
  const handleShowForm = () => setShowForm(true)

  const [showComplete, setShowComplete] = useState(false)
  const handleCloseComplete = () => setShowComplete(false)
  const handleShowComplete = () => setShowComplete(true)

  const [showDelete, setShowDelete] = useState(false)
  const handleCloseDelete = () => setShowDelete(false)
  const handleShowDelete = () => setShowDelete(true)

  const [cloning, setCloning] = useState(false)

  const cloneValuation = useCallback(async () => {
    setCloning(true)
    const result = await cloneValuationMutation({ addressId, valuationId })
    if ('error' in result) {
      const error = result.error
      void message.error(`Failed to clone valuation: ${JSON.stringify(error)}`)
    } else {
      void message.success('Cloned successfully.')
      navigate(`/valuations/${result.data.valuationId}`)
    }
    setCloning(false)
  }, [addressId, cloneValuationMutation, navigate, valuationId])

  const completeValuation = async () => {
    try {
      await updateValuation({
        valuationId,
        valuation: {
          completedDate: new Date().toISOString(),
        },
      }).unwrap()
    } catch (err) {
      console.error(err)
      void message.error(
        'Failed to complete valuation, unknown error occurred.'
      )
    }
    handleCloseComplete()
  }

  const deleteValuation = async () => {
    try {
      await updateValuation({
        valuationId,
        valuation: {
          deletedDate: new Date().toISOString(),
        },
      }).unwrap()
    } catch (err) {
      console.error(err)
      void message.error('Failed to delete valuation, unknown error occurred.')
    }
    handleCloseDelete()
    if (tradingGroupId.match(/\d{16}/g)) {
      navigate(`/trading-groups/${tradingGroupId}/`)
    } else {
      navigate('/')
    }
  }

  return (
    <ButtonGroup>
      <HoverButton onClick={handleShowForm} icon={<EditFilled />}>
        Edit
      </HoverButton>
      <HoverButton
        onClick={() => cloneValuation()}
        icon={!cloning ? <CopyFilled /> : <LoadingOutlined />}
        disabled={cloning}
      >
        Copy
      </HoverButton>
      <HoverButton
        className="approve-button"
        onClick={handleShowComplete}
        icon={<CheckOutlined />}
      >
        Complete
      </HoverButton>
      <HoverButton
        className="reject-button"
        onClick={handleShowDelete}
        icon={<DeleteOutlined />}
      >
        Delete
      </HoverButton>
      <ValuationFormModal
        key={valuation.id}
        show={showForm}
        handleClose={handleCloseForm}
        addressId={addressId}
        selectedValuation={valuation}
      />
      <PromptModal
        show={showComplete}
        handleClose={handleCloseComplete}
        header="Confirm Valuation Completion"
        okButtonProps={{
          disabled: disableCompleteModal,
        }}
        body={
          <Space direction="vertical">
            {!valuation.titleReviewStatus ? (
              <Alert
                type="error"
                message={
                  'Please review the title data under Property Details/Titles and confirm the correctness before completing this valuation.'
                }
              />
            ) : null}
            {!valuation.waterSecurityReviewStatus ? (
              <Alert
                type="error"
                message={
                  'Please review the water security status in Property Details/Consents and confirm the correctness before completing this valuation.'
                }
              />
            ) : null}
            {!valuation.priorMarketValue ? (
              <Alert
                type="error"
                message={
                  'Please provide information about the prior market value before completing this valuation.'
                }
              />
            ) : null}
            {!valuation.tier ? (
              <Alert
                type="error"
                message={
                  'Please provide a value for the tier before completing this valuation.'
                }
              />
            ) : null}
            <Row>{COMPLETE_VALUATION_MESSAGE}</Row>
          </Space>
        }
        handleConfirm={completeValuation}
      />
      <PromptModal
        show={showDelete}
        handleClose={handleCloseDelete}
        header="Confirm Valuation Deletion"
        body={DELETE_VALUATION_MESSAGE}
        handleConfirm={deleteValuation}
      />
    </ButtonGroup>
  )
}
