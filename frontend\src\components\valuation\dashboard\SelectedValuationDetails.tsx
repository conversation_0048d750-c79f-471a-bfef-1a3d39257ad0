import { AuditOutlined, BarsOutlined, BlockOutlined } from '@ant-design/icons'
import { Table } from 'antd'
import React, { useEffect } from 'react'
import { useDispatch } from 'react-redux'
import { Widget } from '@components/generic'
import { TitleNumberLabel } from '@components/generic/TitleNumberLabel'
import { setRecentEntry } from '@store/ui/actions'
import type { DistrictValuationRoll } from '@models/dvr/DistrictValuationRoll'
import type {
  TitleFeature,
  TitleFeatureCollection,
} from '@models/title/TitleFeatureCollection'
import type { Valuation } from '@types'
import { ValuationPageInformationTable } from '../page/widgets/valuation/ValuationPageInformationTable'

interface SelectedValuationDetailsProps {
  valuation: Valuation
  valuationTitles: TitleFeatureCollection
  valuationDvrs: DistrictValuationRoll[]
}

const convertThousands = (text: string) => {
  if (!text) {
    return ''
  }
  const value = Number(text)
  return Number(value / 1e3).toLocaleString()
}

export const SelectedValuationDetails = (
  props: SelectedValuationDetailsProps
) => {
  const { valuation, valuationTitles, valuationDvrs } = props

  const dispatch = useDispatch()

  useEffect(() => {
    if (valuation) {
      dispatch(
        setRecentEntry({
          categoryName: 'valuations',
          value: { ...valuation },
        })
      )
    }
  }, [valuation, dispatch])

  return (
    <React.Fragment>
      <Widget title="Valuation Summary" icon={<BarsOutlined />}>
        <ValuationPageInformationTable valuation={valuation} />
      </Widget>
      <Widget title="Linked Titles" icon={<BlockOutlined />}>
        <div className="agrigis-table">
          <Table
            rowKey={(row) => row.properties.titleNo}
            size="small"
            pagination={false}
            dataSource={valuationTitles.features}
            columns={[
              {
                dataIndex: 'titleNo',
                title: 'Title',
                render: (_: string, row: TitleFeature) => {
                  return <TitleNumberLabel titleProperties={row.properties} />
                },
              },
              {
                dataIndex: ['properties', 'estateDescription'],
                title: 'Description',
              },
              {
                dataIndex: ['properties', 'surveyArea'],
                title: 'Ha',
                render: (text: string) => {
                  return text ? `${(Number(text) / 1e4).toFixed(2)}` : ''
                },
              },
            ]}
          />
        </div>
      </Widget>
      <Widget title="District Valuation Rolls" icon={<AuditOutlined />}>
        <div className="agrigis-table">
          <Table
            rowKey={(row) => row.dvrId}
            size="small"
            pagination={false}
            dataSource={valuationDvrs}
            columns={[
              { dataIndex: 'valRef', title: 'Val Ref' },
              { dataIndex: 'landUseDesc', title: 'Land Use' },
              { dataIndex: 'landZoneDesc', title: 'Land Zone' },
              {
                dataIndex: 'cv',
                title: 'CV ($K)',
                render: (text: string) => convertThousands(text),
              },
              {
                dataIndex: 'iv',
                title: 'IV ($K)',
                render: (text: string) => convertThousands(text),
              },
              {
                dataIndex: 'lv',
                title: 'LV ($K)',
                render: (text: string) => convertThousands(text),
              },
            ]}
          />
        </div>
      </Widget>
    </React.Fragment>
  )
}
