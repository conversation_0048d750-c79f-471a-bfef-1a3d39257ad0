import { FeatureCollection, Geometry } from 'geojson'
import { RVRValuation } from '@models/valuations/RVRValuation'
import { WaterConsent } from '@models/valuations/WaterConsent'
import { PaginatedResponse } from '../../../models/generic/PaginatedResponse'
import { ConsentFile } from '../../../models/valuations/ConsentFile'
import { ValuationPDFData } from '../../../models/valuations/ValuationPDFData'
import { ValuationSummary } from '../../../models/valuations/ValuationSummary'
import { ValuationSearchPayload } from '../../../models/valuations/util/ValuationSearchPayload'
import {
  Rainfall,
  ResourceConsent,
  Valuation,
  ValuationTitleMemorial,
} from '../../../types'
import { baseApi } from '../baseApi'

export const valuationApi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    createRVRValuation: build.mutation<
      Valuation,
      { valuation: Partial<Valuation>; rvrId: string }
    >({
      query: (body) => {
        return {
          url: `/api/v2/valuations/create_rvr_valuation/`,
          method: 'POST',
          body: body.valuation,
        }
      },
      invalidatesTags: (result, error, { rvrId }) => [
        'Valuations',
        { type: 'RVRValuations', id: rvrId },
      ],
    }),
    createValuation: build.mutation<
      Valuation,
      { valuation: Partial<Valuation> }
    >({
      query: (body) => {
        return {
          url: `/api/v2/valuations/`,
          method: 'POST',
          body: body.valuation,
        }
      },
      invalidatesTags: ['Valuations'],
    }),
    updateValuationTitles: build.mutation<
      void,
      { valuationId: string; titleIds: (string | number)[] }
    >({
      query: (body) => {
        return {
          url: `/api/v2/valuations/${body.valuationId}/titles/`,
          body: body?.titleIds,
          method: 'PUT',
        }
      },
      invalidatesTags: (result, error, { valuationId }) => [
        { type: 'ValuationSummary', id: valuationId?.toString() },
      ],
    }),
    updateValuation: build.mutation<
      Valuation,
      {
        valuationId: string | number
        valuation: Omit<Partial<Valuation>, 'rvrValuation'> & {
          rvrValuation?: Partial<RVRValuation>
        }
      }
    >({
      query: ({ valuationId, valuation }) => ({
        url: `/api/v2/valuations/${valuationId}/`,
        body: valuation,
        method: 'PATCH',
      }),
      invalidatesTags: (result, error, { valuationId }) => [
        { type: 'ValuationSummary', id: valuationId?.toString() },
        { type: 'RVRValuations' },
      ],
    }),
    getValuers: build.query<
      { username: string; id: number }[],
      string | undefined
    >({
      query: (match: string) =>
        match ? `/api/valuers/?match=${match}` : '/api/valuers/',
    }),
    getValuationSummary: build.query<ValuationSummary, string | number>({
      query: (valuationId) => {
        return `/api/v2/valuations/${valuationId}/summary/`
      },
      providesTags: (result, error, valuationId) => [
        { type: 'ValuationSummary', id: valuationId?.toString() },
      ],
    }),
    getValuationRainfall: build.query<Rainfall[], string>({
      query: (valuationId: string) => {
        return `/api/v2/valuations/${valuationId}/rainfall/`
      },
    }),
    getValuationResourceConsents: build.query<ResourceConsent[], string>({
      query: (valuationId: string) => {
        return `/api/v2/valuations/${valuationId}/resource_consents/`
      },
    }),
    getValuationTitleMemorials: build.query<ValuationTitleMemorial[], string>({
      query: (valuationId: string) => {
        return `/api/v2/valuations/${valuationId}/memorials/`
      },
    }),
    getValuationWaterConsents: build.query<
      FeatureCollection<Geometry, WaterConsent>,
      string
    >({
      query: (valuationId: string) => {
        return `/api/v2/valuations/${valuationId}/water_consents/`
      },
    }),
    // Back-up search viewset incase slowness persists with tomorrow's image change
    getMinimalValuations: build.query<PaginatedResponse<Valuation[]>, number>({
      query: (page) => `/api/minimal_valuations/?page=${page ?? 1}`,
    }),
    updateValuationTitleReview: build.mutation<
      any,
      {
        valuationId: string
        titleReviewStatus: number
        titleReviewComments: string
      }
    >({
      query: ({ valuationId, titleReviewStatus, titleReviewComments }) => ({
        url: `/api/v2/valuations/${valuationId}/summary/title_review/`,
        method: 'PUT',
        body: {
          titleReviewStatus,
          titleReviewComments,
        },
      }),
      invalidatesTags: (result, error, { valuationId }) => [
        { type: 'ValuationSummary', id: valuationId },
      ],
    }),
    updateValuationWaterSecurityReview: build.mutation<
      any,
      {
        valuationId: string
        waterSecurityReviewStatus: number
        waterSecurityReviewComments: string
      }
    >({
      query: ({
        valuationId,
        waterSecurityReviewStatus,
        waterSecurityReviewComments,
      }) => ({
        url: `/api/v2/valuations/${valuationId}/summary/water_security_review/`,
        method: 'PUT',
        body: {
          waterSecurityReviewStatus,
          waterSecurityReviewComments,
        },
      }),
      invalidatesTags: (result, error, { valuationId }) => [
        { type: 'ValuationSummary', id: valuationId },
      ],
    }),
    uploadConsentFile: build.mutation<
      void,
      { valuationId: string; file: File; fileDescription: string }
    >({
      query: ({ valuationId, file, fileDescription }) => {
        const blob = new File([file], file.name, { type: file.type })

        const formData = new FormData()
        formData.append('blob', blob, file.name)
        formData.append('fileDescription', fileDescription)

        return {
          method: 'POST',
          url: `/api/v2/valuations/${valuationId}/consent_files/`,
          body: formData,
          headers: {
            'Content-Type': undefined,
          },
        }
      },
      invalidatesTags: (result, error, { valuationId }) => [
        { type: 'ConsentFiles', id: valuationId },
      ],
    }),
    deleteConsentFile: build.mutation<
      void,
      { valuationId: string; consentFile: ConsentFile }
    >({
      query: ({ valuationId, consentFile }) => {
        return {
          method: 'DELETE',
          url: `/api/v2/valuations/${valuationId}/consent_files/${consentFile.id}/`,
        }
      },
      invalidatesTags: (result, error, { valuationId }) => [
        { type: 'ConsentFiles', id: valuationId },
      ],
    }),
    getConsentFiles: build.query<ConsentFile[], string>({
      query: (valuationId) =>
        `/api/v2/valuations/${valuationId}/consent_files/`,
      providesTags: (result, error, valuationId) => [
        { type: 'ConsentFiles', id: valuationId },
      ],
    }),
    cloneValuation: build.mutation<
      Valuation,
      { addressId: string; valuationId: string; body?: { name?: string } }
    >({
      query: ({ valuationId, addressId, body }) => ({
        url: `/api/address/${addressId}/valuations/${valuationId}/clone/`,
        method: 'POST',
        body,
      }),
    }),
    getValuationPDFData: build.query<ValuationPDFData, { valuationId: string }>(
      {
        query: (body) => `/api/v2/valuations/${body?.valuationId}/pdf/`,
        providesTags: (result, error, args) => [
          { type: 'ValuationPDFData', id: args?.valuationId },
        ],
      }
    ),
  }),
})

export const {
  useGetValuersQuery,
  useLazyGetValuersQuery,
  useGetMinimalValuationsQuery,
  useGetValuationSummaryQuery,
  useUpdateValuationTitleReviewMutation,
  useUploadConsentFileMutation,
  useGetConsentFilesQuery,
  useUpdateValuationWaterSecurityReviewMutation,
  useCloneValuationMutation,
  useCreateValuationMutation,
  useUpdateValuationTitlesMutation,
  useGetValuationPDFDataQuery,
  useCreateRVRValuationMutation,
  useUpdateValuationMutation,
  useGetValuationRainfallQuery,
  useGetValuationResourceConsentsQuery,
  useGetValuationTitleMemorialsQuery,
  useGetValuationWaterConsentsQuery,
  useDeleteConsentFileMutation,
} = valuationApi
