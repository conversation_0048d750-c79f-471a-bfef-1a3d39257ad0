# Frontend-RiskRadar Integration Setup

This document outlines the steps to integrate the frontend directly with the RiskRadar Django backend, bypassing the BFF.

## Changes Made

### 1. Frontend Configuration
- **baseApi.ts**: Configured to connect directly to `http://localhost:8000` (RiskRadar Django backend)
- **const.ts**: API_HOST points to RiskRadar backend
- Frontend already uses RTK Query with proper CSRF token handling

### 2. Django Backend Configuration
- **urls.py**: Commented out BFF routes (`/bff/` endpoints)
- **settings.py**: Added CORS support and static file configuration for frontend
  - Added `corsheaders` to INSTALLED_APPS
  - Added CORS middleware
  - Configured CORS_ALLOWED_ORIGINS for frontend dev server
  - Added frontend build directory to STATICFILES_DIRS and TEMPLATES

### 3. Dependencies
- Created `requirements.txt` with necessary packages:
  - Django
  - django-ninja
  - django-cors-headers

## Setup Instructions

### 1. Install Python Dependencies
```bash
cd riskradar_apps
pip install -r ../requirements.txt
```

### 2. Install Frontend Dependencies
```bash
cd frontend
pnpm install
```

### 3. Build Frontend (for production)
```bash
cd frontend
pnpm run build
```

### 4. Run Development Servers

#### Backend (RiskRadar Django)
```bash
cd riskradar_apps
python manage.py runserver 8000
```

#### Frontend (Development)
```bash
cd frontend
pnpm start
# This will run on http://localhost:3000 and proxy API calls to Django backend
```

## API Endpoints

The frontend connects directly to RiskRadar Django backend at:
- **Development**: `http://localhost:8000/api/`
- **Production**: `/api/` (relative to Django server)

### Available API Routes
- `/api/perils/` - Perils management
- `/api/locations/` - Locations management  
- `/api/loss-models/` - Loss models management

## Frontend Routes

The RiskRadar frontend is available at:
- **Development**: `http://localhost:3000/riskradar/`
- **Production**: `http://localhost:8000/riskradar/` (served by Django)

## Notes

- BFF components are commented out but not deleted (can be re-enabled if needed)
- CORS is configured for development (localhost:3000)
- Frontend build outputs to `/static/` base path for Django static files
- Django serves the React app's index.html at the root path
