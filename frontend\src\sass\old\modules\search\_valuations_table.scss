.valuations-table {
  .valuation-table-actions {
    @include row-flex;
  }

  tr {
    &.completed {
      .valuations-table-address-text {
        &::before {
          content: '✓';
          font-weight: 400;
          color: rgba(0, 155, 0, 0.8);
          font-size: 12px;
          margin-right: 10px;
        }
      }
    }
  }

  td {
    .valuations-table-address {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      text-align: left;

      .valuations-table-address-text {
        font-weight: 600;
        color: $grey-5;
      }

      .valuations-table-address-ul {
        padding: 0;
        margin: 0;

        li {
          @include detail-li;

          &.ref::before {
            content: 'REF';
          }

          &.name::before {
            content: 'NAME';
          }

          &.type::before {
            content: 'TYPE';
          }

          &.created::before {
            content: 'CREATED';
          }
        }
      }
    }

    span {
      &.unlinked {
        color: $west-coast-sunset;
      }

      &.prospect {
        color: $grey-3;
      }
    }
  }
}
