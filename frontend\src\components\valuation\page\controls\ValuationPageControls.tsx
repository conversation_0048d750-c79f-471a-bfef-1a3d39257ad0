import { Select } from 'antd'
import classNames from 'classnames'
import type React from 'react'
import { useCallback, useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { ButtonWidget } from '@components/generic'
import type { RootState } from '@store'
import { uiSelectors } from '@store/ui'
import { setLayoutValue } from '@store/ui/actions'
import type { Valuation } from '../../../../types'
import { EditButtonGroup } from './EditButtonGroup'
import { ExportButtonGroup } from './ExportButtonGroup'
import styles from './ValuationPageControls.module.scss'

interface ValuationPageControlsProps {
  valuation: Valuation
  disabled?: boolean
}

const selector = (state: RootState, props: ValuationPageControlsProps) => {
  return {
    ...props,
    layoutState: uiSelectors.getLayoutState(state, 'valuationPage'),
  }
}

export const ValuationPageControls = ({
  disabled,
  ...props
}: ValuationPageControlsProps) => {
  const dispatch = useDispatch()

  const { valuation, layoutState } = useSelector((state: RootState) =>
    selector(state, props)
  )

  const layoutDispatch = useCallback(
    (payload: { type: string; value: unknown }) => {
      dispatch(
        setLayoutValue({
          pageName: 'valuationPage',
          layoutKey: payload?.type,
          layoutValue: payload?.value,
        })
      )
    },
    [dispatch]
  )

  const { frontlineCreated } = valuation

  const options: {
    value: string
    label: string
    isValuer?: boolean
  }[] = useMemo(
    () => [
      { value: 'PROPERTY_INFORMATION', label: 'Property Details' },
      {
        value: 'TRADING_GROUP_INFORMATION',
        label: 'Trading Group Information',
      },
      {
        value: 'VALUATION_INFORMATION',
        label: 'Valuation Information',
        disabled: frontlineCreated ?? disabled,
      },
      { value: 'PHYSICAL_PROPERTIES', label: 'Physical Properties' },
      {
        value: 'CARBON_MODELLING',
        label: 'Carbon Modelling',
        disabled: disabled,
      },
      {
        value: 'SALES_AND_LISTINGS',
        label: 'Sales & Listings',
        disabled: disabled || frontlineCreated,
      },
    ],
    [disabled, frontlineCreated]
  )

  return (
    <div className={classNames('ValuationPageControls', styles.container)}>
      <ButtonWidget className={styles.buttons}>
        <EditButtonGroup
          disabled={disabled}
          valuation={valuation}
          completedDate={valuation.completedDate}
        />
        <ExportButtonGroup valuation={valuation} />
      </ButtonWidget>
      <Select
        className={styles.select}
        onChange={(e: React.ChangeEvent<Element>) =>
          layoutDispatch({ type: 'selectedView', value: e })
        }
        options={options}
        defaultValue={layoutState?.selectedView || ''}
      />
    </div>
  )
}
