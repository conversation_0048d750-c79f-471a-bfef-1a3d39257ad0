.user-valuations {
  @include row-flex;
  justify-items: stretch;
  width: max-content;

  .user-valuations-widget {
    width: 100%;
    margin-bottom: $full;
  }

  .user-valuations-create {
    @extend .user-valuations-widget;
  }

  .current-user-valuations-header {
    @include col-flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $half;
  }

  .user-valuations-search {
    @include row-flex;

    .address-search-container {
      width: 100%;
    }

    .radio-controls {
      margin-top: $half;
      margin-bottom: $half;

      .radio-control {
        @include col-flex;
        align-items: center;
        margin-bottom: 2px;

        button {
          margin-right: 10px;
        }

        span {
          font-size: smaller;
          color: $grey-3;
        }
      }
    }
  }

  td {
    padding: 4px;
    width: min-content;
  }

  tr {
    width: min-content;

    &:hover {
      .user-valuations-td-action {
        &:not(.disabled) {
          color: tint($primary, 25);

          &:hover {
            color: shade($secondary, 25);
          }
        }

        &.disabled {
          color: $grey-3;
        }
      }
    }

    .user-valuations-td-content {
      @include col-flex;
      justify-content: space-between;
      align-items: center;
      transition: 0.2s;

      .user-valuations-td-action {
        color: transparent;
        transition: inherit;

        &:hover {
          color: shade($secondary, 25);
          cursor: pointer;

          &.disabled {
            color: $grey-3;
            cursor: not-allowed;
          }
        }
      }

      .user-valuations-td-action-link,
      .user-valuations-td-action-edit {
        @extend .user-valuations-td-action;
      }

      .user-valuations-td-action-link {
        margin-left: 10px;
      }

      .address-label-tag {
        @include row-flex;
      }
    }

    .user-valuations-td-actions {
      @include row-flex;
      align-items: flex-start;
    }
  }
}
