import { AlertOutlined, SaveOutlined } from '@ant-design/icons'
import { Alert, Button, Space, message } from 'antd'
import type { Feature, Point } from 'geojson'
import { isEqual } from 'lodash'
import React, { useCallback, useEffect, useMemo } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import { ButtonWidget } from '@components/generic'
import { Widget } from '@components/generic/Widget'
import type { RootState } from '@store'
import { useAssetSummaryStatistics } from '@store/features/assets/hooks'
import {
  useCommitComparableSalesMutation,
  useGetComparableSalesQuery,
  useGetValuationTypesQuery,
} from '@store/services/assets'
import { type Sale, useSalesListQuery } from '@store/services/sdk'
import { useGetValuationSummaryQuery } from '@store/services/valuations'
import { uiActions, uiSelectors } from '@store/ui'
import type { HighestAndBestUseSummary } from '@models/assets/Assets'
import {
  bestUseIsCompatible,
  compatibleBestUses,
} from '@models/assets/ValuationTypes'
import {
  type SaleEditState,
  getSaleCacheId,
} from '@models/sales/util/SaleEditState'
import SalesSearchFilters from '../../../../sales/dashboard/SalesSearchFilters'
import { ComparableSaleSearchTable } from './ComparableSaleSearchTable'

const pageName = 'comparableSales'
interface ComparableSaleSearchProps {
  center: Feature<Point>
  valuationId: string
}

const selector = (state: RootState) => {
  return {
    saleEditState: uiSelectors.getSaleEditState(state),
    isValuer: uiSelectors.isValuer(state) ?? false,
  }
}

export const ComparableSaleSearch = (props: ComparableSaleSearchProps) => {
  const { valuationId, center } = props
  const { saleEditState, isValuer } = useSelector(
    (state: RootState) => selector(state),
    shallowEqual
  )

  const { data: valuationTypes } = useGetValuationTypesQuery()

  const { summary: subjectPropertySummary } =
    useAssetSummaryStatistics(valuationId)

  const { data: subjectValuation } = useGetValuationSummaryQuery(valuationId)

  const dispatch = useDispatch()
  const saleFiltersState = useSelector((state: RootState) =>
    uiSelectors.getSaleSearchFilters(state, pageName)
  )

  const { data: loadedSales } = useSalesListQuery({
    ...saleFiltersState,
    type: 'vettedSales',
  })

  const { data: savedSales } = useGetComparableSalesQuery(valuationId)

  const [commitComparableSales] = useCommitComparableSalesMutation()

  const salesCurrentPage = useMemo(() => {
    return loadedSales?.currentPage ?? 0
  }, [loadedSales])

  const salesCount = useMemo(() => {
    return loadedSales?.count ?? 0
  }, [loadedSales])

  useEffect(() => {
    if (center) {
      dispatch(
        uiActions.setFilterValue({
          pageName: 'comparableSales',
          filterKey: 'distanceFrom',
          filterValue: center,
        })
      )
      dispatch(
        uiActions.setFilterValue({
          pageName: 'comparableSales',
          filterKey: 'orderBy',
          filterValue: ['-sale_date'],
        })
      )
    }
  }, [dispatch, center])

  useEffect(() => {
    if (subjectPropertySummary && valuationTypes) {
      dispatch(
        uiActions.setFilterValue({
          pageName: 'comparableSales',
          filterKey: 'highestAndBestUses',
          filterValue: subjectPropertySummary.highestAndBestUseSummary.map(
            (summary: HighestAndBestUseSummary) =>
              valuationTypes?.[
                summary.highestAndBestUseType
              ]?.highestAndBestUse?.toString() ?? ''
          ),
        })
      )
    }
  }, [dispatch, subjectPropertySummary, valuationTypes])

  useEffect(() => {
    const _saleEditState: SaleEditState = { ...saleEditState }

    for (const [saleId, saleEdit] of Object.entries(_saleEditState)) {
      if (saleEdit.state === 'NEW' || saleEdit.state === 'EXISTING') {
        delete _saleEditState[saleId]
      }
    }

    for (const comparableSale of savedSales || []) {
      const sale: Sale = {
        id: comparableSale.linkedSale.id,
        type: 'Feature',
        geometry: comparableSale.geometry,
        properties: {
          ...comparableSale.linkedSale,
          fullAddress: comparableSale.fullAddress,
        },
      }

      const saleCacheId = getSaleCacheId(
        sale.id,
        comparableSale.saleHighestAndBestUseType ?? -1
      )
      _saleEditState[saleCacheId] = {
        ..._saleEditState[saleCacheId],
        state: _saleEditState[saleCacheId]?.state || 'EXISTING',
        highestAndBestUseType: comparableSale.highestAndBestUseType,
        saleHighestAndBestUseType: comparableSale.saleHighestAndBestUseType,
        isCompatibleWithSubject: valuationTypes
          ? bestUseIsCompatible(
              valuationTypes[comparableSale.highestAndBestUseType],
              valuationTypes[comparableSale.saleHighestAndBestUseType]
            )
          : true,
        sale,
      }
    }

    const subjectBestUses =
      subjectPropertySummary?.highestAndBestUseSummary?.map(
        (bestUseSummary) => bestUseSummary.highestAndBestUseType
      ) ?? []

    for (const saleFeature of loadedSales?.results?.features || []) {
      for (const bestUseType of saleFeature.properties.bestUses ?? [
        saleFeature.properties.highestAndBestUseType ?? -1,
      ]) {
        const saleCacheId = saleFeature?.id?.toString()
          ? getSaleCacheId(saleFeature?.id, bestUseType)
          : null
        const compatibleSubjectBestUses =
          valuationTypes && bestUseType
            ? compatibleBestUses(valuationTypes, subjectBestUses, bestUseType)
            : []
        if (saleCacheId) {
          _saleEditState[saleCacheId] = {
            ..._saleEditState[saleCacheId],
            state: _saleEditState[saleCacheId]?.state || 'NEW',
            isCompatibleWithSubject: compatibleSubjectBestUses.length > 0,
            saleHighestAndBestUseType: bestUseType,
            sale: {
              ...saleFeature,
              id: Number(saleFeature.id ?? '-1'), // hack
            },
          }
        }
      }
    }

    if (!isEqual(saleEditState, _saleEditState)) {
      dispatch(uiActions.setSaleEditState({ saleEdit: _saleEditState }))
    }
  }, [
    dispatch,
    saleEditState,
    savedSales,
    loadedSales,
    subjectPropertySummary?.highestAndBestUseSummary,
    valuationTypes,
  ])

  const modified = useMemo(() => {
    if (saleEditState) {
      return (
        Object.values(saleEditState).filter(
          (x) => x.state === 'ADD' || x.state === 'REMOVE'
        ).length > 0
      )
    }
  }, [saleEditState])

  const handleClick = useCallback(async () => {
    if (isValuer) {
      const sales = Object.values(saleEditState)
        .filter((x) => x.state === 'EXISTING' || x.state === 'ADD')
        .map((x) => ({
          saleId: x.sale.id,
          lsdbId: x.lsdbId,
          saleHighestAndBestUseType: x.saleHighestAndBestUseType,
          highestAndBestUseType: x.highestAndBestUseType,
        }))

      const result = await commitComparableSales({ valuationId, sales })
      if ('error' in result) {
        const error = result.error
        void message.error(
          `Failed to save comparable sales: ${JSON.stringify(error)}`
        )
      } else {
        const _saleEditState: SaleEditState = { ...saleEditState }
        for (const [saleId, saleEdit] of Object.entries(_saleEditState)) {
          if (saleEdit.state === 'EXISTING' || saleEdit.state === 'ADD') {
            _saleEditState[saleId] = {
              ...saleEdit,
              state: 'EXISTING',
            }
          } else {
            delete _saleEditState[saleId]
          }
        }
        dispatch(uiActions.setSaleEditState({ saleEdit: _saleEditState }))
      }
    } else {
      return
    }
  }, [isValuer, saleEditState, commitComparableSales, valuationId, dispatch])

  return (
    <>
      <Widget>
        <SalesSearchFilters
          disableFilterSave={false}
          pageName="comparableSales"
        />
      </Widget>
      {modified ? (
        <Alert
          className="agrigis-alert"
          message="You have unsaved changes"
          icon={<AlertOutlined />}
          showIcon={true}
          description={
            subjectValuation?.valuation?.completedDate ? null : (
              <ButtonWidget>
                <Space direction="vertical">
                  <span>
                    Navigating away from this page will result in a loss of
                    these changes.
                  </span>
                  <Button icon={<SaveOutlined />} onClick={handleClick}>
                    Save
                  </Button>
                </Space>
              </ButtonWidget>
            )
          }
          type="warning"
        />
      ) : (
        <></>
      )}
      <Widget>
        <ComparableSaleSearchTable
          isValuer={isValuer}
          saleEditState={saleEditState}
          salesCount={salesCount}
          salesCurrentPage={salesCurrentPage || 1}
          subjectValuation={subjectValuation}
          subjectSummary={subjectPropertySummary}
          pageName={pageName}
        />
      </Widget>
    </>
  )
}
