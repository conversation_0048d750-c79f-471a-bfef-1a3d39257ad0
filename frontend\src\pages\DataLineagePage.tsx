import { FlowAnalysisGraph } from '@ant-design/charts'
import {
  type FlowAnalysisGraphConfig,
  NodeCfg,
} from '@ant-design/charts/es/graphs'
import { useGetDataLineageQuery } from '@store/services/sdk'

const DataLineagePage = () => {
  const { data } = useGetDataLineageQuery()

  if (!data) return null

  return (
    <div style={{ height: '100vh', width: '100vw' }}>
      <FlowAnalysisGraph
        data={data as unknown as FlowAnalysisGraphConfig['data']}
        style={{ backgroundColor: '#eee' }}
        autoFit={true}
        markerCfg={{
          show: false,
          collapsed: false,
          position: 'bottom',
        }}
        edgeCfg={{
          style: () => {
            return {
              stroke: '#5ae859',
              strokeOpacity: 0.5,
            }
          },
          edgeStateStyles: {
            hover: {
              strokeOpacity: 1,
              lineWidth: 2,
            },
          },
        }}
        nodeCfg={{
          size: [200, 25],
          style: {
            stroke: 'transparent',
          },
          nodeStateStyles: {
            hover: {
              stroke: '#1890ff',
              lineWidth: 4,
            },
          },
          title: {
            autoEllipsis: false,
            style: {
              fontSize: 10,
              fontWeight: 'bold',
            },
            containerStyle: (cfg: { value: { type: string } }) => {
              switch (cfg.value.type) {
                case 'nzdw':
                  return { fill: '#9c5500' }
                case 'nzdw-db':
                  return { fill: '#df7a00' }
                case 'nzdw-v':
                  return { fill: '#fdc82f' }
                case 'esgis':
                  return { fill: '#004165' }
                case 'esgis-db':
                  return { fill: '#007dba' }
                case 'esgis-t':
                  return { fill: '#00c6d7' }
                case 'esgis-v':
                  return { fill: '#7a99ac' }
                default:
                  return {}
              }
            },
          },
        }}
        behaviors={['drag-canvas', 'zoom-canvas', 'drag-node']}
      />
    </div>
  )
}

export default DataLineagePage
