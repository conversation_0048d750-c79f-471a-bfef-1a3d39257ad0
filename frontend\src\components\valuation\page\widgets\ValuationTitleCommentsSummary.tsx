import React from 'react'
import { Title } from '@components/typography'
import type { ValuationsSavedTitle } from '@store/services/sdk'

type Props = {
  titles?: ValuationsSavedTitle[]
}

const ValuationTitleCommentsSummary = ({ titles }: Props) => {
  if (!titles?.some((title) => title.review?.comments)) return null
  return (
    <div>
      <Title>Title Comments:</Title>
      <dl>
        {titles.map((title) => {
          const review = title.review
          if (!review?.comments) return null
          return (
            <div key={title.id}>
              <dt>{title.titleNo}</dt>
              <dd>
                <div>{review.comments}</div>
                {!!review.memorials?.length && (
                  <>
                    Concerning memorials:{' '}
                    {review.memorials
                      .map((memorial) => memorial.instrumentNumber)
                      .join(', ')}
                  </>
                )}
              </dd>
            </div>
          )
        })}
      </dl>
    </div>
  )
}

export default ValuationTitleCommentsSummary
