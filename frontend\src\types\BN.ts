import BigNumber from 'bignumber.js'

export const BN = BigNumber.clone({ DECIMAL_PLACES: 3 })
export const AreaBN = BN

export const ZERO = new BN(0)
export const NEG_1 = new BN(-1)

export function toBigNumber<T extends Record<string, string>>(object: T) {
  return Object.entries(object).reduce(
    (accum, [key, value]: [keyof T, string]) => {
      accum[key] = new BN(value)
      return accum
    },
    {} as Record<keyof T, BigNumber>
  )
}

export function fromBigNumber<T extends Record<string, BigNumber>>(object: T) {
  return Object.entries(object).reduce(
    (accum, [key, value]: [keyof T, BigNumber]) => {
      accum[key] = value.toString()
      return accum
    },
    {} as Record<keyof T, string>
  )
}
