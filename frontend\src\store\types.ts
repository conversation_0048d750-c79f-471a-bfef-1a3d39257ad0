import type { Middleware } from '@reduxjs/toolkit'
import type { ThunkAction, ThunkDispatch } from 'redux-thunk'
import type { rootReducer } from './rootReducer'

export interface Action {
  type: string
}

export type AppThunk<A extends Action = Action, E = null> = ThunkAction<
  void,
  RootState,
  E,
  A
>

export type RootState = ReturnType<typeof rootReducer>

/**
 * Types useDispatch correctly, useful when expecting a promise
 * @example useDispatch<ThunkAppDispatch>()
 */
export type ThunkAppDispatch = ThunkDispatch<RootState, void, Action>

// biome-ignore lint: Empty object recommended in Redux documentation
export type AppMiddleware = Middleware<{}, RootState, ThunkAppDispatch>
