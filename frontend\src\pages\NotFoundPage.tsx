import { Divider, Typography } from 'antd'
import { Helmet } from 'react-helmet'
import styles from './ErrorPage.module.scss'

export const NotFoundPage = () => {
  return (
    <div className={styles.container} data-testid="404-not-found">
      <Helmet>
        <title>Page Not Found</title>
      </Helmet>
      <div className={styles.error}>
        <Typography className={styles.errorCode}>404</Typography>
        <Typography className={styles.errorType}>Page Not Found</Typography>
        <Divider />
        <Typography className={styles.errorDescription}>
          <p>
            Please check you've typed the URL correctly. If you believe this is
            a mistake, please contact{' '}
            <a href="mailto:<EMAIL>"><EMAIL></a>
          </p>
        </Typography>
      </div>
    </div>
  )
}
