import { EditOutlined, FileTextOutlined } from '@ant-design/icons'
import { skipToken } from '@reduxjs/toolkit/dist/query'
import { Alert, Tabs } from 'antd'
import { useForm } from 'antd/lib/form/Form'
import React from 'react'
import { ButtonWidget } from '@components/generic'
import { HoverButton } from '@components/generic/HoverButton'
import type { Sale } from '@store/services/sdk'
import {
  useGetSaleDescriptionQuery,
  useGetSaleFilesQuery,
  useGetSaleQuery,
  useGetSaleTitlesQuery,
} from '../../../../../store/services/sale'
import { integerToAlpha } from '../../../../../util/intgerToAlpha'
import { LandBestUseSummary } from '../../../../assets/summary/LandBestUseSummary'
import { Widget } from '../../../../generic/Widget'
import { PhysicalPropertiesGraphPane } from '../../../../physicalProperties/PhysicalPropertiesGraphPane'
import { SelectedSaleDetails } from '../../../../sales/dashboard/SelectedSaleDetails'
import { ServiceCentreWidget } from '../property/ServiceCentreWidget'

export interface SelectedSalePaneProps {
  sale: Sale
  saleRowIndex: number | undefined
}

export const SelectedSalePane = (props: SelectedSalePaneProps) => {
  const { sale: selectedSale, saleRowIndex } = props

  const [assetForm] = useForm()

  const { data: saleTitles } = useGetSaleTitlesQuery(
    selectedSale ? selectedSale.id.toString() : skipToken
  )
  const { data: saleFiles } = useGetSaleFilesQuery(
    selectedSale ? selectedSale.id.toString() : skipToken
  )
  const { data: salePropertyDescription } = useGetSaleDescriptionQuery(
    selectedSale ? { saleId: selectedSale.id.toString() } : skipToken
  )

  return (
    <>
      <Widget
        title="Sale Information"
        secondaryTitle={`${
          saleRowIndex !== null && saleRowIndex !== undefined
            ? integerToAlpha(saleRowIndex)
            : ''
        } ${selectedSale?.properties?.fullAddress ?? ''}`}
        extra={
          <ButtonWidget>
            <HoverButton
              icon={<EditOutlined />}
              href={`/editSale/${selectedSale?.id}/`}
            >
              Edit Sale
            </HoverButton>
          </ButtonWidget>
        }
      >
        <Tabs defaultActiveKey={'SALES'}>
          <Tabs.TabPane tab="Sale Information" key="SALE">
            <SelectedSaleDetails
              sale={selectedSale}
              saleTitles={saleTitles}
              saleFiles={saleFiles ?? []}
              salePropertyDescription={salePropertyDescription}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="Asset Breakdown" key="ASSET_BREAKDOWN">
            <LandBestUseSummary
              valuationId={selectedSale.properties.valuation}
              targetSaleId={selectedSale.id.toString()}
              assetForm={assetForm}
              editEnabled={false}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="Distances" key="DISTANCES">
            {selectedSale?.properties?.address ? (
              <ServiceCentreWidget
                addressId={selectedSale.properties.address.toString()}
                fullAddress={selectedSale.properties.fullAddress ?? ''}
              />
            ) : (
              <Alert
                type="warning"
                message="This sale does not have an associated address."
              />
            )}
          </Tabs.TabPane>
          <Tabs.TabPane tab="Physical Properties" key="PHYSICAL_PROPERTIES">
            <PhysicalPropertiesGraphPane
              valuationId={selectedSale.properties.valuation.toString()}
            />
          </Tabs.TabPane>
        </Tabs>
      </Widget>
    </>
  )
}
