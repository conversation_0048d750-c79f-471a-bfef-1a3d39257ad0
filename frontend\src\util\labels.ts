import { startCase, truncate } from 'lodash'
import { formatDollarValue } from '@util'
import { LOCALE } from './const'
import { squareMetresToHectares } from './helpers'
import { percentage, toNumber } from './number'
import type { MaybeNumber } from './types'

export function charFromIndex(index: number) {
  return String.fromCharCode(index + 97).toUpperCase()
}

type FormatAddressOptions = {
  startCase?: true
  length?: number
}

export function formatAddress(
  address: string | null | undefined,
  options: FormatAddressOptions = {}
) {
  const formatOptions = {
    startCase: true,
    length: Number.POSITIVE_INFINITY,
    ...options,
  }

  let formattedAddress = address || ''

  if (formatOptions.startCase) {
    formattedAddress = startCase(formattedAddress.toLowerCase())
  }

  return truncate(formattedAddress, { length: formatOptions.length })
}

export const formatDollarsAsMillions = (value: MaybeNumber) =>
  `${formatDollarValue(toNumber(value))}M`

// Newer TS target has this fixed
export type NumberFormatterOptions = Intl.NumberFormatOptions & {
  trailingZeroDisplay?: 'stripIfInteger' | 'auto'
}

/**
 * Convenience function for Intl.NumberFormat
 * @param value Number representation or undefined, with 0 as default value
 * @param options Intl.NumberFormatOptions
 * @example
 * const options = { maximumFractionDigits: 2, minimumFractionDigits: 2, trailingZeroDisplay: 'stripIfInteger' }
 *
 * formatNumber(95, options)
 * "95"
 *
 * formatNumber(95.5, options)
 * "95.50"
 */
export function formatNumber(
  value: MaybeNumber = 0,
  options?: NumberFormatterOptions
): string {
  return Number(value).toLocaleString(LOCALE, options)
}

/**
 * Convenience function for Intl.NumberFormat using percent style
 * @param value Decimal number representation or undefined, with 0 as default value
 * @param options Intl.NumberFormatOptions with style omitted
 * @example
 * const options = { maximumFractionDigits: 2, minimumFractionDigits: 2, trailingZeroDisplay: 'stripIfInteger' }
 *
 * formatPercent(0.95, options)
 * "95%"
 *
 * formatPercent(0.955, options)
 * "95.50%"
 */
export const formatPercent = (
  value: MaybeNumber,
  options: Omit<NumberFormatterOptions, 'style'> = {}
) => formatNumber(value, { ...options, style: 'percent' })

// TODO: Replace with above
export const formatPercentage = (
  value: MaybeNumber,
  options = { fractionDigits: 2 }
) => `${toNumber(value).toFixed(options.fractionDigits)}%`

export function formatWholeNumber(
  ...args: Parameters<typeof formatNumber>
): string {
  return formatNumber(args[0], { maximumFractionDigits: 0, ...args[1] })
}

export function formatSquareMetresAsHectares(value: number) {
  const ha = squareMetresToHectares(value).toFixed(2)
  return `${ha} ha`
}

export const percentageString = (a: MaybeNumber, b: MaybeNumber) =>
  formatPercentage(percentage(a, b))
