import type { Feature, FeatureCollection, Point } from 'geojson'

export interface ResourceConsentProperties {
  cnt: number
  category: string
  irisId: string
  consentId: string
  subtype: string
  location: string
  status: string
  expiryDate: string
  commencementDate: string
  holder: string
  description: string
  council: string
  link?: string
}

export type ResourceConsent = Feature<Point, ResourceConsentProperties>

export type ResourceConsentArray = ResourceConsent[]

export interface ResourceConsentFeatureCollection extends FeatureCollection {
  features: Array<ResourceConsent>
}
