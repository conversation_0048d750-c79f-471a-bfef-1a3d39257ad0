import { message } from 'antd'
import { useCallback, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useCloneValuationMutation } from '@store/services/valuations'
import type { Valuation } from '@types'
import { getQueryErrorMessage } from '@util/error'

export interface ValuationCloneInput {
  name?: string
  highestAndBestUseTypeId?: string
}

interface Props {
  addressId?: string
  valuationId?: string
}

// Should maybe make 'redirect' an option and then always return a single function
function useCloneValuation({ addressId, valuationId }: Props) {
  const [cloneValuationMutation] = useCloneValuationMutation()
  const [cloning, setCloning] = useState(false)

  const navigate = useNavigate()

  const clone = useCallback(
    async (body?: ValuationCloneInput): Promise<Valuation | null> => {
      setCloning(true)
      let clone: Valuation | null = null
      try {
        if (!(addressId && valuationId))
          throw new Error('Valuation and address required')
        const result = await cloneValuationMutation({
          addressId,
          valuationId,
          body,
        }).unwrap()
        void message.success('Cloned successfully.')
        clone = result
      } catch (e) {
        const errorMessage = getQueryErrorMessage(e)
        void message.error(`Failed to clone mutation: ${errorMessage}`)
      }
      setCloning(false)
      return clone
    },
    [addressId, valuationId, cloneValuationMutation]
  )

  const cloneAndRedirect = useCallback(
    async (body?: ValuationCloneInput) => {
      const result = await clone(body)
      if (!result) return
      navigate(`/valuations/${result.id}/`)
      return cloning
    },
    [clone, cloning, navigate]
  )

  return { clone, cloneAndRedirect, cloning }
}

export default useCloneValuation
