import { Helmet } from 'react-helmet'
import ProtectedRoute from '@components/ProtectedRoute'
import AddressEditView from '@components/addresses/AddressEditView'

const AddressEditPage = () => (
  <>
    <Helmet>
      <title>Edit Address</title>
    </Helmet>
    <ProtectedRoute requiredEntitlements={['client:propertyflow:*']}>
      <AddressEditView />
    </ProtectedRoute>
  </>
)

export default AddressEditPage
