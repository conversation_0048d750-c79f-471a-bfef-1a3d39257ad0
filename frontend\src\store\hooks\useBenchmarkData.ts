import { useMemo } from 'react'
import type {
  Benchmark,
  CustomerKpiBenchmarkListApiResponse,
  KpiType,
  ValidKpiMeasuresListApiResponse,
} from '@store/services/sdk'

const useBenchmarkData = (
  data: CustomerKpiBenchmarkListApiResponse | undefined,
  options: ValidKpiMeasuresListApiResponse | undefined,
  metric: KpiType['value'] | undefined
) => {
  const { description, label } = useMemo(() => {
    const opt = (options ?? []).find((option) => option.value === metric)
    return { ...opt }
  }, [options, metric])

  const benchmarkData = useMemo(() => {
    const labels: Record<
      keyof Pick<Benchmark, 'lq' | 'median' | 'uq'>,
      string
    > = {
      lq: 'Lower Quartile',
      median: 'Median',
      uq: 'Upper Quartile',
    }

    const labelKeys = Object.keys(labels) as (keyof typeof labels)[]

    return (data || []).flatMap((benchmark) =>
      labelKeys.map((key) => ({
        ...benchmark,
        year: benchmark.year,
        category: labels[key],
        value: Number(benchmark[key]) !== 0 ? Number(benchmark[key]) : null,
      }))
    )
  }, [data])

  const customerData = useMemo(() => {
    return (data ?? []).map(({ year, target }) => ({
      year,
      customer: Number(target) !== 0 ? Number(target) : null,
    }))
  }, [data])

  return { description, benchmarkData, customerData, label }
}

export default useBenchmarkData
