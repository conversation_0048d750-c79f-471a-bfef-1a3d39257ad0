@media (min-width: $smBp) {

  html,
  body {
    height: 100%;
    width: 100%;
    margin: 0;
    overflow-y: hidden;
  }

  .application-container {
    @include flex;
    margin-left: 3em;
    justify-content: center;
  }


  .parent-container {
    @include flex;
    flex-direction: row;
    min-width: 100%;
    min-height: 0;
  }

  %base-content-container {
    overflow-y: scroll;
    height: 94.5vh;
    /* padding: 5px; */
  }

  .content-container {
    @extend %base-content-container;
    height: 100vh;
    min-width: 500px;
    max-width: 500px;
  }

  .map-container {
    @extend %base-content-container;
    width: 100%;
    overflow-y: hidden;
  }

  .header-container {
    @include flex;
    align-items: center;
    justify-content: space-between;
  }
}

@media (min-width: $mdBp) {
  .content-container {
    min-width: 600px;
    max-width: 600px;
  }
}

@media (min-width: $lgBp) {
  .content-container {
    min-width: 700px;
    max-width: 700px;
  }
}

.divider {
  cursor: col-resize;
  width: 5px;
  z-index: 100000;
}

.resizable-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.map-sider {
  width: 650px;
  overflow-y: scroll;
  padding-right: $half;
  padding-left: $half;
  display: flex;
  flex-direction: column;
  flex-basis: 0;
  flex-grow: 1;
}

.droppable {
  display: flex;
  flex-direction: column;
}

.droppable>div {
  flex-grow: 1;
}

.scrollable-container {
  overflow-y: scroll;
}

.tab-container {
  margin: 10px 20px 30px 20px;
}

.nav-link {
  font-size: 12px;
}

.overlay-drawer-container {
  background-color: #fff;
  display: none;
  height: 100%;
  width: 100%;
  z-index: 400;
  overflow-y: scroll;
}

.ant-select-dropdown,
.ant-cascader-menus {
  z-index: 11000;
}

.overlay-drawer {
  margin: 15px;
  padding-right: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.overlay-drawer-header {
  align-self: flex-end;
  position: relative;
  right: -15px;
  top: -10px;
  height: 15px;
}

.resizable-handle {
  z-index: 1000;
}