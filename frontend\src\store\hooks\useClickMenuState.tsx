import { useCallback, useState } from 'react'
import type { ReactNode } from 'react'

export interface ClickMenuState {
  posX: number
  posY: number
  active: boolean
  title?: ReactNode | ReactNode[]
  content?: ReactNode | ReactNode[]
  noOffset?: boolean
}

const initialState = {
  posX: 0,
  posY: 0,
  active: false,
}

export const useClickMenuState = (): [
  ClickMenuState,
  (
    posX: number,
    posY: number,
    title?: ReactNode | ReactNode[],
    content?: ReactNode | ReactNode[],
    active?: boolean,
    noOffset?: boolean
  ) => void,
  () => void,
  () => void,
  (posX: number, posY: number) => void,
  (title?: ReactNode | ReactNode[]) => void,
  (content?: ReactNode | ReactNode[]) => void,
] => {
  const [state, setState] = useState<ClickMenuState>(initialState)

  const updateState = useCallback(
    (
      posX: number,
      posY: number,
      title?: ReactNode | ReactNode[],
      content?: ReactNode | ReactNode[],
      active?: boolean,
      noOffset?: boolean
    ) => {
      setState((prev) => {
        const newState = {
          ...prev,
          posX,
          posY,
          title,
          content,
          active,
          noOffset,
        } as ClickMenuState
        return newState
      })
    },
    []
  )

  const updatePos = useCallback((posX: number, posY: number) => {
    setState((prev) => {
      const newState = {
        ...prev,
        posX,
        posY,
      } as ClickMenuState
      return newState
    })
  }, [])

  const updateTitle = useCallback((title?: ReactNode | ReactNode[]) => {
    setState((prev) => {
      const newState = {
        ...prev,
        title,
      } as ClickMenuState
      return newState
    })
  }, [])

  const updateContent = useCallback((content?: ReactNode | ReactNode[]) => {
    setState((prev) => {
      const newState = {
        ...prev,
        content,
      } as ClickMenuState
      return newState
    })
  }, [])

  const deactivateMenu = useCallback(() => {
    setState((prev) => {
      const newState = {
        ...prev,
        active: false,
      } as ClickMenuState
      return newState
    })
  }, [])

  const activateMenu = useCallback(() => {
    setState((prev) => {
      const newState = {
        ...prev,
        active: false,
      } as ClickMenuState
      return newState
    })
  }, [])

  return [
    state,
    updateState,
    deactivateMenu,
    activateMenu,
    updatePos,
    updateTitle,
    updateContent,
  ]
}
