import { Helmet } from 'react-helmet'
import { Link } from 'react-router-dom'
import EsstLayout from '@components/esst/EsstLayout'
import EsstListView from '@components/esst/EsstListView'
import {
  useCustomerRetrieveQuery,
  useCustomerGroupRetrieveQuery,
} from '@store/services/sdk'
import { skipArgObject } from '@util/helpers'
import { useCustomerMaybe } from '@components/customer/context'
import util from '@styles/new/util.module.css'

export default function EsstListPage() {
  const customerPk = useCustomerMaybe()

  const { data: customer, isLoading: customerLoading } =
    useCustomerRetrieveQuery(skipArgObject({ pk: customerPk?.pk }))

  const { data: customerGroup, isLoading: customerGroupLoading } =
    useCustomerGroupRetrieveQuery(skipArgObject({ pk: customerPk?.pk }), {
      skip: !customer || !!customer.tradingGroup,
    })

  const isLoading = customerLoading || customerGroupLoading

  const groupInfo = customerPk
    ? {
        customerGroupId: customerGroup?.id,
        tradingGroupId: customer?.tradingGroup?.tradingGroupId,
      }
    : undefined

  return (
    <>
      <Helmet>
        <title>Environmental & Social Screening Tool</title>
      </Helmet>
      <EsstLayout
        actions={
          <Link to="new" className={util.button}>
            Create New Report
          </Link>
        }
      >
        {isLoading ? (
          <div>Loading...</div>
        ) : (
          <EsstListView groupInfo={groupInfo} />
        )}
      </EsstLayout>
    </>
  )
}
