import type { Point } from 'geojson'
import type { Sale } from '@store/services/sdk'
import { fromBigNumber, toBigNumber } from '@types'
import {
  deserializeValuationSummary,
  serializeValuationSummary,
} from './Assets'
import {
  type SerializableComparableSaleAdjustment,
  type SerializableComparableSaleAssetMetricAdjustment,
  deserializeComparableSaleAdjustment,
  deserializeComparableSaleAssetMetricAdjustment,
  serializeComparableSaleAdjustment,
  serializeComparableSaleAssetMetricAdjustment,
} from './ComparableSaleAdjustment'
import type { SerializableValuationSummary } from './ValuationTypes'

export interface SerializableComparableSale {
  id: number
  valuation?: number
  adjustments: SerializableComparableSaleAdjustment[]
  assetMetricAdjustments: SerializableComparableSaleAssetMetricAdjustment[]
  summaryComments: string

  isBenchmark: boolean

  linkedSale: Sale['properties'] & {
    valuationSummary?: SerializableValuationSummary
  }

  landClassAdjustmentSummary?: string

  highestAndBestUseType: number

  saleHighestAndBestUseType: number

  totalAdjustmentPercent: string
  lwbAdjustmentPercent: string

  overallComparability: string
  lwbComparability: string

  valuationSummary?: SerializableValuationSummary

  // if the linked sale has a valuation summary
  hasValuationSummary: boolean

  // if we should use the AgriGIS breakdown or the sale input values
  useValuationSummary: boolean

  geometry: Point
  fullAddress: string
}

export function deserializeComparableSale({
  lwbAdjustmentPercent,
  totalAdjustmentPercent,
  adjustments,
  assetMetricAdjustments,
  valuationSummary,
  linkedSale,
  ...rest
}: SerializableComparableSale) {
  return {
    ...rest,
    adjustments: adjustments.map(deserializeComparableSaleAdjustment),
    assetMetricAdjustments: assetMetricAdjustments.map(
      deserializeComparableSaleAssetMetricAdjustment
    ),
    valuationSummary: valuationSummary
      ? deserializeValuationSummary(valuationSummary)
      : undefined,
    linkedSale: {
      ...linkedSale,
      valuationSummary: linkedSale.valuationSummary
        ? deserializeValuationSummary(linkedSale.valuationSummary)
        : undefined,
    },
    ...toBigNumber({
      lwbAdjustmentPercent,
      totalAdjustmentPercent,
    }),
  }
}

export type ComparableSale = ReturnType<typeof deserializeComparableSale>

export function serializeComparableSale({
  lwbAdjustmentPercent,
  totalAdjustmentPercent,
  adjustments,
  assetMetricAdjustments,
  valuationSummary,
  linkedSale,
  ...rest
}: ComparableSale): SerializableComparableSale {
  return {
    ...rest,
    adjustments: adjustments.map(serializeComparableSaleAdjustment),
    assetMetricAdjustments: assetMetricAdjustments.map(
      serializeComparableSaleAssetMetricAdjustment
    ),
    valuationSummary: valuationSummary
      ? serializeValuationSummary(valuationSummary)
      : undefined,
    linkedSale: {
      ...linkedSale,
      valuationSummary: linkedSale.valuationSummary
        ? serializeValuationSummary(linkedSale.valuationSummary)
        : undefined,
    },
    ...fromBigNumber({
      lwbAdjustmentPercent,
      totalAdjustmentPercent,
    }),
  }
}
