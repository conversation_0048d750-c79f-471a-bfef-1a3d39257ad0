@import 'func/stroke';
@import 'typography';

@import 'old/abstract/_functions.scss';
@import 'old/abstract/_mixins.scss';
@import 'old/abstract/_variables.scss';

@import 'old/base/_body.scss';

@import 'old/layout/_container.scss';

@import 'old/components/_action_text.scss';
@import 'old/components/_card.scss';
@import 'old/components/_chart.scss';
@import 'old/components/_checkbox.scss';
@import 'old/components/_colored_rect.scss';
@import 'old/components/_description.scss';
@import 'old/components/_draggable.scss';
@import 'old/components/_fixed_bar.scss';
@import 'old/components/_form.scss';
@import 'old/components/_highlight.scss';
@import 'old/components/_input.scss';
@import 'old/components/_leaflet.scss';
@import 'old/components/_list.scss';
@import 'old/components/_loading.scss';
@import 'old/components/_modal.scss';
@import 'old/components/_numberinput.scss';
@import 'old/components/_radio.scss';
@import 'old/components/_react-datepicker.scss';
@import 'old/components/_tab.scss';
@import 'old/components/_table.scss';
@import 'old/components/_toggleswitch.scss';

@import 'old/modules/_dvr_search.scss';
@import 'old/modules/_map.scss';
@import 'old/modules/_recent_search.scss';
@import 'old/modules/_titles.scss';
@import 'old/modules/_home.scss';

@import 'old/vendors/_bootstrap.scss';

@import 'base/_page.scss';
@import 'base/_filters.scss';
@import 'base/_map.scss';
@import 'base/_pagination.scss';
@import 'base/_pane.scss';
@import 'base/_table.scss';
@import 'base/_widget.scss';
@import 'base/_alert.scss';
@import 'base/_form.scss';
@import 'base/_chart.scss';
@import 'base/_overlay.scss';
@import 'base/_input.scss';
@import 'components/_asset_sale_progress.scss';
@import 'base/_button.scss';

@import 'components/_records_table.scss';
@import 'components/_login.scss';

@import 'components/_map_layer_control.scss';
@import 'components/_carbon_summary_card.scss';
@import 'components/_radius_control.scss';
@import 'components/_asset_form.scss';
@import 'components/_land_asset_table.scss';
@import 'components/_irrigation_layer_popup.scss';
@import 'components/_sale_comparison_widget.scss';
@import 'components/_asset_comparison.scss';
@import 'components/_user_task_drawer.scss';
@import 'components/_project.scss';

@import 'pages/valuation/_page.scss';

@import 'pages/home/<USER>';
@import 'pages/home/<USER>';
@import 'pages/home/<USER>';

@import 'pages/admin/_page.scss';
@import 'pages/admin/_pane.scss';
@import 'pages/admin/_widget.scss';

@import 'pages/loan/_page.scss';
@import 'pages/loan/_loan_detail.scss';

@import 'pages/tcfdPortfolio/_page.scss';

@import 'defaults.scss';
@import 'typography.scss';
@import 'utility.scss';

.Layout h2 {
  @include heading-section;

  margin-bottom: 12px;
}

.Layout:not(.MapFocusPage) {
  .ant-tabs>.ant-tabs-nav {
    margin-bottom: 32px;
  }

  .ant-tabs-tab {
    @include body;
  }
}

// fixes black element styling when using the browser fullscreen API
::backdrop {
  background-color: transparent;
}