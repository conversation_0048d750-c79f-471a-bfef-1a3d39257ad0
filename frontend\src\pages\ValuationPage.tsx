import React from 'react'
import { Helmet } from 'react-helmet'
import { useParams } from 'react-router-dom'
import ProtectedRoute from '@components/ProtectedRoute'
import { Layout } from '@components/generic'
import ValuationDetailView from '@components/valuation/ValuationDetailView'
import { UnlinkedValuationOverlay } from '@components/valuation/page/generic/UnlinkedValuationOverlay'

export interface ValuationPageRouteParams {
  valuationId: string
}

const ValuationPage = () => {
  const { valuationId } = useParams()

  return (
    <>
      <Helmet>
        <title>Valuation</title>
      </Helmet>
      <ProtectedRoute requiredEntitlements={['client:propertyflow:*']}>
        <Layout id="valuation" data-testid="valuation-page">
          {valuationId && (
            <>
              <UnlinkedValuationOverlay valuationId={valuationId} />
              <ValuationDetailView valuationId={valuationId} />
            </>
          )}
        </Layout>
      </ProtectedRoute>
    </>
  )
}

export default ValuationPage
