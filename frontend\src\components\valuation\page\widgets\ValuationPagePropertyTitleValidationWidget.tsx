import { Button, Form, Input, Select, Space, message } from 'antd'
import { useForm } from 'antd/lib/form/Form'
import React, { useCallback, useState } from 'react'
import { useEffect } from 'react'
import {
  useGetValuationSummaryQuery,
  useUpdateValuationTitleReviewMutation,
} from '../../../../store/services/valuations'
import { Widget } from '../../../generic/Widget'

const VALIDATION_OPTIONS = [
  { value: 0, label: 'Not Reviewed' },
  { value: 1, label: 'Reviewed - No Concerns' },
  { value: 2, label: 'Reviewed - With Concerns' },
]

export interface ValuationPagePropertyTitleValidationWidgetProps {
  valuationId: string
  addressId: string
  disabled?: boolean
}

export const ValuationPagePropertyTitleValidationWidget = ({
  valuationId,
  disabled,
}: ValuationPagePropertyTitleValidationWidgetProps) => {
  const [form] = useForm<{
    titleReviewStatus: number
    titleReviewComments: string
  }>()

  const reviewStatusValue = Form.useWatch('titleReviewStatus', form)
  const [dirty, setDirty] = useState(false)

  const [updateValuationTitleReview] = useUpdateValuationTitleReviewMutation()

  const { data: valuationSummary } = useGetValuationSummaryQuery(valuationId)

  // biome-ignore lint/correctness/useExhaustiveDependencies:
  useEffect(() => {
    form.resetFields()
  }, [form, valuationSummary])

  const saveChanges = useCallback(async () => {
    const formValues = form.getFieldsValue()
    const result = await updateValuationTitleReview({
      valuationId,
      ...formValues,
    })
    if ('error' in result) {
      void message.error(result.error.toString())
    } else {
      setDirty(false)
    }
  }, [form, updateValuationTitleReview, valuationId])

  const clearChanges = useCallback(() => {
    form.resetFields()
    setDirty(false)
  }, [form])

  return (
    <Widget
      title="Property Title Review"
      extra={
        <Space style={{ marginRight: '10px' }} direction="horizontal">
          {dirty ? (
            <>
              <Button onClick={saveChanges} disabled={disabled}>
                Save
              </Button>
              <Button onClick={clearChanges} disabled={disabled}>
                Cancel
              </Button>
            </>
          ) : null}
        </Space>
      }
    >
      <Form
        form={form}
        onValuesChange={(changedValues) => {
          setDirty(true)
          if (
            changedValues.titleReviewStatus !== undefined &&
            changedValues.titleReviewStatus !== 2
          ) {
            form.setFieldsValue({ titleReviewComments: '' })
          }
        }}
        initialValues={{
          titleReviewStatus:
            valuationSummary?.valuation?.titleReviewStatus ?? 0,
          titleReviewComments: valuationSummary?.valuation?.titleReviewComments,
        }}
      >
        <Form.Item label="Review Status" name="titleReviewStatus">
          <Select
            options={VALIDATION_OPTIONS}
            disabled={!!valuationSummary?.valuation?.completedDate || disabled}
          />
        </Form.Item>
        {reviewStatusValue === 2 && (
          <div
            style={{
              background: 'whitesmoke',
              minHeight: '114px',
              padding: '8px',
              borderRadius: '3px',
            }}
          >
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, curValues) =>
                prevValues.titleReviewStatus !== curValues.titleReviewStatus
              }
            >
              <Form.Item
                name="titleReviewComments"
                label="Comments"
                style={{ margin: 0 }}
              >
                <Input.TextArea
                  rows={4}
                  disabled={!!valuationSummary?.valuation?.completedDate}
                />
              </Form.Item>
            </Form.Item>
          </div>
        )}
      </Form>
    </Widget>
  )
}
