.map-container-click-menu {
  min-width: 150px;
  background-color: white;
  z-index: 10008;
  position: absolute;
  padding: 1.5em 2em;
  font-size: 13px;

  ul {
    padding-bottom: 0;
    margin-bottom: 0;
  }
}

.map-container-click-menu-list {
  li {
    transition: 0.25s background-color;
    padding: 0.5em 1em;

    &:before {
      content: attr(data-icon);
      margin-right: 8px;
    }

    &:hover {
      background-color: #eee;
      outline: 1px solid #ccc;
      cursor: pointer;
    }
  }
}

.ant-picker-dropdown {
  z-index: 10201;
}

.map-container-aggregated-point {
  border-radius: 256px;
  padding: 32px;
  background-color: rgba(0, 125, 186, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  border: 0px solid #004168;
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.5);

  > div {
    color: black;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 2px;
  }

  &.sale {
    background-color: rgba(255, 0, 0, 0.33);
    border-color: rgba(155, 0, 0, 1);
  }

  &.consent {
    background-color: rgba(155, 155, 155, 0.33);
    border-color: rgba(55, 55, 55, 1);
  }

  &.listing {
    background-color: rgba(16, 201, 218, 0.33);
    border-color: rgba(16, 201, 218, 1);
  }

  &.titles {
    background-color: rgba(0, 0, 255, 0.33);
    border-color: rgba(0, 0, 255, 1);
  }
}

.map-container-click-menu-header {
  display: flex;
  margin-bottom: 8px;
  border-bottom: #ddd solid 1px;
  padding-bottom: 8px;
  justify-content: space-between;
}

.map-container-click-menu-title {
  display: flex;
  justify-content: space-between;

  .map-container-click-menu-icon {
    padding-right: 16px;
  }
}

.agrigis-project-click-menu-close,
.map-container-widget-close,
.map-container-click-menu-close {
  padding: 0;
  margin-left: 16px;
  color: white;
  background-color: rgb(245, 185, 195);
  border: 1px solid rgb(195, 135, 145);
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  font-size: 10px;
  transition: 0.2s background-color;

  &:hover {
    background-color: rgb(255, 212, 223);
    cursor: pointer;
  }
}

.map-container-widget-minimised,
.map-container-click-menu,
.map-container-widget {
  border-radius: 4px;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
  z-index: 10001;
  margin-top: 12px;
}

.map-container-widget {
  padding: 1em;
  font-size: 13px;
  display: block;
  min-width: 300px;
  max-width: 600px;
  max-height: 45vh;

  &.map-search {
    min-width: 400px;
  }

  &.main-controls {
    height: 100%;
    min-height: 45vh;
  }

  &.search {
    width: 400px;
    max-height: 400px;
  }

  .agrigis-widget {
    margin-bottom: 2em;

    h2 {
      font-size: 12px;

      .anticon {
        padding-right: 10px;

        svg {
          width: 100%;
          height: 100%;
        }
      }
    }

    .agrigis-widget-header {
      margin-bottom: 1em;
    }

    .ant-descriptions {
      th,
      td {
        padding: 4px;
      }
    }

    h2 {
      font-size: 13px;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
  }

  .map-container-widget-content {
    overflow-y: scroll;
    max-height: calc(45vh - 6em);
    padding: 0.5em;
  }
}

.map-container-widget,
.map-container-widget-minimised {
  background-color: white;
}

.map-container-widget-minimised {
  height: 44px;
  width: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  transition: 0.25s background-image;

  &:hover {
    background-color: rgb(255, 255, 153);
    cursor: pointer;
  }
}

.map-container-overlay {
  z-index: 10001;
  position: absolute;
  margin: 0 60px;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  flex-direction: column;

  &.right {
    right: 12px;
    align-items: flex-end;
  }
}

.agrigis-modal-title {
  text-transform: uppercase;
  font-weight: bold;
  color: #004168;
  margin-right: 16px;

  &::before {
    content: attr(data-icon);
    font-size: 19px;
    padding: 0;
    margin: 0;
    line-height: 19px;
    margin-right: 16px;
  }
}

.map-container-widget-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  border-bottom: #ddd solid 1px;
  padding-bottom: 8px;
  font-size: 15px;

  > span:first-child {
    text-transform: uppercase;
    font-weight: bold;
    color: #004168;
    margin-right: 16px;

    &::before {
      content: attr(data-icon);
      font-size: 19px;
      padding: 0;
      margin: 0;
      line-height: 19px;
      margin-right: 16px;
    }
  }

  .map-container-widget-icon {
    flex-shrink: 0;
  }
}

.map-container-list {
  display: flex;
  flex-direction: column;

  .show-titles-no-label,
  .show-titles-owners-label,
  .show-titles-areas-label,
  .show-saledate-label,
  .show-vendor-label,
  .show-sale-label,
  .show-purchaser-label,
  .show-permanent-labels,
  .show-addresses-label,
  .show-owners-label,
  .show-areas-label,
  .show-trading-groups-label,
  .show-consent-category-label,
  .show-consent-iris_id-label,
  .show-consent-consent_id-label,
  .show-consent-subtype-label,
  .show-consent-expiry_date-label,
  .show-consent-commencement_date-label,
  .show-consent-holder-label,
  .show-consent-description-label,
  .show-consent-council-label {
    @include stroke(1, black);
    color: white;
    font-size: 13px;
    font-weight: bold;
    letter-spacing: 0.5px;

    &::before {
      letter-spacing: 0.5px;
      font-weight: normal;
      font-size: 14px;
      color: black;
      text-transform: none;
      text-shadow: none;
      margin-right: 8px;
      content: 'Show';
    }
  }
}

.map-container-list .show-sale-label,
.map-container-list .show-consent-category-label,
.map-container-marker-label.lightyellow {
  color: lightyellow;
}

.map-container-list .show-titles-owners-label,
.map-container-list .show-owners-label,
.map-container-list .show-consent-iris_id-label,
.map-container-marker-label.yellow {
  color: yellow;
}

.map-container-list .show-titles-areas-label,
.map-container-list .show-consent-subtype-label,
.map-container-list .show-areas-label,
.map-container-marker-label.orange {
  color: orange;
}

.map-container-list .show-consent-holder-label,
.map-container-list .show-trading-groups-label,
.map-container-marker-label.cyan {
  color: cyan;
}

.map-container-list .show-consent-description-label,
.map-container-list .show-saledate-label,
.map-container-marker-label.pink {
  color: pink;
}

.map-container-list .show-consent-expiry_date-label,
.map-container-list .show-vendor-label,
.map-container-marker-label.lightcyan {
  color: LightCyan;
}

.map-container-list .show-consent-commencement_date-label,
.map-container-list .show-purchaser-label,
.map-container-marker-label.springgreen {
  color: springgreen;
}

div.map-container-list-item {
  display: flex;
  align-items: center;
  padding: 0.5em 1em;
}

.map-container-list-legend {
  border: 1px solid #eee;
  padding: 1em;
  margin-top: 1em;

  .map-container-list-legend-title {
    margin-bottom: 0.5em;
    padding-bottom: 0.5em;
    border-bottom: 1px solid #eee;
  }

  .map-container-list-legend-item {
    display: flex;
  }

  .map-container-list-legend-color {
    width: 24px;
    height: 24px;
    margin-right: 1em;
  }
}

tr.map-container-list-item {
  width: 100%;
  overflow: auto;

  th,
  td {
    padding: 4px 8px;
  }

  td {
    padding-bottom: 8px;
  }

  &.selected {
    background-color: rgb(255, 255, 153) !important;

    &:nth-child(even) {
      background-color: rgb(245, 245, 143) !important;
    }

    &:nth-child(even):hover,
    &:hover {
      background-color: rgba(255, 255, 153, 0.5) !important;
    }
  }
}

.map-container-list-item-color-container {
  display: grid;
  grid-template-columns: auto 1fr;
  column-gap: 18px;
}

.map-container-list-item-color-container > :first-child {
  padding-top: 4px;
}

.map-container-list-input {
  display: flex;
  align-items: center;

  .ant-picker-range {
    max-width: 200px;
  }

  .map-container-list-input-label {
    min-width: 100px;
  }
}

.map-container-list-item {
  transition: 0.25s background-color;
  font-size: 12px;

  &.flex {
    display: flex;
    justify-content: space-between;
  }

  &:hover {
    background-color: rgba(255, 255, 153, 0.5) !important;
    // outline         : 1px solid #ccc;
    cursor: pointer;
  }

  .map-container-list-item-color {
    margin-right: 18px;
    width: 18px;
    height: 18px;
    font-size: 13px;
    font-weight: bold;
    line-height: 13px;
    display: flex;
    padding: 0;
    justify-content: center;
    align-items: center;
    outline: 1px solid #444;
    flex-shrink: 0;
  }

  &[data-focus='true'] {
    background-color: rgb(255, 255, 153);
  }
}

.agrigis-map-straight-line-label-text {
  border: none;
  background-color: none;
}

.map-container-straight-line-point {
  width: 16px;
  height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid black;
}

.map-container-info-text {
  p {
    margin-bottom: 0;
  }
}

ul.mortgagee-list {
  margin: 0;
  padding: 0;
}

.leaflet-tooltip-left {
  align-items: flex-end;
  display: flex;
  flex-direction: column;
  z-index: 99999; // Quick and dirty fix, tooltip should be consolidated to single component
}

.map-container-marker-label-container {
  padding-left: 18px;
  max-width: 300px;
  transition: 0.25s display;
  z-index: 10110 !important;
  display: flex;
  flex-direction: column;
  justify-content: center;

  &.on-hover:not(.hovered) {
    display: none;
  }
}

.map-container-marker-label {
  color: white;
  margin: 0 8px;
  width: max-content;
  @include stroke(2, black);
  font-size: 13px;
  opacity: 0.9;
  z-index: 10110 !important;
}

*:not(:has(.ExplorerTradingGroupModal)) {
  // The following rules break centering on modals and should be removed or scoped to their use

  .ant-modal-root {
    padding-left: 10vw;
    padding-right: 10vw;
    display: flex;
  }

  .ant-modal-wrap {
    display: flex;
  }

  .map-container-modal {
    display: flex;
    justify-content: center;
    align-self: flex-start;
    width: 100% !important;
    top: 2em;

    .ant-modal-content {
      max-width: 1920px;
    }
  }
}

.map-container-modal-table {
  display: block;
  width: 100%;
  overflow: auto;

  thead {
    white-space: nowrap;
    font-size: 11px;
  }

  tbody {
    vertical-align: top;
  }

  th,
  td {
    padding: 8px;
  }

  th:not(:last-child),
  td:not(:last-child) {
    border-right: 1px solid #ddd;
  }

  tr:last-child {
    border-bottom: 2px solid #ddd;
  }

  tr:nth-child(even) {
    background-color: rgba(240, 240, 240, 0.75);
  }

  th {
    color: #666;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    background-color: transparent;
    border-bottom: 2px solid #ddd;
    border-top: 2px solid #ddd;
  }
}

.map-container-controls {
  min-width: 300px;

  .map-container-list {
    padding-right: 1em;

    &:not(:last-child) {
      margin-bottom: 2em;
    }

    &::before {
      content: attr(data-title);
      border-bottom: 1px solid #ddd;
      padding-bottom: 0.5em;
      margin-bottom: 0.5em;
      font-size: 11px;
      font-weight: bold;
      color: #666;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

.map-container-widget .ant-tabs-tabpane {
  display: block;
}

.agrigis-modal-table-nested {
  background-color: white;

  th {
    letter-spacing: 0;
    font-weight: normal;
    text-transform: none;
    background-color: transparent !important;
  }

  th,
  td {
    padding: 4px;
  }

  tr:nth-child(even) {
    background-color: white;
  }

  tr {
    border-bottom: 1px solid #eee;

    th,
    td {
      &:first-child {
        border-left: 1px solid #ddd;
      }

      &:last-child {
        border-right: 1px solid #ddd;
      }
    }
  }
}

.ant-modal-root,
.ant-modal-mask,
.ant-modal-wrap {
  z-index: 10005;
}

.ant-message {
  z-index: 10010;
}

.rc-virtual-list {
  z-index: 10006;

  * {
    z-index: 10006;
  }
}
