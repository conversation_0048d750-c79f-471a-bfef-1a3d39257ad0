import React from 'react'
import { PdfTable } from '@components/pdf'
import type { ValuationPriorMarketValue } from '@store/services/sdk'
import { PLACEHOLDER_CHAR } from '@util/const'

type Props = {
  priorMarketValue: ValuationPriorMarketValue
}

const PriorMarketValueTable = ({ priorMarketValue }: Props) => {
  const rows = [
    {
      key: 'Prior Market Value',
      value: priorMarketValue.valueDollars,
    },
    {
      key: 'Change %',
      value: `${priorMarketValue.changePercent ?? '0'}%`,
    },
    {
      key: 'Assessment Type',
      value: priorMarketValue.assessmentType,
    },
    {
      key: 'Justification',
      value: priorMarketValue.justification,
    },
    {
      key: 'Date of Prior Assessment',
      value: priorMarketValue.formattedDateOfPriorAssessment ?? '-',
    },
  ]

  return (
    <PdfTable
      margins={false}
      striped
      rows={rows}
      placeholder={PLACEHOLDER_CHAR}
    />
  )
}

export default PriorMarketValueTable
