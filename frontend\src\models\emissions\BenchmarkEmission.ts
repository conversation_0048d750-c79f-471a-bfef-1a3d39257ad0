type BenchmarkEmissionMeasure = 'UPPER' | 'LOWER' | 'MEDIAN' | 'CUSTOMER'

export interface BenchmarkEmissionValue {
  measure: BenchmarkEmissionMeasure
  electricityCo2eKgEffArea: number
  fertiliserCo2eKgEffArea: number
  fuelCo2eKgEffArea: number
  stockCo2eKgEffArea: number
  totalCo2eKgEffArea: number
}

export interface BenchmarkEmission {
  regionId: string
  industryId: string
  sampleSize: number
  effectiveArea: number
  data: BenchmarkEmissionValue[]
}
