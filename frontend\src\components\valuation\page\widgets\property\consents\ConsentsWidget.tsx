import type { FeatureCollection, Geometry } from 'geojson'
import React from 'react'
import ValuationResourceConsentRecordsCard from '@components/valuation/ValuationResourceConsentRecordsCard'
import type { WaterConsent } from '@models/valuations/WaterConsent'
import { ValuationConsentRelatedDocumentWidget } from './ValuationConsentRelatedDocumentWidget'
import { ValuationPageWaterConsentWidget } from './ValuationPageWaterConsentWidget'
import { WaterSecurityValidationWidget } from './WaterSecurityValidationWidget'

export interface ConsentsWidgetProps {
  valuationId: string
  valuationPageWaterConsents:
    | FeatureCollection<Geometry, WaterConsent>
    | undefined
  disabled?: boolean
}

export const ConsentsWidget = ({
  valuationId,
  valuationPageWaterConsents,
  disabled,
}: ConsentsWidgetProps) => {
  return (
    <>
      <WaterSecurityValidationWidget
        valuationId={valuationId}
        disabled={disabled}
      />
      {valuationPageWaterConsents && (
        <ValuationPageWaterConsentWidget
          valuationPageWaterConsents={valuationPageWaterConsents}
        />
      )}
      <ValuationResourceConsentRecordsCard />
      <ValuationConsentRelatedDocumentWidget
        valuationId={valuationId}
        disabled={disabled}
      />
    </>
  )
}
