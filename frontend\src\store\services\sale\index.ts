import { Address } from '@/types'
import { SalePDFData } from '@models/sales/SalePDFData'
import { AddressNeighbourFeatureCollection } from '../../../models/address/AddressNeighbour'
import { AnzUnionFeatureCollection } from '../../../models/gis/AnzUnionFeatureCollection'
import { ElevationFeatureCollection } from '../../../models/gis/ElevationFeatureCollection'
import { PredictedSale } from '../../../models/sales/PredictedSale'
import { SaleFeature } from '../../../models/sales/SaleFeatureCollection'
import { SaleFile } from '../../../models/sales/SaleFile'
import { SalePropertyDescription } from '../../../models/sales/SalePropertyDescription'
import { TitleFeatureCollection } from '../../../models/title/TitleFeatureCollection'
import { baseApi } from '../baseApi'

export const saleApi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    predictSale: build.query<
      PredictedSale,
      { addressId: string; titleIds: string[] } | undefined
    >({
      query: (body) => {
        if (!body) {
          return ''
        }
        const { addressId, titleIds } = body

        if (titleIds?.length === 0 || !addressId || addressId === '') {
          return ''
        }

        const baseEndpoint = '/api/predict_sale/'
        let queryString = '?address_id=' + addressId
        queryString += '&title_id=' + titleIds?.join('&title_id=')

        return baseEndpoint + queryString
      },
    }),
    updateSaleTitles: build.mutation<
      void,
      { saleId: string; titles: string[] }
    >({
      query: (body) => {
        const { saleId, titles } = body
        return {
          url: `/api/sales/${saleId}/titles/`,
          method: 'PUT',
          body: titles,
        }
      },
      invalidatesTags: (result, error, args) => [
        { type: 'SaleTitles', id: args.saleId },
        { type: 'SaleUnion', id: args.saleId },
        { type: 'SaleSmap', id: args.saleId },
        { type: 'SaleElevation', id: args.saleId },
        { type: 'SaleNeighbours', id: args.saleId },
      ],
    }),
    updateSalePropertyDescription: build.mutation<
      void,
      Partial<SalePropertyDescription>
    >({
      query: (body) => {
        const { saleId, ...data } = body
        return {
          url: `/api/sales/${saleId ?? 'undefined'}/description/`,
          method: 'POST',
          body: {
            saleId,
            ...data,
          },
        }
      },
      invalidatesTags: (result, error, args) => [
        { type: 'SaleDescription', id: args.saleId },
      ],
    }),
    uploadSaleFile: build.mutation<
      void,
      { saleId: string; formData: FormData }
    >({
      query: (body) => {
        const { saleId, formData } = body
        return {
          method: 'POST',
          url: `/api/sales/${saleId}/files/`,
          body: formData,
          headers: {
            'Content-Type': undefined,
          },
        }
      },
      invalidatesTags: (result, error, args) => [
        { type: 'SaleFiles', id: args.saleId },
      ],
    }),
    deleteSaleFile: build.mutation<
      void,
      { saleId: string; saleFileId: string }
    >({
      query: (body) => {
        const { saleId, saleFileId } = body
        return {
          method: 'DELETE',
          url: `/api/sales/${saleId}/files/${saleFileId}/`,
        }
      },
      invalidatesTags: (result, error, args) => [
        { type: 'SaleFiles', id: args.saleId },
      ],
    }),
    getSale: build.query<SaleFeature, string>({
      query: (saleId) => `/api/sales/${saleId}/`,
      providesTags: (result, error, args) => [{ type: 'Sale', id: args }],
    }),
    getSaleAddress: build.query<Address, string>({
      query: (saleId) => `/api/sales/${saleId}/address/`,
      providesTags: (result, error, args) => [
        { type: 'SaleAddress', id: args },
      ],
    }),
    getSaleElevation: build.query<ElevationFeatureCollection, string>({
      query: (saleId) => `/api/sales/${saleId}/elevation/`,
      providesTags: (result, error, args) => [
        { type: 'SaleElevation', id: args },
      ],
    }),
    getSaleUnion: build.query<AnzUnionFeatureCollection, string>({
      query: (saleId) => `/api/sales/${saleId}/union/`,
      providesTags: (result, error, args) => [{ type: 'SaleUnion', id: args }],
    }),
    getSaleSmapFamily: build.query<AnzUnionFeatureCollection, string>({
      query: (saleId) => `/api/sales/${saleId}/smap_family/`,
      providesTags: (result, error, args) => [{ type: 'SaleSmap', id: args }],
    }),
    getSaleTitles: build.query<TitleFeatureCollection, string>({
      query: (saleId) => `/api/sales/${saleId}/titles/`,
      providesTags: (result, error, args) => [{ type: 'SaleTitles', id: args }],
    }),
    getSaleFiles: build.query<SaleFile[], string>({
      query: (saleId) => `/api/sales/${saleId}/files/`,
      providesTags: (result, error, args) => [{ type: 'SaleFiles', id: args }],
    }),
    getSaleNeighbours: build.query<AddressNeighbourFeatureCollection, string>({
      query: (saleId) => `/api/sales/${saleId}/neighbours/`,
      providesTags: (result, error, args) => [
        { type: 'SaleNeighbours', id: args },
      ],
    }),
    getSaleDescription: build.query<
      SalePropertyDescription,
      { saleId: string; asHtml?: boolean }
    >({
      query: (body) => {
        const { saleId, asHtml } = body
        return (
          `/api/sales/${saleId}/description/` + (asHtml ? '?as_html=true' : '')
        )
      },
      providesTags: (result, error, args) => [
        { type: 'SaleSmap', id: args.saleId },
      ],
    }),
    getSalePDFData: build.query<SalePDFData, { saleId: string }>({
      query: (body) => {
        const url = `/api/sales/${body?.saleId}/pdf/?`
        return url
      },
      providesTags: (result, error, args) => [
        {
          type: 'SalePDFData',
          id: `${args?.saleId}`,
        },
      ],
    }),
  }),
})

export const {
  usePredictSaleQuery,
  useGetSaleQuery,
  useGetSaleElevationQuery,
  useGetSaleUnionQuery,
  useGetSaleSmapFamilyQuery,
  useGetSaleTitlesQuery,
  useGetSaleNeighboursQuery,
  useGetSaleFilesQuery,
  useGetSaleAddressQuery,
  useGetSaleDescriptionQuery,
  useUpdateSalePropertyDescriptionMutation,
  useUpdateSaleTitlesMutation,
  useUploadSaleFileMutation,
  useDeleteSaleFileMutation,
  useGetSalePDFDataQuery,
} = saleApi
