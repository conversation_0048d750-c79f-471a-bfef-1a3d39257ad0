import type { PortfolioDataValues } from './PortfolioDataValues'
import type { ccrKeys, industryKeys, regionKeys, siKeys } from './keys'

export type siPortfolioData = {
  [si in keyof siKeys]: PortfolioDataValues
}

export type ccrPortfolioData = {
  [ccr in keyof ccrKeys]: PortfolioDataValues
}

export type industryPortfolioData = {
  [industry in keyof industryKeys]: PortfolioDataValues
}

export type regionPortfolioData = {
  [region in keyof regionKeys]: PortfolioDataValues
}

export interface PortfolioData {
  si: siPortfolioData
  ccr: ccrPortfolioData
  industry: industryPortfolioData
  region: regionPortfolioData
}
