export type KpiKeys = {
  totalFarmWorkingExpensesEffArea: string
  beefIncomeEffArea: string
  croppingIncomeEffArea: string
  dairyIncomeEffArea: string
  deerIncomeEffArea: string
  grapesStonefruitIncomeEffArea: string
  horticultureIncomeEffArea: string
  kiwifruitIncomeEffArea: string
  milkIncomeEffArea: string
  otherIncomeEffArea: string
  otherStockIncomeEffArea: string
  pigsIncomeEffArea: string
  pipfruitIncomeEffArea: string
  poultryIncomeEffArea: string
  sheepIncomeEffArea: string
  sundryFarmIncomeEffArea: string
  velvetIncomeEffArea: string
  woolIncomeEffArea: string
  fwePerSuEffArea: string
  ebitEffArea: string
  ebitPerTgiPercentageEffArea: string
  effectiveAreaEffArea: string
  fwePerCowEffArea: string
  administrationEffArea: string
  animalHealthEffArea: string
  breedingAndTestingEffArea: string
  contractingEffArea: string
  electricityEffArea: string
  feedHayPerSilagePerGrainEffArea: string
  fertiliserAndLimeEffArea: string
  freightEffArea: string
  grazingEffArea: string
  miscellaneousPerOtherFweEffArea: string
  pollinationEffArea: string
  postHarvestExpensesEffArea: string
  repairsAndMaintenanceEffArea: string
  seedEffArea: string
  shedExpensesEffArea: string
  standingChargesEffArea: string
  vehicleExpensesEffArea: string
  wagesEffArea: string
  weedAndPestEffArea: string
}
