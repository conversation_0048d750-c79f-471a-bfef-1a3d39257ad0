import {
  type ActionReducerMapBuilder,
  type PayloadAction,
  type SliceCaseReducers,
  type ValidateSliceCaseReducers,
  createSlice,
  isAnyOf,
} from '@reduxjs/toolkit'
import type { LatLngBounds, LatLngExpression, LatLngLiteral } from 'leaflet'
import { mapApi } from '@store/services/map'
import sdk from '@store/services/sdk'
import { getStoredState } from '@store/util'
import type { PrintPageFormat } from '@util/const'
import { equals, not } from '@util/helpers'
import type { ExplorerLayer, PageOrientation } from '@util/types'
import type { MapState } from '../map/types'
import type { ExplorerBaseLayer } from './types'

interface GenericMapState {
  baseLayer: string
  boundsString: string
  center: LatLngLiteral
  layersLoading: number
  zoom: number
}

/**
 * @description This should replace ExplorerLayer
 **/
type ExplorerLayers =
  | 'address'
  | 'sale'
  | 'listing'
  | 'valocitySale'
  | 'valocityListing'
  | 'resourceConsent'

export type ExplorerSelectableLayer = ExplorerLayers

export type ExplorerMenu = 'aggregate' | 'map' | undefined

export type ExplorerLayerMenu = ExplorerLayers | ExplorerMenu

const createMapSlice = <
  T,
  Reducers extends SliceCaseReducers<GenericMapState & T>,
>({
  name = 'genericMapSlice',
  initialState,
  reducers,
  extraReducers,
}: {
  name: string
  initialState: T
  reducers: ValidateSliceCaseReducers<GenericMapState & T, Reducers>
  extraReducers?:
    | ((builder: ActionReducerMapBuilder<GenericMapState & T>) => void)
    | undefined
}) => {
  return createSlice({
    name,
    initialState: {
      baseLayer: 'OpenStreetMap',
      boundsString: '',
      center: { lng: 175.1070803, lat: -37.4225643 },
      layersLoading: 0,
      zoom: 15.5,
      ...initialState,
    },
    reducers: {
      setBounds(state, { payload }: PayloadAction<LatLngBounds>) {
        state.boundsString = payload.toBBoxString()
      },
      setBoundsString(state, { payload }: PayloadAction<string>) {
        state.boundsString = payload
      },
      setCenter(state, { payload }: PayloadAction<LatLngLiteral>) {
        state.center = payload
      },
      setZoom(state, { payload }: PayloadAction<number>) {
        state.zoom = payload
      },
      ...reducers,
    },
    extraReducers,
  })
}

export enum TooltipSizes {
  S = 0,
  M = 1,
  L = 2,
}

export interface ExplorerState
  extends Omit<MapState & GenericMapState, 'menuType'> {
  baseLayer: ExplorerBaseLayer
  exportPageFormat: PrintPageFormat
  exportPageOrientation: PageOrientation
  isDrawing: boolean
  layerOrder: ExplorerLayer[]
  menuFeatureId: number | undefined
  menuType: ExplorerLayerMenu
  menuLatLng: LatLngExpression | undefined
  menuPosition: { x: number; y: number }
  searching: boolean
  showExportPreview: boolean
  showSidebar: boolean
  titleMode: boolean
  titleMinSqM: number | undefined
  titlePolygonIsGrouped: boolean
  titleTooltipSize: TooltipSizes
  titleShowMortgageeShading: boolean
  showSelectedAddressTooltip: boolean
  showSelectedTooltips: ExplorerLayers[]
}

const initialState = (): Partial<ExplorerState> => {
  return {
    baseLayer: 'OpenStreetMap',
    exportPageFormat: 'A3',
    exportPageOrientation: 'landscape',
    isDrawing: false,
    menuFeatureId: undefined,
    menuLatLng: undefined,
    menuPosition: { x: 0, y: 0 },
    menuType: undefined,
    searching: false,
    showExportPreview: false,
    showSidebar: false,
    titleMode: true,
    titleMinSqM: undefined,
    titlePolygonIsGrouped: false,
    titleShowMortgageeShading: true,
    titleTooltipSize: TooltipSizes.M,
    showSelectedAddressTooltip: true,
    showSelectedTooltips: ['address', 'listing'],
    ...getStoredState(SLICE_NAME),
    layerOrder: [
      'addresses',
      'anzUnion',
      'consents',
      'sales',
      'listings',
      'titles',
      'valocityListings',
      'valocitySales',
    ],
  }
}

function setMenuOpenOfType<T extends ExplorerState>(
  menuType: ExplorerState['menuType']
) {
  return function setMenuOpen(
    state: T,
    {
      payload,
    }: PayloadAction<{
      position: ExplorerState['menuPosition']
      id?: ExplorerState['menuFeatureId']
      latLng?: ExplorerState['menuLatLng']
    }>
  ) {
    state.menuFeatureId = payload.id
    state.menuPosition = payload.position
    state.menuLatLng = payload.latLng
    state.menuType = menuType
  }
}

const SLICE_NAME = 'explorer'

const explorer = createMapSlice({
  name: SLICE_NAME,
  initialState: initialState() as ExplorerState,
  reducers: {
    decreaseTitleTooltipSize(state) {
      state.titleTooltipSize = Math.max(
        state.titleTooltipSize - 1,
        TooltipSizes.S
      )
    },
    increaseTitleTooltipSize(state) {
      state.titleTooltipSize = Math.min(
        state.titleTooltipSize + 1,
        TooltipSizes.L
      )
    },
    setBaseLayer(
      state,
      { payload }: PayloadAction<ExplorerState['baseLayer']>
    ) {
      state.baseLayer = payload
    },
    setAddressMenuOpen: setMenuOpenOfType('address'),
    setResourceConsentMenuOpen: setMenuOpenOfType('resourceConsent'),
    setAggregateMenuOpen: setMenuOpenOfType('aggregate'),
    setExportPageFormat(state, { payload }: PayloadAction<PrintPageFormat>) {
      state.exportPageFormat = payload
    },
    setExportPageOrientation(
      state,
      { payload }: PayloadAction<PageOrientation>
    ) {
      state.exportPageOrientation = payload
    },
    setIsDrawing(state, { payload }: PayloadAction<boolean>) {
      state.isDrawing = payload
    },
    setLayerOrder(state, { payload }: PayloadAction<ExplorerLayer[]>) {
      state.layerOrder = payload
    },
    setListingMenuOpen: setMenuOpenOfType('listing'),
    setMapMenuOpen(
      state: ExplorerState,
      { payload }: PayloadAction<{ position: ExplorerState['menuPosition'] }>
    ) {
      if (
        state.menuType === 'map' &&
        JSON.stringify(state.menuPosition) === JSON.stringify(payload.position)
      ) {
        state.menuType = undefined
      } else {
        state.menuPosition = payload.position
        state.menuType = 'map'
      }
    },
    setMenuClosed(state) {
      state.menuType = undefined
    },
    setSaleMenuOpen: setMenuOpenOfType('sale'),
    setTitleMinSqM(
      state,
      { payload }: PayloadAction<ExplorerState['titleMinSqM']>
    ) {
      state.titleMinSqM = payload
    },
    setTitleTooltipSize(
      state,
      { payload }: PayloadAction<ExplorerState['titleTooltipSize']>
    ) {
      state.titleTooltipSize = payload
    },
    toggleShowExportPreview(state) {
      state.showExportPreview = !state.showExportPreview
    },
    toggleShowSidebar(state) {
      state.showSidebar = !state.showSidebar
    },
    toggleTitleMode(state) {
      state.titleMode = !state.titleMode
    },
    toggleTitlePolygonIsGrouped(state) {
      state.titlePolygonIsGrouped = !state.titlePolygonIsGrouped
    },
    toggleTitleShowMortgageeShading(state) {
      state.titleShowMortgageeShading = !state.titleShowMortgageeShading
    },
    toggleShowSelectedAddressTooltip(state) {
      state.showSelectedAddressTooltip = !state.showSelectedAddressTooltip
    },
    toggleSelectedTooltip(state, { payload }: PayloadAction<ExplorerLayers>) {
      const selected = state.showSelectedTooltips
      if (selected.includes(payload))
        state.showSelectedTooltips = [...selected].filter(not(equals(payload)))
      else state.showSelectedTooltips = [...selected, payload]
    },
  },
  extraReducers: (builder) => {
    builder.addMatcher(
      isAnyOf(
        mapApi.endpoints.getViewportAddresses.matchPending,
        mapApi.endpoints.getViewportSales.matchPending,
        mapApi.endpoints.getViewportTitles.matchPending,
        sdk.endpoints.explorerTitlesList.matchPending
      ),
      (state) => {
        state.layersLoading += 1
      }
    )
    builder.addMatcher(
      isAnyOf(
        mapApi.endpoints.getViewportAddresses.matchFulfilled,
        mapApi.endpoints.getViewportAddresses.matchRejected,
        mapApi.endpoints.getViewportSales.matchFulfilled,
        mapApi.endpoints.getViewportSales.matchRejected,
        mapApi.endpoints.getViewportTitles.matchFulfilled,
        mapApi.endpoints.getViewportTitles.matchRejected,
        sdk.endpoints.explorerTitlesList.matchFulfilled,
        sdk.endpoints.explorerTitlesList.matchRejected
      ),
      (state) => {
        state.layersLoading = Math.max(state.layersLoading - 1, 0)
      }
    )
    builder.addMatcher(
      mapApi.endpoints.searchForAddresses.matchPending,
      (state) => {
        state.searching = true
      }
    )
    builder.addMatcher(
      isAnyOf(
        mapApi.endpoints.searchForAddresses.matchFulfilled,
        mapApi.endpoints.searchForAddresses.matchRejected
      ),
      (state) => {
        state.searching = false
      }
    )
  },
})

export const actions = explorer.actions

export const reducer = explorer.reducer

export default explorer
