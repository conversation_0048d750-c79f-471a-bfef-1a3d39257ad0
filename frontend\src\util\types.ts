import type { Feature, Geometry } from 'geojson'
import type { Location } from 'react-router-dom'
import type { EXPLORER_LAYERS } from './const'

export type GraphData = {
  measure: string
  periodTo: string
  value: number
  rank?: number
}

export type DataVisualisationItem = GraphData

export type DataVisualisationMode = '$' | '%' | 'number'

export type ExplorerLayer = (typeof EXPLORER_LAYERS)[number]

export type PageOrientation = 'landscape' | 'portrait'

export type NotEmpty<T> = Record<string, never> extends T ? never : T

export type RedirectState = { from: Location }

export type Properties<T, K extends keyof T> = Pick<T, K> & Partial<Omit<T, K>>

export type PropertiesMatching<T, V> = {
  [K in keyof T as T[K] extends V | undefined ? K : never]: T[K]
}

export type KeysMatching<T, V> = {
  [K in keyof T]-?: T[K] extends V ? K : never
}[keyof T]

export type MaybeNumber = number | string | undefined | null

type PartialFeature = Omit<Partial<Feature>, 'geometry'> & {
  geometry?: Partial<Geometry>
  properties?: Partial<Feature['properties']>
}

export type FeatureFixup<T extends PartialFeature> = Required<T> & {
  type: NonNullable<T['type']>
  geometry: Required<T['geometry']>
}

export type FeatureCollectionFixup<T extends PartialFeature> = {
  type: 'FeatureCollection'
  features: FeatureFixup<T>[]
}

export interface FileState {
  file: File
  description: string
}
export interface FileListState {
  [fileName: string]: FileState
}

export interface SelectOption<T> {
  value: NonNullable<T>
  label?: string
}
