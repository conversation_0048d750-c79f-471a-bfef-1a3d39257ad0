import { baseApi } from '../baseApi'

export const eventApi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    recordPageLoad: build.mutation<void, { pageUrl: string }>({
      query(body) {
        return {
          url: `/api/events/page_load/`,
          method: 'POST',
          body,
        }
      },
    }),
    recordPageError: build.mutation<
      void,
      {
        pageUrl: string
        error: string
        stacktrace: string | undefined
        componentStack: string | undefined
      }
    >({
      query(body) {
        return {
          url: '/api/events/error/',
          method: 'POST',
          body,
        }
      },
    }),
  }),
})

export const { useRecordPageLoadMutation, useRecordPageErrorMutation } =
  eventApi
