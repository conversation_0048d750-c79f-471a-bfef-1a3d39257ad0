import type { Address } from '@/types'
import type {
  Feature,
  FeatureCollection,
  Geometry,
  MultiPolygon,
  Point,
} from 'geojson'

export type AddressFeature<T extends Geometry = Point, U = Address> = Feature<
  T,
  U
> & { id: number }

export interface AddressFeatureCollection extends FeatureCollection {
  features: AddressFeature<MultiPolygon>[]
}

export interface AddressPointFeatureCollection extends FeatureCollection {
  features: AddressFeature[]
}
