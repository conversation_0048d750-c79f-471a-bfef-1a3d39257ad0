import { Table } from 'antd'
import React, { useMemo } from 'react'
import type { Rainfall } from '../../../../../../types'
import { titleCase } from '../../../../../../util'

interface ValuationPageRainfallTableProps {
  valuationRainfall: Rainfall[]
}

export const ValuationPageRainfallTable = ({
  valuationRainfall,
}: ValuationPageRainfallTableProps) => {
  const [seasonalRainfall] = valuationRainfall
  const totalRainfall = useMemo(() => {
    return Object.values(seasonalRainfall).reduce<number>(
      (a: number, b: number) => a + b,
      0
    )
  }, [seasonalRainfall])

  const columns = useMemo(() => {
    return [
      {
        dataIndex: 'key',
        title: 'Season',
        render: (key: string) => titleCase(key),
      },
      { dataIndex: 'value', title: 'Rainfall Range' },
    ]
  }, [])

  const dataSource = useMemo(() => {
    return Object.entries(seasonalRainfall).map(([key, value]) => {
      const start = `${value * 25 - 25}`
      const end = `${value * 25}mm`
      return {
        key,
        value: `${start} - ${end}`,
      }
    })
  }, [seasonalRainfall])

  return (
    <Table
      size="small"
      pagination={false}
      columns={columns}
      rowKey={(row) => row.key}
      dataSource={dataSource}
      summary={() => (
        <Table.Summary.Row>
          <Table.Summary.Cell index={0}>
            <b>Total</b>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={1}>
            <b> {`${totalRainfall * 25 - 25} - ${totalRainfall * 25}mm`}</b>
          </Table.Summary.Cell>
        </Table.Summary.Row>
      )}
    />
  )
}
