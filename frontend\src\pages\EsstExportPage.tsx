import { Helmet } from 'react-helmet'
import { useParams } from 'react-router-dom'
import EsstLayout from '@components/esst/EsstLayout'
import EsstPdf from '@components/esst/EsstPdf'
import { useEsstReportsExportRetrieveQuery } from '@store/services/esst/codegen'
import { PDFViewer } from '@react-pdf/renderer'

export default function EsstExportPage() {
  const { esstId } = useParams()

  const { data } = useEsstReportsExportRetrieveQuery({ pk: Number(esstId) })

  if (!data) return null

  return (
    <>
      <Helmet>
        <title>ESST {esstId} Export</title>
      </Helmet>
      <div style={{ width: '100%', height: '100%', display: 'flex' }}>
        <PDFViewer style={{ flex: 1 }}>
          <EsstPdf data={data} />
        </PDFViewer>
      </div>
    </>
  )
}
