from ninja import NinjaAPI
# Use the main API from apps.api which has complete model integration
from apps.api.api import peril_router, location_router, loss_router

api = NinjaAPI(
    title="RiskRadar API",
    version="1.0.0",
    description="API for managing perils, locations, and risk models",
)

# Add routers from the main API app
api.add_router("/perils", peril_router)
api.add_router("/locations", location_router)
api.add_router("/loss-models", loss_router)