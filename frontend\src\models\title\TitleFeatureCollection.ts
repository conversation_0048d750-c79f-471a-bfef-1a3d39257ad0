import type { Feature, Geometry, MultiPolygon, Point } from 'geojson'
import type { TitleMemorial } from '../../types'

/**
 * @deprecated Move to using codegen types, currently this is used to paper over at least 3 different API endpoints
 */
export interface TitleProperties {
  id: number
  fid: number
  titleId: number
  titleNo: string
  status: string
  type: string
  landDistrict: string
  issueDate: string
  guaranteeStatus: string
  estateDescription: string
  owners: string
  spatialExtentsShared: string
  area: number
  surveyArea: number
  surveyAreaHa: string
  memorials?: TitleMemorial[]
  mortgagee?: string
  cnt?: number
  fullAddress?: string
  deletedDate: string | null
}

/**
 * @deprecated Should be using codegen types
 */
export type TitleFeature<T extends null | Geometry = MultiPolygon> = Feature<
  T | null,
  TitleProperties
> & { id: number | string }

export type MultiPolygonTitleFeature = TitleFeature<MultiPolygon>

/**
 * @deprecated Should be using codegen types
 */
export interface TitleFeatureCollection {
  type: 'FeatureCollection'
  features: Array<TitleFeature>
}

/**
 * @deprecated Should be using codegen types
 */
export interface AggregateTitleFeatureCollection {
  type: 'FeatureCollection'
  features: Array<TitleFeature<MultiPolygon | Point | null>>
}
