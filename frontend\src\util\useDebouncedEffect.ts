import { type EffectCallback, useCallback, useEffect } from 'react'

export function useDebouncedEffect<T extends EffectCallback, U>(
  effect: T,
  delay: number,
  deps: U[]
): void {
  const callback = useCallback(effect, [...deps])
  useEffect(() => {
    let effectUndo: ReturnType<EffectCallback>
    const handler = setTimeout(() => {
      effectUndo = callback()
    }, delay)
    return () => {
      try {
        if (effectUndo) {
          effectUndo()
        }
        clearTimeout(handler)
      } catch (_) {
        console.error('Failed to cancel request due to page change')
      }
    }
  }, [callback, delay])
}
