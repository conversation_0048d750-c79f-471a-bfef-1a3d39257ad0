import { message } from 'antd'
import useFormInstance from 'antd/lib/form/hooks/useFormInstance'
import type { ValidateErrorEntity } from 'rc-field-form/lib/interface'
import { useCallback, useState } from 'react'
import { getQueryErrorMessage } from '@util/error'

type Callback = (...args: unknown[]) => unknown

type FormSubmit<T extends Callback> = (
  values: Parameters<T>
) => Promise<ReturnType<T>>

function useFormSubmit<T extends Callback>(submit: FormSubmit<T>) {
  const form = useFormInstance()
  const [loading, setLoading] = useState(false)

  const handleFinishFailed = useCallback(
    ({ errorFields }: ValidateErrorEntity<T>) => {
      form.setFields(errorFields)
      void message.error(
        'Validation failed, please ensure all fields have no errors.'
      )
    },
    [form]
  )

  const handleFinish = useCallback(
    async (values: Parameters<T>) => {
      try {
        setLoading(true)
        await submit(values)
        void message.success('Saved form successfully.')
      } catch (err) {
        const errorMessage = getQueryErrorMessage(err) || 'Unknown error'
        void message.error(errorMessage)
      }
      setLoading(false)
    },
    [submit]
  )

  return {
    handleFinish,
    handleFinishFailed,
    loading,
  }
}

export default useFormSubmit
