import { EyeFilled, EyeOutlined } from '@ant-design/icons'
import { Table, type TableColumnsType } from 'antd'
import React, { useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { ButtonWidget } from '@components/generic'
import type { RootState } from '../../../../../store'
import {
  addressesActions,
  addressesSelectors,
} from '../../../../../store/features/address'
import { useGetAddressServiceCentreDistancesQuery } from '../../../../../store/services/address'
import type { AddressServiceCentre, GeoFeature } from '../../../../../types'
import { HoverButton } from '../../../../generic/HoverButton'
import { Widget } from '../../../../generic/Widget'

export interface ServiceCentreWidgetProps {
  addressId: string
  fullAddress: string
}

const createMapsLink = (
  address: string,
  feature: GeoFeature<AddressServiceCentre>
) => {
  return `https://www.google.com/maps/dir/?api=1&origin=${encodeURIComponent(
    address
  )}&destination=${encodeURIComponent(feature.properties.name)}`
}

export const ServiceCentreWidget = (props: ServiceCentreWidgetProps) => {
  const { addressId, fullAddress } = props

  const dispatch = useDispatch()

  const { data: serviceCentres } =
    useGetAddressServiceCentreDistancesQuery(addressId)
  const distancePanelState = useSelector((state: RootState) =>
    addressesSelectors.getPanelState(state, 'distances')
  )

  const serviceCentresArray = useMemo(() => {
    let servCentres: GeoFeature<AddressServiceCentre>[] = []
    if (serviceCentres?.features === undefined) {
      return servCentres
    }
    servCentres = [...serviceCentres.features]

    servCentres.sort(
      (
        a: GeoFeature<AddressServiceCentre>,
        b: GeoFeature<AddressServiceCentre>
      ) => {
        if (a.properties.distance <= b.properties.distance) {
          return -1
        }
        return 1
      }
    )

    return servCentres
  }, [serviceCentres])

  const toggleVisiblityButton = useMemo(() => {
    return (
      <ButtonWidget>
        <HoverButton
          onClick={() =>
            dispatch(
              addressesActions.togglePanelLayerVisibility({
                panel: 'distances',
              })
            )
          }
          type={!distancePanelState.layerVisible ? 'ghost' : 'primary'}
          icon={
            !distancePanelState.layerVisible ? <EyeOutlined /> : <EyeFilled />
          }
        >
          View
        </HoverButton>
      </ButtonWidget>
    )
  }, [distancePanelState, dispatch])

  const columns: TableColumnsType<(typeof serviceCentresArray)[number]> = [
    { dataIndex: ['properties', 'name'], title: 'Town/City' },
    {
      dataIndex: ['properties', 'generalized_classification'],
      title: 'Classification',
    },
    {
      dataIndex: ['properties', 'distance'],
      title: 'Straight Line Distance',
      render: (value) => `${(value / 1000).toFixed(2)} km`,
    },
    {
      dataIndex: '',
      title: 'Google Maps',
      render: (feature: GeoFeature<AddressServiceCentre>) => (
        <a
          target="_blank"
          rel="noopener noreferrer"
          href={createMapsLink(fullAddress, feature)}
        >
          Directions
        </a>
      ),
    },
  ]

  return (
    <Widget title="Service Centre Distances" extra={toggleVisiblityButton}>
      <div className="agrigis-table">
        <Table
          size="small"
          pagination={false}
          columns={columns}
          rowKey={(row) => row.id || '-1'}
          dataSource={serviceCentresArray}
        />
      </div>
    </Widget>
  )
}
