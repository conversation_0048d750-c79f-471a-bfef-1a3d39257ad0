import type { Kpi } from '@store/services/sdk'

const getKpiLookup = <T extends Kpi>(data: T[] | undefined) => {
  /*  I think I've future proofed this incase we want to add multiple customers.
        It does some weird aggregation thing though which I am not completely comfortable
        with, so I've just left it as a single customer for now.
    */
  const castArr = [...(data ?? [])].map(({ value, ...rest }) => ({
    value: Number(value),
    ...rest,
  }))

  const { metricLookup, years } = (() => {
    const metricLookup: Record<string, Record<string, number>> = {}
    for (const { measure, periodTo, value } of castArr) {
      if (!metricLookup[measure]) {
        metricLookup[measure] = {}
      }
      if (!metricLookup[measure][periodTo]) {
        metricLookup[measure][periodTo] = 0
      }
      metricLookup[measure][periodTo] += value
    }
    return {
      metricLookup,
      years: [...new Set(castArr.map(({ periodTo }) => periodTo))],
    }
  })()

  const getKpiMaybe = (measure: string, periodTo: string): number => {
    if (!metricLookup[measure]) return Number.NaN
    if (!metricLookup[measure][periodTo]) return Number.NaN
    return metricLookup[measure][periodTo]
  }

  const getKpi = (measure: string, periodTo: string) => {
    if (!metricLookup[measure]) return 0
    if (!metricLookup[measure][periodTo]) return 0
    return metricLookup[measure][periodTo]
  }

  return { metricLookup, years, getKpi, getKpiMaybe }
}

export default getKpiLookup
