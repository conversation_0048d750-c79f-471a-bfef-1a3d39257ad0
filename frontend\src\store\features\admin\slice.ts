import { type PayloadAction, createSlice } from '@reduxjs/toolkit'

export type SelectedModel = 'sales' | 'projects' | undefined
interface AdminState {
  selectedModel: SelectedModel
}

const initialState: AdminState = {
  selectedModel: 'sales',
}

const admin = createSlice({
  name: 'admin',
  initialState: initialState,
  reducers: {
    setSelectedModel: (state, { payload }: PayloadAction<SelectedModel>) => {
      state.selectedModel = payload
    },
  },
  //    extraReducers: (builder) => {
  //
  //    },
})

export const actions = admin.actions

export const reducer = admin.reducer

export default admin
