import { Helmet } from 'react-helmet'
import { useParams } from 'react-router-dom'
import { AgriUpliftUpdateForm } from '@components/agri-uplift/AgriUpliftForm'
import AgriUpliftLayout from '@components/agri-uplift/AgriUpliftLayout'
import AgriUpliftHistoryTag from '@components/agri-uplift/AgriUpliftHistoryTag'
import {
  type AgriUpliftHistory,
  useFinanceAgriUpliftRetrieveQuery,
} from '@store/services/finance/codegen'
import { NotFoundPage } from './NotFoundPage'
import StatusTag from '@components/agri-uplift/StatusTag'

function AgriUpliftPage() {
  const { agriUpliftId } = useParams()
  const pk = Number(agriUpliftId)

  const { data, error, isLoading } = useFinanceAgriUpliftRetrieveQuery({
    pk,
  })

  if (error) return <NotFoundPage />

  return (
    <>
      <Helmet>
        <title>
          {agriUpliftId}
          {data ? ` (${data?.history[0].date})` : ''}
        </title>
      </Helmet>
      <AgriUpliftLayout
        loading={isLoading}
        extra={
          data && (
            <>
              <StatusTag status={data.status} />
              <AgriUpliftHistoryTag
                {...(data?.history[0] as AgriUpliftHistory)}
              />
            </>
          )
        }
      >
        <AgriUpliftUpdateForm pk={pk} />
      </AgriUpliftLayout>
    </>
  )
}

export default AgriUpliftPage
