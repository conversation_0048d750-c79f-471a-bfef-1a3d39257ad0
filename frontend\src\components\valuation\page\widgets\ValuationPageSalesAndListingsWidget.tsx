import { Tabs } from 'antd'
import React, { useEffect } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import { PermissionDeniedPage } from '../../../../pages/PermissionDeniedPage'
import { useCenter } from '../../../../store/features/assets/hooks'
import { uiActions, uiSelectors } from '../../../../store/ui'
import type { SalesAndListingTabOption } from '../../../../store/ui/types'
import { ComparableSaleSearch } from '../../../valuation/page/widgets/sales/ComparableSaleSearch'
import { SalesComparisonWidget } from './sales/SalesComparisonWidget'

interface ValuationPageSalesAndListingProps {
  addressId: string
  valuationId: string
}

export const ValuationPageSalesAndListingsWidget = (
  props: ValuationPageSalesAndListingProps
) => {
  const { valuationId } = props
  const dispatch = useDispatch()

  useEffect(() => {
    dispatch(uiActions.toggleSalesAndListingLayerVisibility({ visible: true }))
    return () => {
      dispatch(
        uiActions.toggleSalesAndListingLayerVisibility({
          visible: false,
        })
      )
    }
  }, [dispatch])

  const isValuer = useSelector(uiSelectors.isValuer, shallowEqual)

  const subjectPropertyCenter = useCenter(valuationId)

  const salesAndListingsCurrentTab = useSelector(
    uiSelectors.getSalesAndListingsCurrentTab,
    shallowEqual
  )

  if (!subjectPropertyCenter) {
    return <></>
  }

  if (!isValuer) {
    return <PermissionDeniedPage />
  }

  return (
    <Tabs
      defaultActiveKey={salesAndListingsCurrentTab}
      onChange={(currentTab) => {
        dispatch(
          uiActions.setSalesAndListingCurrentTab({
            currentTab: currentTab as SalesAndListingTabOption,
          })
        )
      }}
    >
      <Tabs.TabPane tab="Comparable Sale Search" key="SALES">
        <ComparableSaleSearch
          valuationId={valuationId}
          center={subjectPropertyCenter}
        />
      </Tabs.TabPane>
      <Tabs.TabPane tab="Comparable Sales" key="COMPARISON">
        <SalesComparisonWidget valuationId={valuationId} />
      </Tabs.TabPane>
    </Tabs>
  )
}
