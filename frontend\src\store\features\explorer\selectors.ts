import { createSelector } from '@reduxjs/toolkit'
import { LatLngBounds, type LatLngTuple, Point } from 'leaflet'
import { chunk } from 'lodash'
import type { RootState } from '@store/types'
import { EXPLORER_LAYERS } from '@util/const'
import { isLatLngTuple } from '@util/guards'
import type { ExplorerLayer } from '@util/types'
import { TERRAIN_LAYERS } from './const'
import type { ExplorerSelectableLayer, ExplorerState } from './slice'

const select = (state: RootState) => state.explorer

const createExplorerSelector = <T>(
  callback: (state: ReturnType<typeof select>) => T
) => createSelector(select, callback)

const showSelectedTooltipsByLayer =
  (layer: ExplorerSelectableLayer) => (state: ExplorerState) =>
    state.showSelectedTooltips.includes(layer)

export const getCenter = createExplorerSelector((state) => state.center)

export const getCenterString = createExplorerSelector((state) => {
  const { lat, lng } = state.center
  return `${lng},${lat}`
})

export const getCenterTuple = createExplorerSelector((state) => {
  const { lat, lng } = state.center
  return [lng, lat] as LatLngTuple
})

export const getBounds = createExplorerSelector((state) => {
  const bounds = chunk(
    state.boundsString.split(',').map((coord) => Number(coord)),
    2
  ).map((chunk) => chunk.reverse())
  if (!bounds.every(isLatLngTuple)) return undefined
  return new LatLngBounds(bounds)
})

export const getBoundsString = createExplorerSelector(
  (state) => state.boundsString
)

export const getBoundsView = createExplorerSelector((state) => ({
  bounds: state.boundsString,
  zoom: state.zoom,
}))

export const getExportActive = createExplorerSelector(
  (state) => state.showExportPreview
)

export const getExportPageFormat = createExplorerSelector(
  (state) => state.exportPageFormat
)

export const getExportPageOrientation = createExplorerSelector(
  (state) => state.exportPageOrientation
)

export const getExportPageIsLandscape = createExplorerSelector(
  (state) => state.exportPageOrientation === 'landscape'
)

export const getExportState = (state: RootState) => ({
  active: getExportActive(state),
  pageFormat: getExportPageFormat(state),
  pageOrientation: getExportPageOrientation(state),
  isLandscape: getExportPageIsLandscape(state),
})

export const getLoading = createExplorerSelector(
  (state) => state.layersLoading > 0
)

export const getMenu = createExplorerSelector((state) => {
  return {
    id: state.menuFeatureId,
    position: state.menuPosition,
    type: state.menuType,
  }
})

export const getMenuPoint = createExplorerSelector((state) => {
  const { x, y } = state.menuPosition
  return new Point(x, y)
})

export const getShowSelectedTooltip = (layer: ExplorerSelectableLayer) =>
  createExplorerSelector(showSelectedTooltipsByLayer(layer))

export const getShowSelectedAddressTooltip = createExplorerSelector(
  showSelectedTooltipsByLayer('address')
)

export const getShowWhiteScale = createExplorerSelector((state) =>
  ['ESRI Satellite', 'LINZ Satellite'].includes(state.baseLayer)
)

export const getTitleMinSqM = createExplorerSelector(
  (state) => state.titleMinSqM
)

export const getTitleMode = createExplorerSelector((state) => state.titleMode)

export const getTitlePolygonIsGrouped = createExplorerSelector(
  (state) => state.titlePolygonIsGrouped
)

export const getTitleTooltipSize = createExplorerSelector(
  (state) => state.titleTooltipSize
)

export const getTitleShowMortgageeShading = createExplorerSelector(
  (state) => state.titleShowMortgageeShading
)

export const getViewportKey = createExplorerSelector((state) => {
  return `${state.center.lat}${state.center.lng}${state.zoom}`
})

export const getZoom = createExplorerSelector((state) => state.zoom)

export const getLayerOrder = createExplorerSelector((state): ExplorerLayer[] =>
  // TODO: type shenanigans here
  state.layerOrder.filter((layer: ExplorerLayer) =>
    EXPLORER_LAYERS.includes(layer)
  )
)

export const isTerrainLayer = createExplorerSelector((state) =>
  TERRAIN_LAYERS.includes(state.baseLayer)
)
