import { Descriptions, Table } from 'antd'
import React from 'react'
import type { TitleMemorial } from '../../../../../types'
import { Widget } from '../../../../generic/Widget'

interface ValuationPageMemorialWidgetProps {
  selectedMemorials: TitleMemorial[]
}

export const ValuationPageMemorialWidget = (
  props: ValuationPageMemorialWidgetProps
) => {
  const { selectedMemorials } = props

  return (
    <div>
      <h2>Memorials</h2>
      <div className="agrigis-table">
        <Table
          rowKey={(row) => row.id}
          size="small"
          dataSource={selectedMemorials}
          columns={[
            { dataIndex: 'titleNo', title: 'Number' },
            { dataIndex: 'instrumentNumber', title: 'ID' },
            { dataIndex: 'instrumentType', title: 'Type' },
            {
              dataIndex: 'instrumentLodgedDatetime',
              title: 'Lodged',
              render: (text: string) => {
                return text ? text.slice(0, 10) : ''
              },
            },
          ]}
          expandable={{
            expandedRowRender: (record) => (
              <p
                style={{ margin: 0, backgroundColor: 'white' }}
                className="agrigis-table-expandable-row"
              >
                <Descriptions
                  column={1}
                  bordered
                  className="agrigis-table-expanded-row"
                >
                  {record.memorialText ? (
                    <Descriptions.Item label="Description">
                      {record.memorialText}
                    </Descriptions.Item>
                  ) : (
                    <></>
                  )}
                  {record.encumbrancees ? (
                    <Descriptions.Item label="Encumbrancees">
                      {record.encumbrancees}
                    </Descriptions.Item>
                  ) : (
                    <></>
                  )}
                </Descriptions>
              </p>
            ),
          }}
        />
      </div>
    </div>
  )
}
