import { createSelector } from '@reduxjs/toolkit'
import { feature } from '@turf/turf'
import type {
  Feature,
  FeatureCollection,
  Geometry,
  MultiPolygon,
} from 'geojson'
import type { RootState } from '../..'
import type { BuildingOutline } from '../../../models/BuildingOutline'
import {
  ASSET_GEOMETRY_TYPES,
  ASSET_TYPES,
  type AssetType,
} from '../../../models/assets/Asset'
import type { AssetUpdate } from '../../../models/assets/AssetUpdate'
import type { Assets } from '../../../models/assets/Assets'
import type { ImprovementAsset } from '../../../models/assets/ImprovementAsset'
import {
  calculateApproxArea,
  createFeatureFromMap,
} from '../../../util/geoUtil'
import { type AssetsState, initialAssetsState } from './slice'

export const assetsState = (state: RootState): AssetsState => {
  return state.assets || initialAssetsState
}

export const geometryBeingEdited = createSelector(assetsState, (state) => {
  return state.ui.edits.featureUnderEdit.geoFeatureMap
})

export const getRemainingFeatureOriginal = createSelector(
  assetsState,
  (state) => {
    return state.ui.edits.featureUnderEdit.remainingFeatureOriginal
  }
)

export const geometryUserEditedAndDirty = createSelector(
  assetsState,
  (state) => {
    return (
      state.ui.edits.featureUnderEdit.userEdited &&
      state.ui.edits.featureUnderEdit.dirty
    )
  }
)

export function editedGeoFeature<T>(
  state: RootState,
  featureProperties: T,
  id?: string | number
) {
  const remainingFeatureOriginal = getRemainingFeatureOriginal(state)
  const assetType = state.assets.ui.edits.assetType
  if (
    (assetType === 'Land' && geometryUserEditedAndDirty(state)) ||
    assetType === 'Improvement' ||
    assetType === 'Planting'
  ) {
    return createFeatureFromMap(
      geometryBeingEdited(state),
      featureProperties,
      id
    )
  }
  if (remainingFeatureOriginal) {
    return {
      ...(remainingFeatureOriginal as Feature<MultiPolygon>),
      id,
      properties: featureProperties,
    }
  }
  return feature(undefined, featureProperties, { id })
}

export const addRemainingGeometry = createSelector(assetsState, (state) => {
  return state.ui.edits.addRemainingGeometry
})

export const featureUnderEdit = createSelector(assetsState, (state) => {
  return state.ui.edits.featureUnderEdit
})

export const geometryBeingEditedApproxArea = createSelector(
  assetsState,
  (state) => {
    return calculateApproxArea(state.ui.edits.featureUnderEdit.geoFeatureMap)
  }
)

export const validAssetGeometryTypes = createSelector(assetsState, (state) => {
  if (!state.ui || !state.ui.edits || state.ui.edits.assetType === undefined) {
    return []
  }

  return ASSET_GEOMETRY_TYPES[state.ui.edits.assetType]
})

export const editingAssetType = createSelector(assetsState, (state) => {
  if (!state.ui || !state.ui.edits.assetType) {
    return ASSET_TYPES[0]
  }

  return state.ui.edits.assetType
})

export const showAssetLayer = createSelector(assetsState, (state) => {
  return state.ui.assetLayer.show
})

export const assetLayerToggled = createSelector(
  (_: RootState, assetType: AssetType) => {
    return assetType
  },
  assetsState,
  (assetType, state: AssetsState) => {
    return (
      state.ui.assetLayer.show &&
      state.ui.assetLayer.selectedAssetType === assetType
    )
  }
)

export const selectedAssetType = createSelector(assetsState, (state) => {
  return state.ui.assetLayer.selectedAssetType
})

export const selectedAssetId = createSelector(assetsState, (state) => {
  return state.ui.assetLayer.selectedAssetId
})

export const toggledAssetField = createSelector(assetsState, (state) => {
  return state.ui.assetLayer.selectedAssetField
})

export const assetUnderEditId = createSelector(
  assetsState,
  (state: AssetsState) => {
    return state.ui.edits.assetUnderEditId
  }
)

export const assetEdits = createSelector(assetsState, (state) => {
  return state.ui.edits
})

export const assetsToDelete = createSelector(
  (_state: RootState, assetType: AssetType) => {
    return assetType
  },
  assetsState,
  (assetType, state: AssetsState) => {
    return (
      state.ui.edits.assetType === assetType
        ? state.ui.edits.changes.delete
        : {}
    ) as Record<string, boolean>
  }
)

export const assetListToDelete = createSelector(assetsToDelete, (toDelete) => {
  return Object.entries(toDelete)
    .filter(([, toDelete]) => toDelete)
    .map(([id]) => id)
})

export const selectedBuildingOutlines = createSelector(
  assetsState,
  (state: AssetsState) => {
    return state.ui.edits.selectedBuildingOutlines
  }
)

export const improvementAssetsWithBuildingOutlines = (
  state: RootState,
  _valuationId: string,
  buildingOutlines: FeatureCollection<Geometry, BuildingOutline> | undefined,
  assets: Assets | undefined
) => {
  const deletedAssets = assetsToDelete(state, 'Improvement')
  const improvementAssets =
    assets?.features?.Improvement?.features
      ?.filter((feature) => !deletedAssets[feature.id ?? '-1'])
      ?.map(
        (feature): Feature<Geometry, AssetUpdate<ImprovementAsset>> => ({
          ...feature,
          properties: {
            asset: feature.properties,
            geometry: feature.geometry,
            pseudoId: (feature.id ?? -1).toString(),
            editState: 'EXISTING',
            area: feature.properties.area ?? 0,
          },
        })
      ) ?? []
  const selectedOutlines = selectedBuildingOutlines(state)
  const usedFeatures = new Set([
    ...(improvementAssets
      .flatMap(
        (feature: Feature<Geometry, AssetUpdate<ImprovementAsset>>) =>
          feature.properties.asset.improvementBuildingOutlines.map(
            (buildingOutlineId: number) => buildingOutlineId.toString()
          ) ?? []
      )
      ?.filter((id) => id !== undefined) ?? []),
    ...selectedOutlines.map((selected) => selected.toString()),
  ])

  const uncoveredBuildingOutlines =
    buildingOutlines?.features
      ?.filter((feature: Feature<Geometry, BuildingOutline>) => {
        return (
          feature.id !== undefined && !usedFeatures.has(feature.id.toString())
        )
      })
      ?.map((feature: Feature<Geometry, BuildingOutline>) => {
        return {
          ...feature,
          properties: {
            area: feature.properties.area,
            geometry: feature.geometry,
            editState: 'NEW' as const,
            pseudoId: `new-${feature.id?.toString() ?? ''}`,
            asset: {
              assetType: 'Improvement',
              area: feature.properties.area,
              improvementBuildingOutlines: feature.id ? [feature.id] : [],
            } as Partial<ImprovementAsset>,
          } as AssetUpdate<ImprovementAsset>,
        }
      }) ?? []

  return {
    type: 'FeatureCollection' as const,
    features: [
      ...improvementAssets.map((assetFeature) => {
        return {
          ...assetFeature,
          properties: {
            ...assetFeature.properties,
          },
        }
      }),
      ...uncoveredBuildingOutlines,
    ],
  }
}

export const availableImprovementAssets = createSelector(
  (
    state: RootState,
    valuationId: string,
    buildingOutlines: FeatureCollection<Geometry, BuildingOutline> | undefined,
    assets: Assets | undefined
  ) =>
    improvementAssetsWithBuildingOutlines(
      state,
      valuationId,
      buildingOutlines,
      assets
    ),
  (featureGroup) => {
    return [
      ...featureGroup.features.map(
        (feature: Feature<Geometry, AssetUpdate<ImprovementAsset>>) => {
          return {
            ...feature.properties,
            geometry: feature.geometry,
          }
        }
      ),
    ]
  }
)

export const hasPendingChanges = createSelector(
  (_state: RootState, assetType: AssetType) => assetType,
  assetsState,
  (assetType: AssetType, state: AssetsState) => {
    // TODO: add geometryUnderEdit checks?
    return (
      assetType === state.ui.edits.assetType &&
      (Object.values(state.ui.edits.changes.delete).filter((x) => x).length >
        0 ||
        state.ui.edits.dirty)
    )
  }
)

export const isAssetBeingEditedFn = createSelector(
  (_state: RootState, assetType: AssetType) => assetType,
  assetsState,
  (assetType: AssetType, state: AssetsState) => {
    return (type: 'EXISTING' | 'NEW', id: string | number) => {
      return (
        state.ui.edits.assetType === assetType &&
        type === 'EXISTING' &&
        state.ui.edits.assetUnderEditId?.toString() === id?.toString()
      )
    }
  }
)

export const showCreateForm = createSelector(
  (_: RootState, assetType: AssetType) => assetType,
  assetsState,
  (assetType: AssetType, state) => {
    return state.ui.edits.assetType === assetType && state.ui.edits.showForm
  }
)

export const editedGeometryExists = createSelector(assetsState, (state) => {
  return (
    Object.entries(state.ui.edits.featureUnderEdit.geoFeatureMap).length > 0
  )
})

export const editedGeometryDirty = createSelector(assetsState, (state) => {
  return state.ui.edits.featureUnderEdit.dirty
})

export const showEditLayer = createSelector(assetsState, (state) => {
  return (
    state.ui.edits.dirty ||
    Object.keys(state.ui.edits.featureUnderEdit.geoFeatureMap).length > 0
  )
})

export const assetTypeBeingEdited = createSelector(
  assetsState,
  (rootState: RootState) =>
    hasPendingChanges(rootState, rootState.assets.ui.edits.assetType),
  (state, hasPendingChanges) => {
    return hasPendingChanges ? state.ui.edits.assetType : null
  }
)
