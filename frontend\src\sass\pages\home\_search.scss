.home-page-search-container-button {
  transition: none margin;
  margin-left: calc(100% - 84px - 2em);
  margin-bottom: -32px;

  &:not(.collapsed) {
    margin-top: -32px;
    margin-bottom: $full;
    margin-left: calc(100% - 46px);
  }
}

.home-page-search-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 3em 0;
  opacity: 1;
  background: linear-gradient(180deg, $primary 0%, $secondary);

  &.collapsed {
    opacity: 0;
    height: 0;
    padding: 0;
    margin-bottom: $half;

    * {
      display: none;
      padding: 0;
      margin: 0;
    }
  }

  .ant-select-selector {
    border-left: 10px solid #ddd !important;

    span,
    input {
      font-size: 12px;
    }
  }

  .home-page-search-container-widget {
    background-color: rgba(255, 255, 255, 0.95);
    max-width: 800px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 3em 1em;
    border-radius: 10px;
    box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.25);
    transition: none;
  }

  .home-page-searh-container-title {
    color: #004186;
    text-transform: uppercase;
    font-weight: 400;
    letter-spacing: 10%;
    font-size: 25px;
    margin: 0 0 10px 0;
    display: flex;

    > span {
      margin-left: 15px;
    }

    svg {
      width: 100% !important;
      height: 100% !important;
    }

    &.buttons {
      margin: 20px 0 10px 0;
    }
  }
}

.home-page-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;

  &.loans {
    justify-content: flex-start;
  }

  $homeButtonColor: #007dba;

  .home-page-button {
    display: flex;
    justify-content: space-between;
    background-color: tint($homeButtonColor, 75);
    padding: 1em 1.75em;
    transition: 0.3s box-shadow, 0.5s background-color;
    text-transform: uppercase;
    font-weight: bold;
    flex: 25%;
    margin: 4px;
    flex-grow: 0;
    font-size: 14px;
    max-width: 200px;
    color: #004168;

    &.active {
      background-color: #004168 !important;
      box-shadow: 0 !important;
      color: white;
    }

    svg {
      height: 21px !important;
      width: 21px !important;
      margin-right: 10px;
    }

    &:hover,
    &:active {
      cursor: pointer;
    }

    &:hover {
      background-color: tint($homeButtonColor, 90);
      box-shadow: 0 0 1px 1px tint($secondary, 50);
    }

    &:active {
      box-shadow: 0 0 2px 2px tint($secondary, 50);
    }
  }
}
