import {
  CheckOutlined,
  DeleteOutlined,
  <PERSON>Outlined,
  <PERSON>Outlined,
  SettingOutlined,
  SnippetsOutlined,
  SwapOutlined,
} from '@ant-design/icons'
import { Space, message } from 'antd'
import React, { useMemo, useState } from 'react'
import { ButtonWidget } from '@components/generic'
import { Widget } from '@components/generic'
import { useSelector } from '@store'
import {
  useLazyGetValuersQuery,
  useUpdateValuationMutation,
} from '@store/services/valuations'
import { isValuer } from '@store/ui/selectors'
import type { Valuation } from '../../../types'
import { DebounceSelect } from '../../generic/DebouncedSelect'
import { HoverButton } from '../../generic/HoverButton'
import { PromptModal } from '../../generic/PromptModal'
import ValuationClone from '../ValuationClone'

interface SelectedValuationControlsProps {
  valuation: Valuation
}

const deleteValuationMessage =
  'Deleting this valuation will result in a loss of data associated with it.'
const completeValuationMessage = `Completing this valuation will result in the valuation being 'locked' and placed in a read-only mode. You will not be able to make any further changes.`

export const SelectedValuationControls = (
  props: SelectedValuationControlsProps
) => {
  const { valuation } = props

  const _isValuer = useSelector(isValuer)

  const { addressId, valuationId, tradingGroupId } = valuation

  const [target, setTarget] = useState<
    { addressId: string; valuationId: string } | undefined
  >()
  const [targetValuer, setTargetValuer] = useState<string | undefined>()

  const [getValuers] = useLazyGetValuersQuery()

  const [updateValuation] = useUpdateValuationMutation()

  const [showDelete, setShowDelete] = useState(false)
  const handleCloseDelete = () => setShowDelete(false)
  const handleShowDelete = () => setShowDelete(true)

  const [showComplete, setShowComplete] = useState(false)
  const handleCloseComplete = () => setShowComplete(false)
  const handleShowComplete = () => setShowComplete(true)

  const [showTransfer, setShowTransfer] = useState(false)
  const handleCloseTransfer = () => setShowTransfer(false)
  const handleShowTransfer = () => setShowTransfer(true)

  const deleteValuation = async () => {
    if (target) {
      const { valuationId } = target
      try {
        await updateValuation({
          valuationId,
          valuation: {
            deletedDate: new Date().toISOString(),
          },
        }).unwrap()
        // TODO: do we need to clear search results / close the valuation on the RHS pane?
        setTarget(undefined)
        handleCloseDelete()
        void message.success('Valuation successfully deleted.')
      } catch (_) {
        void message.error(
          'Failed to delete valuation, unknown error occurred.'
        )
      }
    }
  }

  const completeValuation = async () => {
    if (target) {
      const { valuationId } = target
      try {
        await updateValuation({
          valuationId,
          valuation: {
            completedDate: new Date().toISOString(),
          },
        }).unwrap()
        setTarget(undefined)
        handleCloseComplete()
        void message.success('Valuation successfully completed.')
      } catch (_) {
        void message.error(
          'Failed to complete valuation, unknown error occurred.'
        )
      }
    }
  }

  const completeTransfer = async () => {
    if (!targetValuer) {
      void message.error('User not selected')
    }

    if (target && targetValuer) {
      const { valuationId } = target
      try {
        await updateValuation({
          valuationId,
          valuation: {
            creator: targetValuer,
            valuationId,
          },
        }).unwrap()
        setTarget(undefined)
        handleCloseTransfer()
        void message.success('Valuation successfully transfered.')
      } catch (_) {
        void message.error(
          'Failed to transfer valuation to new valuer, unknown error occurred.'
        )
      }
    }
  }

  const editControls = useMemo(() => {
    if (valuation) {
      return (
        <ButtonWidget>
          {valuation.valuationType === 'RVR' && valuation.rvrValuation ? (
            <HoverButton
              icon={<EyeOutlined />}
              type="primary"
              href={`/RVR/${valuation.rvrValuation.rvr}/valuations/${valuation.valuationId}`}
            >
              View
            </HoverButton>
          ) : (
            <HoverButton
              icon={<EyeOutlined />}
              type="primary"
              href={`/valuations/${valuationId}/`}
            >
              View
            </HoverButton>
          )}
          {valuation.valuationType === 'RVR' && valuation?.rvrValuation?.rvr ? (
            <HoverButton
              href={`/RVR/${valuation?.rvrValuation?.rvr}`}
              icon={<SnippetsOutlined />}
            >
              Open RVR
            </HoverButton>
          ) : null}
          {!tradingGroupId ? (
            <HoverButton
              icon={<LinkOutlined />}
              href={`/valuation/unlinked/${addressId}/${valuationId}/`}
            >
              Link
            </HoverButton>
          ) : (
            <></>
          )}
        </ButtonWidget>
      )
    }
  }, [valuation, tradingGroupId, addressId, valuationId])

  const approvalControls = _isValuer ? (
    <ButtonWidget>
      <HoverButton
        icon={<SwapOutlined />}
        onClick={() => {
          if (addressId && valuationId && !valuation?.completedDate) {
            setTarget({ addressId, valuationId })
            handleShowTransfer()
          }
        }}
      >
        Transfer
      </HoverButton>
      <HoverButton
        icon={<CheckOutlined />}
        className="approve-button"
        onClick={() => {
          if (addressId && valuationId && !valuation?.completedDate) {
            setTarget({ addressId, valuationId })
            handleShowComplete()
          }
        }}
      >
        Complete
      </HoverButton>
      <HoverButton
        icon={<DeleteOutlined />}
        className="reject-button"
        onClick={() => {
          if (addressId && valuationId && !valuation?.completedDate) {
            setTarget({ addressId, valuationId })
            handleShowDelete()
          }
        }}
      >
        Delete Valuation
      </HoverButton>
    </ButtonWidget>
  ) : null

  async function lookupValuer(
    inputString: string
  ): Promise<{ label: string; value: string }[]> {
    if (inputString === '') {
      return []
    }

    const response = await getValuers(inputString)
    if (response.data) {
      return response.data.map(({ username, id }) => ({
        label: username,
        value: id.toString(),
      }))
    }
    return []
  }

  const transferValuationMessage = (
    <div style={{ width: '100%' }}>
      <DebounceSelect
        style={{ width: '100%' }}
        fetchOptions={lookupValuer}
        // TODO: Investigate what's going on here
        // biome-ignore lint:
        onChange={setTargetValuer as any}
      />
    </div>
  )

  return (
    <Widget
      type="page-header"
      title={
        valuation?.valuationType === 'RVR'
          ? 'RVR Valuation Controls'
          : 'Valuation Controls'
      }
      icon={<SettingOutlined />}
      extra={
        <Space>
          {editControls}
          {addressId && valuationId && !valuation?.completedDate ? (
            approvalControls
          ) : (
            <></>
          )}
          {_isValuer ? (
            <ValuationClone addressId={addressId} valuationId={valuationId} />
          ) : null}
        </Space>
      }
    >
      <PromptModal
        show={showDelete}
        handleClose={handleCloseDelete}
        header="Confirm Valuation Deletion"
        body={deleteValuationMessage}
        handleConfirm={async () => {
          await deleteValuation()
        }}
      />
      <PromptModal
        show={showComplete}
        handleClose={handleCloseComplete}
        header="Confirm Valuation Completion"
        body={completeValuationMessage}
        handleConfirm={async () => {
          await completeValuation()
        }}
      />
      <PromptModal
        show={showTransfer}
        handleClose={handleCloseTransfer}
        header="Transfer Valuation Owner"
        body={transferValuationMessage}
        handleConfirm={async () => {
          await completeTransfer()
        }}
      />
    </Widget>
  )
}
