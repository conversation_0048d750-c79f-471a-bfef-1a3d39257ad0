import { EyeFilled, EyeOutlined } from '@ant-design/icons'
import { Button, Table, Tooltip } from 'antd'
import React, { useMemo } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import { useGetAddressServiceCentreDistancesQuery } from '@store/services/address'
import type { RootState } from '../../../../../store'
import {
  addressesActions,
  addressesSelectors,
} from '../../../../../store/features/address'
import type {
  Address,
  AddressServiceCentre,
  GeoFeature,
} from '../../../../../types'
import type { ColumnsType } from 'antd/lib/table'

interface ValuationPageServiceCentreProps {
  address: Address
}

const selector = (state: RootState, props: ValuationPageServiceCentreProps) => {
  return {
    addressId: props.address.addressId,
    address: props.address,
    distancePanelState: addressesSelectors.getPanelState(state, 'distances'),
  }
}

const createMapsLink = (
  address: Address,
  feature: GeoFeature<AddressServiceCentre>
) => {
  return `https://www.google.com/maps/dir/?api=1&origin=${encodeURIComponent(
    address.address
  )}&destination=${encodeURIComponent(feature.properties.name)}`
}

export const ValuationPageServiceCentresWidget = (
  props: ValuationPageServiceCentreProps
) => {
  const dispatch = useDispatch()

  const { addressId, distancePanelState } = useSelector(
    (state: RootState) => selector(state, props),
    shallowEqual
  )

  const { data: serviceCentres } =
    useGetAddressServiceCentreDistancesQuery(addressId)

  const serviceCentresArray = useMemo(() => {
    let servCentres: GeoFeature<AddressServiceCentre>[] = []
    if (serviceCentres?.features === undefined) {
      return servCentres
    }
    servCentres = [...serviceCentres.features]

    servCentres.sort(
      (
        a: GeoFeature<AddressServiceCentre>,
        b: GeoFeature<AddressServiceCentre>
      ) => {
        if (a.properties.distance <= b.properties.distance) {
          return -1
        }
        return 1
      }
    )

    return servCentres
  }, [serviceCentres])

  const toggleVisiblityButton = useMemo(() => {
    return (
      <Tooltip title="View">
        <Button
          onClick={() =>
            dispatch(
              addressesActions.togglePanelLayerVisibility({
                panel: 'distances',
              })
            )
          }
          shape="circle"
          size="small"
          type={!distancePanelState.layerVisible ? 'ghost' : 'primary'}
          icon={
            !distancePanelState.layerVisible ? <EyeOutlined /> : <EyeFilled />
          }
        />
      </Tooltip>
    )
  }, [distancePanelState, dispatch])

  const columns: ColumnsType<(typeof serviceCentresArray)[number]> =
    useMemo(() => {
      return [
        { dataIndex: ['properties', 'name'], title: 'Town/City' },
        {
          dataIndex: ['properties', 'generalized_classification'],
          title: 'Classification',
        },
        {
          dataIndex: ['properties', 'distance'],
          title: 'Straight Line Distance',
          render: (value: number) => `${(value / 1000).toFixed(2)} km`,
        },
        {
          dataIndex: '',
          title: 'Google Maps',
          render: (feature: GeoFeature<AddressServiceCentre>) => (
            <a
              target="_blank"
              rel="noopener noreferrer"
              href={createMapsLink(props.address, feature)}
            >
              Directions
            </a>
          ),
        },
      ]
    }, [props.address])

  return (
    <div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'baseline',
        }}
      >
        <h2>Service Centre Distances</h2>
        {toggleVisiblityButton}
      </div>
      <div className="agrigis-table">
        <Table
          size="small"
          pagination={false}
          columns={columns}
          rowKey={(row) => row.id || '-1'}
          dataSource={serviceCentresArray}
          scroll={{
            x: 'min-content',
          }}
        />
      </div>
    </div>
  )
}
