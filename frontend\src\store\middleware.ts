import type { AnyAction } from '@reduxjs/toolkit'
import explorer from './features/explorer'
import riskRadar from './features/riskRadar'
import { userApi } from './services/user'
import type { AppMiddleware } from './types'
import { setSavedSettings } from './ui/actions'
import { storeSliceState } from './util'
import sdk from './services/sdk'

const storeExplorerState = storeSliceState(explorer)
const storeRiskRadarState = storeSliceState(riskRadar)

export const storeState: AppMiddleware =
  (api) => (next) => (action: AnyAction) => {
    storeExplorerState(api, action, {
      layersLoading: undefined,
    })
    storeRiskRadarState(api, action, {
      layersLoading: undefined,
    })
    return next(action)
  }

export const uiSetSavedSettings: AppMiddleware =
  (api) => (next) => (action: AnyAction) => {
    if (
      typeof action.type === 'string' &&
      action.type.startsWith('ui/') &&
      ![
        'ui/setContextMenuActive',
        'ui/setSavedSettings',
        'ui/setUser',
      ].includes(action.type)
    ) {
      const result = next(action)
      api.dispatch(setSavedSettings({ state: api.getState().ui.saved }))
      return result
    }
    return next(action)
  }

export const uiSaveSettingsToUser: AppMiddleware =
  (api) => (next) => (action) => {
    if (action.type === 'ui/setSavedSettings') {
      void api.dispatch(
        sdk.endpoints.userUserSettingsUpdate.initiate({
          userSettings: {
            settings: action.payload.state,
          },
        })
      )
    }

    next(action)
  }
