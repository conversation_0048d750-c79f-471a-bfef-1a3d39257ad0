import React, { useEffect, useState } from 'react'
import { FeatureGroup, useLeaflet } from 'react-leaflet'
import { useDispatch, useSelector } from 'react-redux'
import type { RootState } from '@store'
import { useGetSelectedComparableSale } from '@store/hooks/useGetSelectedComparableSale'
import { useGetComparableSalesQuery } from '@store/services/assets'
import type { Sale } from '@store/services/sdk'
import { uiActions, uiSelectors } from '@store/ui'
import type { ComparableSale } from '@models/assets/ComparableSale'
import { AssetMapLayer } from '../../../../assets/AssetMapLayer'
import { PhysicalPropertiesLayer } from '../../../../physicalProperties/PhysicalPropertiesLayer'
import { SaleMarker } from './SaleMarker'

interface ComparableSalesLayerProps {
  addressId?: string
  valuationId: string
}

export const ComparableSalesLayer = (props: ComparableSalesLayerProps) => {
  const { valuationId } = props

  const salesAndListingLayerVisibility = useSelector((state: RootState) =>
    uiSelectors.getSalesAndListingsLayerVisibility(state)
  )
  const salesAndListingsCurrentTab = useSelector((state: RootState) =>
    uiSelectors.getSalesAndListingsCurrentTab(state)
  )

  if (!salesAndListingLayerVisibility) {
    return <></>
  }

  return (
    <>
      {salesAndListingsCurrentTab === 'SALES' ? (
        <ComparableSaleSearchLayer />
      ) : null}
      {salesAndListingsCurrentTab === 'COMPARISON' ? (
        <ComparableSalesPhysicalPropertiesLayer
          valuationId={valuationId}
          fillOpacity={0.8}
        />
      ) : null}
    </>
  )
}

/**
 * @deprecated
 */
export const ComparableSaleSearchLayer = () => {
  const leaflet = useLeaflet()

  // biome-ignore lint:
  const [selectedSalePaneRef, setSelectedSalePaneRef] = useState<any>()

  const searchSales = useSelector((state: RootState) =>
    uiSelectors.getSaleEditState(state)
  )
  const selectedSale = useSelector((state: RootState) =>
    uiSelectors.getSelectedSale(state)
  )

  useEffect(() => {
    if (selectedSale && selectedSalePaneRef) {
      const newBounds = selectedSalePaneRef?.leafletElement?.getBounds?.()
      if (newBounds && Object.entries(newBounds).length > 0) {
        console.info(newBounds)
        leaflet?.map?.fitBounds(newBounds)
      }
    } else if (searchSales) {
      // biome-ignore lint:
      const newBounds = (leaflet?.layerContainer as any)?.getBounds?.()
      if (newBounds && Object.entries(newBounds).length > 0) {
        leaflet?.map?.fitBounds(newBounds)
      }
    }
  }, [leaflet, leaflet?.map, searchSales, selectedSale, selectedSalePaneRef])

  const dispatch = useDispatch()

  if (!searchSales) {
    return null
  }

  return (
    <React.Fragment>
      {Object.entries(searchSales).map(([key, value], index) => (
        <SaleMarker
          key={key}
          sale={value.sale}
          state={value.state}
          index={index}
          onClick={(sale: Sale) =>
            dispatch(
              uiActions.setSelectedSale({
                sale,
                saleRowIndex: index,
              })
            )
          }
        />
      ))}
      {selectedSale ? (
        <FeatureGroup ref={setSelectedSalePaneRef}>
          <PhysicalPropertiesLayer
            key={selectedSale.properties.valuation}
            valuationId={selectedSale.properties.valuation.toString()}
            fillOpacity={0.8}
          />
        </FeatureGroup>
      ) : null}
    </React.Fragment>
  )
}

const ComparableSalesPhysicalPropertiesLayer = ({
  valuationId,
  fillOpacity,
}: {
  valuationId: string
  fillOpacity: number
}) => {
  const selectedComparableSaleId = useSelector((state: RootState) =>
    uiSelectors.getSelectedComparableSaleId(state)
  )

  const { data: comparableSales } = useGetComparableSalesQuery(valuationId)

  const selectedComparableSale = useGetSelectedComparableSale(
    valuationId,
    selectedComparableSaleId
  )

  if (selectedComparableSale) {
    return (
      <>
        <PhysicalPropertiesLayer
          key={selectedComparableSale.id}
          valuationId={selectedComparableSale.linkedSale.valuation.toString()}
          fillOpacity={fillOpacity}
        />
        <AssetMapLayer
          valuationId={selectedComparableSale.linkedSale.valuation.toString()}
          fillOpacity={fillOpacity}
        />
      </>
    )
  }
  return (
    <>
      {comparableSales?.map((comparableSale: ComparableSale) => (
        <PhysicalPropertiesLayer
          key={comparableSale.id}
          valuationId={comparableSale.linkedSale.valuation.toString()}
          fillOpacity={fillOpacity}
        />
      ))}
    </>
  )
}
