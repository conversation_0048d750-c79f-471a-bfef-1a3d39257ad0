import { Feature, Point } from 'geojson'
import { ViewportListingProperties } from '@models/listings/Listing'
import { Address } from '../../../models/address/Address'
import { AddressNeighbourFeatureCollection } from '../../../models/address/AddressNeighbour'
import { AnzUnionFeatureCollection } from '../../../models/gis/AnzUnionFeatureCollection'
import { ElevationFeatureCollection } from '../../../models/gis/ElevationFeatureCollection'
import { SmapFamilyFeatureCollection } from '../../../models/gis/SmapFamilyFeatureCollection'
import { TitleFeatureCollection } from '../../../models/title/TitleFeatureCollection'
import { baseApi } from '../baseApi'

export const valocitySaleAPi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    getValocityListing: build.query<
      Feature<Point, ViewportListingProperties>,
      string
    >({
      query: (valocityListingId) =>
        `/api/valocity_listings/${valocityListingId}/`,
      providesTags: (result, error, args) => [
        { type: 'ValocityListing', id: args },
      ],
    }),
    getValocitySaleTitles: build.query<TitleFeatureCollection, string>({
      query: (saleId) => `/api/valocity_sales/${saleId}/titles/`,
      providesTags: (result, error, args) => [
        { type: 'ValocitySaleTitles', id: args },
      ],
    }),
    getValocitySaleAddress: build.query<Address, string>({
      query: (saleId) => `/api/valocity_sales/${saleId}/address/`,
      providesTags: (result, error, args) => [
        { type: 'ValocitySaleAddress', id: args },
      ],
    }),
    getValocitySaleElevation: build.query<ElevationFeatureCollection, string>({
      query: (saleId) => `/api/valocity_sales/${saleId}/elevation/`,
      providesTags: (result, error, args) => [
        { type: 'ValocitySaleElevation', id: args },
      ],
    }),
    getValocitySaleUnion: build.query<AnzUnionFeatureCollection, string>({
      query: (saleId) => `/api/valocity_sales/${saleId}/union/`,
      providesTags: (result, error, args) => [
        { type: 'ValocitySaleUnion', id: args },
      ],
    }),
    getValocitySaleSmapFamily: build.query<SmapFamilyFeatureCollection, string>(
      {
        query: (saleId) => `/api/valocity_sales/${saleId}/smap_family/`,
        providesTags: (result, error, args) => [
          { type: 'ValocitySaleSmapFamily', id: args },
        ],
      }
    ),
    getValocitySaleNeighbours: build.query<
      AddressNeighbourFeatureCollection,
      string
    >({
      query: (saleId) => `/api/valocity_sales/${saleId}/neighbours/`,
      providesTags: (result, error, args) => [
        { type: 'ValocitySaleNeighbours', id: args },
      ],
    }),
  }),
})

export const {
  useGetValocitySaleTitlesQuery,
  useGetValocitySaleAddressQuery,
  useGetValocitySaleElevationQuery,
  useGetValocitySaleUnionQuery,
  useGetValocitySaleSmapFamilyQuery,
  useGetValocitySaleNeighboursQuery,
  useGetValocityListingQuery,
} = valocitySaleAPi
