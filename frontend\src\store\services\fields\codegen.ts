import { baseApi as api } from "../baseApi";
export const addTagTypes = ["fields"] as const;
const injectedRtkApi = api
  .enhanceEndpoints({
    addTagTypes,
  })
  .injectEndpoints({
    endpoints: (build) => ({
      fieldsSchemaEsstList: build.query<
        FieldsSchemaEsstListApiResponse,
        FieldsSchemaEsstListApiArg
      >({
        query: () => ({ url: `/api/fields/schema/esst/` }),
        providesTags: ["fields"],
      }),
      fieldsSchemaEsstCreate: build.mutation<
        FieldsSchemaEsstCreateApiResponse,
        FieldsSchemaEsstCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/fields/schema/esst/`,
          method: "POST",
          body: queryArg.fieldSchema,
        }),
        invalidatesTags: ["fields"],
      }),
      fieldsSchemaEsstRetrieve: build.query<
        FieldsSchemaEsstRetrieveApiResponse,
        FieldsSchemaEsstRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/fields/schema/esst/${queryArg.pk}/`,
        }),
        providesTags: ["fields"],
      }),
      fieldsSchemaEsstUpdate: build.mutation<
        FieldsSchemaEsstUpdateApiResponse,
        FieldsSchemaEsstUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/fields/schema/esst/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.fieldSchema,
        }),
        invalidatesTags: ["fields"],
      }),
      fieldsSchemaEsstPartialUpdate: build.mutation<
        FieldsSchemaEsstPartialUpdateApiResponse,
        FieldsSchemaEsstPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/fields/schema/esst/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedFieldSchema,
        }),
        invalidatesTags: ["fields"],
      }),
      fieldsSchemaEsstDestroy: build.mutation<
        FieldsSchemaEsstDestroyApiResponse,
        FieldsSchemaEsstDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/fields/schema/esst/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["fields"],
      }),
      fieldsSchemaEsstCurrentRetrieve: build.query<
        FieldsSchemaEsstCurrentRetrieveApiResponse,
        FieldsSchemaEsstCurrentRetrieveApiArg
      >({
        query: () => ({ url: `/api/fields/schema/esst/current/` }),
        providesTags: ["fields"],
      }),
    }),
    overrideExisting: false,
  });
export { injectedRtkApi as enhancedApi };
export type FieldsSchemaEsstListApiResponse = /** status 200  */ FieldSchema[];
export type FieldsSchemaEsstListApiArg = void;
export type FieldsSchemaEsstCreateApiResponse = /** status 201  */ FieldSchema;
export type FieldsSchemaEsstCreateApiArg = {
  fieldSchema: FieldSchema;
};
export type FieldsSchemaEsstRetrieveApiResponse =
  /** status 200  */ FieldSchema;
export type FieldsSchemaEsstRetrieveApiArg = {
  /** A unique integer value identifying this field schema. */
  pk: number;
};
export type FieldsSchemaEsstUpdateApiResponse = /** status 200  */ FieldSchema;
export type FieldsSchemaEsstUpdateApiArg = {
  /** A unique integer value identifying this field schema. */
  pk: number;
  fieldSchema: FieldSchema;
};
export type FieldsSchemaEsstPartialUpdateApiResponse =
  /** status 200  */ FieldSchema;
export type FieldsSchemaEsstPartialUpdateApiArg = {
  /** A unique integer value identifying this field schema. */
  pk: number;
  patchedFieldSchema: PatchedFieldSchema;
};
export type FieldsSchemaEsstDestroyApiResponse = unknown;
export type FieldsSchemaEsstDestroyApiArg = {
  /** A unique integer value identifying this field schema. */
  pk: number;
};
export type FieldsSchemaEsstCurrentRetrieveApiResponse =
  /** status 200  */ FieldSchema;
export type FieldsSchemaEsstCurrentRetrieveApiArg = void;
export type FieldNode = {
  id: number;
  fieldKey: string;
  fieldType:
    | "DOCUMENT"
    | "SECTION"
    | "STRING"
    | "TEXT"
    | "BOOLEAN"
    | "RADIO"
    | "SELECT"
    | "SELECT_MANY"
    | "ANY_SELECT"
    | "DATE";
  description: string;
  value: string;
  ancestorId: number | null;
  descendants: FieldNode[];
  metadata?: {
    [key: string]: any;
  };
};
export type FieldSchema = {
  id: number;
  root: FieldNode;
  createdAt: string;
  updatedAt: string;
  key: string;
  current?: boolean;
  valid?: boolean;
};
export type PatchedFieldSchema = {
  id?: number;
  root?: FieldNode;
  createdAt?: string;
  updatedAt?: string;
  key?: string;
  current?: boolean;
  valid?: boolean;
};
export const {
  useFieldsSchemaEsstListQuery,
  useFieldsSchemaEsstCreateMutation,
  useFieldsSchemaEsstRetrieveQuery,
  useFieldsSchemaEsstUpdateMutation,
  useFieldsSchemaEsstPartialUpdateMutation,
  useFieldsSchemaEsstDestroyMutation,
  useFieldsSchemaEsstCurrentRetrieveQuery,
} = injectedRtkApi;
