/*
.project-page {
    @extend .agrigis-page;

    overflow-y: scroll;

    .pane {
        flex: 1 1 0;
        padding: 10px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .pane.collapsed {
        display: none;
    }

    .pane-toggle-button {
        padding: 0px;
    }
}
*/

.project-page {
  @extend .agrigis-page;

  .agrigis-pane-left {
    max-width: 500px;

    &.focused {
      max-width: 0 !important;
      padding: 0 !important;
    }
  }

  &:before {
    transition: background-color 1s !important;
  }

  &.fetching {
    &:before {
      content: '';
      position: absolute;
      z-index: 10003;
      background-color: rgba(55, 55, 55, 0.5);
      width: 100vw;
      height: calc(100vh - 128px);
    }

    &:after {
      position: absolute;
      top: 10%;
      left: 40%;
      z-index: 10004;
      content: 'Computing scenario - this can take awhile depending how big your area of interest is...';
      background-color: white;
      padding: 1em;
      border-radius: 8px;
    }
  }

  .agrigis-pane {
    transition: visibility 0s, max-width 0.25s linear, min-width 0.25s linear;
  }

  .agrigis-pane-fill {
    display: flex;
    position: relative;

    &.not-focused {
      max-width: 0 !important;
      padding: 0 !important;
    }

    .agrigis-pane-fill-container {
      flex-direction: column;
      min-width: 33vw;

      &.none-selected {
        &:before {
          position: absolute;
          content: 'No loan or proposal is currently selected...';
          font-style: italic;
          color: #888;
          opacity: 1;
        }

        > .agrigis-widget,
        > .ant-steps {
          opacity: 0;
        }
      }
    }

    .agrigis-map {
      &.modal {
        height: 100% !important;
      }

      position: relative;
      height: calc(100vh - 128px);
      width: 100%;

      > .agrigis-map-overlay {
        bottom: 0;
        position: absolute;
        z-index: 9999;
        margin: 1em 1em 2em 1em;

        > .agrigis-widget {
          background-color: white;
          padding: 1em;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.15);
        }
      }
    }

    > .agrigis-pane.agrigis-pane-focused {
      display: none !important;
    }

    > .agrigis-pane-fill-container {
      display: block;
    }
  }

  &.focus-mode {
    .agrigis-pane.agrigis-pane-focused {
      display: block !important;
      min-width: 50vw;
      max-width: 50vw;
    }

    .agrigis-pane-left,
    .agrigis-pane-fill-container {
      visibility: none !important;
      opacity: 0 !important;
      max-width: 0px !important;
      min-width: 0px !important;
      padding: 0;
      margin: 0;

      * {
        display: none;
      }
    }
  }

  .covenant-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .ant-row {
      min-width: 180px;
      margin-left: 10px;
      margin-right: 10px;
      flex-grow: 1;
      flex-shrink: 1;
    }
  }

  .covenant-due-soon {
    font-weight: 500;
    color: orange;
  }

  .convenant-overdue {
    font-weight: bold;
    color: red;
  }

  .agrigis-pane-fill-container {
    .ant-steps-icon {
      width: 100% !important;
      height: 100% !important;
      margin-left: 16px;

      svg {
        margin-top: -22px !important;
        height: 16px;
        width: 16px;
      }
    }

    .ant-steps {
      padding-bottom: 8px !important;
    }
  }
}

.agrigis-stats {
  display: flex;
  flex-wrap: wrap;

  > div {
    flex: 25%;
  }
}
