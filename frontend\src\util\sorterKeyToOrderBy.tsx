import type { SorterResult } from 'antd/lib/table/interface'

export const sorterKeyToOrderBy = <T,>(
  sorter: SorterResult<T>
): string | undefined => {
  // assumes each sortable column has a `key` corresponding to the column name supported by the endpoints `orderBy` input
  if (sorter.columnKey && sorter.order) {
    return `${sorter.order === 'ascend' ? '' : '-'}${sorter.columnKey}`
  }
  return undefined
}
