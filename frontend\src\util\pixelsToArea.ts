// 1cm on screen = xm2
const ZOOM_TO_SCALE: Record<number, number> = {
  0: 591657527.591555,
  1: 295828763.795777,
  2: 147914381.897889,
  3: 73957190.948944,
  4: 36978595.474472,
  5: 18489297.737236,
  6: 9244648.868618,
  7: 4622324.434309,
  8: 2311162.217155,
  9: 1155581.108577,
  10: 577790.554289,
  11: 288895.277144,
  12: 144447.638572,
  13: 72223.819286,
  14: 36111.909643,
  15: 18055.954822,
  16: 9027.977411,
  17: 4513.988705,
  18: 2256.994353,
  19: 1128.497176,
  20: 564.248588,
  21: 282.124294,
  22: 141.062147,
  23: 70.5310735,
}

const CM_TO_PIXELS = 37.7952755906

export const pixelsToArea = (
  width: number | undefined,
  height: number | undefined,
  zoom: number | undefined
): number => {
  const scaleFactor = ZOOM_TO_SCALE[zoom ?? 0]
  const area = ((width ?? 0) * (height ?? 0)) / CM_TO_PIXELS
  return scaleFactor * area
}

export const areaToPixels = (
  area: number | undefined,
  zoom: number | undefined
): number => {
  const scaleFactor = ZOOM_TO_SCALE[zoom ?? 0]
  const edge = ((area ?? 0) / scaleFactor) * CM_TO_PIXELS
  return edge * edge
}
