export interface CarbonProjectionAssetDataset {
  assetId: string
  speciesName: string
  speciesId: string
  totalStock: number[]
}

// totalStock is of the form of <current_year>:<current_Year+len(stock_per_year)> ([currentYear, currentYear + 1, ..., currentYear + len(stock_per_year)])
export interface CarbonProjectionData {
  perAsset: CarbonProjectionAssetDataset[]
  totalStock: number[]
  labels: string[]
}
