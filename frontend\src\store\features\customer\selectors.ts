import { createSelector } from '@reduxjs/toolkit'
import type { RootState } from '@store/types'
import {
  initialBalancesState,
  initialBenchmarkState,
  initialCovenantState,
  initialEmissionState,
  initialFinancialsState,
  initialReportState,
} from './slice'

const select = (state: RootState) => state.customer

const selectCustomerId = <T>(_: T, customerId: number) => customerId

const createCustomerSelector = <T>(
  callback: (state: ReturnType<typeof select>) => T
) => createSelector(select, callback)

export const getCustomerDrawerVisible = createCustomerSelector(
  (state) => state.drawer.visible ?? false
)

export const getCustomerDrawerMode = createCustomerSelector(
  (state) => state.drawer.mode ?? 'data'
)

export const getCustomerPageTab = createCustomerSelector((state) => state.tab)

export const getCustomerState = createCustomerSelector((state) => state)

export const getCustomerBenchmarkingState = (
  state: RootState,
  customerId: number
) => {
  return state.customer.benchmarking?.[customerId] ?? initialBenchmarkState
}

export const getCustomerCovenantState = (
  state: RootState,
  customerId: number
) => {
  return state.customer.covenant?.[customerId] ?? initialCovenantState
}

export const getCustomerEmissionsState = (
  state: RootState,
  customerId: number
) => {
  return state.customer.emissions?.[customerId] ?? initialEmissionState
}

export const getCustomerFinancialsState = createSelector(
  [select, selectCustomerId],
  (state, customerId: number) =>
    state.financials?.[customerId] ?? initialFinancialsState
)

// export const getCustomerFinancialsDataType = createSelector(
//   [select, selectCustomerId],
//   (state, customerId: number) =>
//     state.report?.[customerId] ?? initialReportState
// )

export const getCustomerBalancesState = (
  state: RootState,
  customerId: number
) => {
  return state.customer.balances?.[customerId] ?? initialBalancesState
}

export const getCustomerReportState = (
  state: RootState,
  customerId: number
) => {
  return state.customer.report?.[customerId] ?? initialReportState
}

export const getCustomerReportStateById = createSelector(
  [select, selectCustomerId],
  (state, customerId: number) =>
    state.report?.[customerId] ?? initialReportState
)
