import { useCallback, useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { uiSelectors } from '@store/ui'
import type { SelectionBookmark } from '@store/ui/types'
import type { RootState } from '../store'
import {
  addBookmark,
  setSelectedAddressIds,
  setSelectedBookmarkId,
  setSelectedListingIds,
  setSelectedSaleIds,
  setSelectedValocityListingIds,
  setSelectedValocitySaleIds,
  updateBookmark,
} from '../store/ui/actions'

function selector(state: RootState) {
  return {
    bookmarks: state.ui.saved.map?.bookmarks || [],
    selectedBookmarkId: state.ui.saved.map?.selectedBookmarkId || null,
    selectedItems: {
      addressIds: uiSelectors.getSelectedAddressIds(state),
      listingIds: uiSelectors.getSelectedListingIds(state),
      saleIds: uiSelectors.getSelectedSaleIds(state),
      valocitySaleIds: uiSelectors.getSelectedValocitySaleIds(state),
      valocityListingIds: uiSelectors.getSelectedValocityListingIds(state),
    },
  }
}

export function useBookmarks() {
  const dispatch = useDispatch()

  const { bookmarks, selectedBookmarkId, selectedItems } = useSelector(selector)

  const selectedBookmark = useMemo(
    () => bookmarks.find((bookmark) => bookmark.id === selectedBookmarkId),
    [bookmarks, selectedBookmarkId]
  )

  const creatable = useMemo(
    () => !!Object.values(selectedItems).flat().length,
    [selectedItems]
  )

  const create = useCallback(() => {
    const id = Date.now()
    const name = new Date().toLocaleString('en-NZ', {
      dateStyle: 'short',
      timeStyle: 'short',
    })
    const bookmark = {
      id,
      name,
      ...selectedItems,
    }
    dispatch(addBookmark({ bookmark }))
    dispatch(setSelectedBookmarkId({ id: bookmark.id }))
  }, [dispatch, selectedItems])

  const updateSelected = useCallback(() => {
    if (!selectedBookmark) return
    const bookmark = { ...selectedBookmark, ...selectedItems }
    dispatch(updateBookmark({ bookmark }))
  }, [dispatch, selectedBookmark, selectedItems])

  const load = useCallback(
    ({
      id,
      addressIds,
      saleIds,
      listingIds,
      valocityListingIds,
      valocitySaleIds,
    }: SelectionBookmark) => {
      dispatch(setSelectedBookmarkId({ id }))
      dispatch(setSelectedAddressIds({ addressIds }))
      dispatch(setSelectedSaleIds({ saleIds }))
      dispatch(setSelectedListingIds({ listingIds }))
      dispatch(setSelectedValocityListingIds({ valocityListingIds }))
      dispatch(setSelectedValocitySaleIds({ valocitySaleIds }))
    },
    [dispatch]
  )

  return {
    bookmarks,
    creatable,
    create,
    load,
    selectedBookmark,
    updateSelected,
  }
}
