import { useCallback, useEffect, useState } from 'react'
import { useAppDispatch } from '@store'
import type { CustomerBalance } from '@store/services/sdk'
import sdk from '@store/services/sdk/enhanced'

// TODO: Make this a .ts file

const useCustomerBalances = (
  ids: { customerId: number; entityName: string }[]
) => {
  const [balances, setBalances] = useState<CustomerBalance[]>([])
  const [loading, setLoading] = useState<boolean>(false)
  const dispatch = useAppDispatch()

  const getCustomerBalances = useCallback(
    async (pk: number) => {
      setLoading(true)
      try {
        const { data } = await dispatch(
          sdk.endpoints.customerBalancesList.initiate({
            pk,
          })
        )
        return [...(data?.map((f) => ({ ...f })) ?? [])] as CustomerBalance[]
      } catch (error) {
        console.error(error)
      }
      return [] as CustomerBalance[]
    },
    [dispatch]
  )

  useEffect(() => {
    void (async () => {
      const aggBalances = (
        await Promise.all(
          ids.map(({ customerId }) => getCustomerBalances(customerId))
        )
      ).flat()
      setBalances(aggBalances)
      setLoading(false)
    })()
  }, [ids, getCustomerBalances])

  return {
    loading,
    balances,
  }
}

export default useCustomerBalances
