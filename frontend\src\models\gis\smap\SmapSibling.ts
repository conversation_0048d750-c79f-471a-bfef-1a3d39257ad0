import type { SmapSibling } from '@store/services/sdk'
import { isNonNullable } from '@util/guards'
import type { GeoFeature, GeoFeatureGroup } from '../../../types'
import type { LegendValue } from '../../../types/GISLayerDescriptors'
import { lerpColors } from '../../../util/colors'
import { smapDictionary } from '../../../variables/smapDictionary'
import type { SmapFeature } from './SmapFeature'

export type SiblingMap = Record<string, SmapSibling[]>

export const getDominantSiblingFeatures = (
  siblingMap: SiblingMap | undefined,
  smapFeatures: GeoFeatureGroup<SmapFeature> | undefined
) => {
  if (
    smapFeatures === undefined ||
    siblingMap === undefined ||
    Object.keys(siblingMap).length === 0
  ) {
    return undefined
  }

  const newFeatures = {
    ...smapFeatures,
    features: smapFeatures.features
      .map((feature: GeoFeature<SmapFeature>) => {
        const smu = feature.properties.smu
        if (siblingMap[smu] === undefined) {
          return undefined
        }

        const siblings = [...siblingMap[smu]]
        const dominantSibling = siblings.sort(
          (a: SmapSibling, b: SmapSibling) =>
            +(b.siblingproportion ?? 0) - +(a.siblingproportion ?? 0)
        )[0]

        if (dominantSibling === undefined) {
          return undefined
        }
        return {
          ...feature,
          properties: {
            ...dominantSibling,
            ...feature.properties,
            area: feature.properties.area,
          },
        }
      })
      .filter((feature) => feature !== undefined),
  } as GeoFeatureGroup<SmapSibling>

  return newFeatures
}

export const getCompleteSiblingLayerLegend = (
  siblingMap: SiblingMap | undefined,
  smapFeatures: GeoFeatureGroup<SmapFeature> | undefined,
  siblingField: keyof SmapSibling
) => {
  const DEFAULT_COLOR_RANGE = [
    '#d50000',
    '#f9ff06',
    '#c000ff',
    '#0207ff',
  ].reverse()

  if (siblingMap === undefined || smapFeatures === undefined) {
    return undefined
  }

  const smus = smapFeatures.features.map((feature) => feature.properties.smu)

  const siblings = smus.flatMap((smu: string) => siblingMap[smu])

  let fieldValues = [
    ...new Set(
      siblings
        .map((sibling: SmapSibling) => {
          return sibling[siblingField as keyof SmapSibling]
        })
        .filter(isNonNullable)
        .map(String)
    ),
  ].sort()

  if (fieldValues.indexOf('Unknown') !== -1) {
    fieldValues = fieldValues
      .filter((value) => value !== 'Unknown')
      .concat('Unknown')
  }

  const numValues = fieldValues.length
  const colorMap = fieldValues.reduce<Record<string, string>>(
    (accum, fieldValue, index) => {
      if (fieldValue !== undefined) {
        accum[fieldValue] = lerpColors(
          DEFAULT_COLOR_RANGE,
          index,
          0,
          numValues - 1
        )
      }
      return accum
    },
    {}
  )

  const legendEntries: { [key: string]: LegendValue } = fieldValues.reduce<
    Record<string, LegendValue>
  >((accum, fieldValue) => {
    const label = fieldValue
    accum[fieldValue] = {
      label,
      color: colorMap[fieldValue],
    }
    return accum
  }, {})

  return {
    legendEntries,
    getDescriptor: (fieldValue: string) => fieldValue,
    getColor: (fieldValue: string) => colorMap[fieldValue],
  }
}

export const getDominantSiblingLayerLegend = (
  siblingData: GeoFeatureGroup<SmapSibling> | undefined,
  siblingField: keyof SmapSibling
) => {
  const DEFAULT_COLOR_RANGE = [
    '#d50000',
    '#ba1c66',
    '#f9ff06',
    '#f7c42a',
    '#cf8ce6',
    '#0207ff',
  ].reverse()

  if (siblingData === undefined) {
    return undefined
  }

  let fieldValues = [
    ...new Set(
      siblingData.features
        .map((feature: GeoFeature<SmapSibling>) => {
          return feature.properties[siblingField as keyof SmapSibling]
        })
        .filter(isNonNullable)
        .map(String)
    ),
  ].sort()

  if (fieldValues.indexOf('Unknown') !== -1) {
    fieldValues = fieldValues
      .filter((value) => value !== 'Unknown')
      .concat('Unknown')
  }

  const numValues = fieldValues.length
  const colorMap = fieldValues.reduce<Record<string, string>>(
    (accum, fieldValue, index) => {
      accum[fieldValue] = lerpColors(
        DEFAULT_COLOR_RANGE,
        index,
        0,
        numValues - 1
      )
      return accum
    },
    {}
  )

  const legendEntries: Record<string, LegendValue> = fieldValues.reduce(
    (accum: Record<string, LegendValue>, fieldValue) => {
      const label = fieldValue.toString()
      accum[fieldValue] = {
        label,
        color: colorMap[fieldValue],
      }
      return accum
    },
    {}
  )

  return {
    name: 'Smap',
    legendEntries,
    hasGlossary: smapDictionary[siblingField] !== undefined,
    getGlossary: () => smapDictionary[siblingField],
    getDescriptor: (fieldValue: string) => fieldValue,
    getColor: (fieldValue: string) => colorMap[fieldValue],
  }
}

export const getSmuLayerLegend = (
  smapFeatures: GeoFeatureGroup<SmapFeature> | undefined,
  siblingMap: SiblingMap | undefined
) => {
  const DEFAULT_COLOR_RANGE = [
    '#d50000',
    '#ba1c66',
    '#f9ff06',
    '#f7c42a',
    '#cf8ce6',
    '#0207ff',
  ].reverse()

  if (smapFeatures === undefined || siblingMap === undefined) {
    return undefined
  }

  const fieldValues = [
    ...new Set(
      smapFeatures.features
        .map((feature: GeoFeature<SmapFeature>) => {
          return feature.properties.smu
        })
        .filter(isNonNullable)
        .map(String)
    ),
  ].sort()

  const numValues = fieldValues.length
  const colorMap = fieldValues.reduce(
    (accum: { [value: string]: string }, fieldValue: string, index: number) => {
      accum[fieldValue] = lerpColors(
        DEFAULT_COLOR_RANGE,
        index,
        0,
        numValues - 1
      )
      return accum
    },
    {}
  )

  const legendEntries: { [key: string]: LegendValue } = fieldValues.reduce(
    (accum: { [key: string]: LegendValue }, fieldValue: string) => {
      const label = fieldValue
      accum[fieldValue] = {
        label,
        color: colorMap[fieldValue],
      }
      return accum
    },
    {}
  )

  return {
    name: 'SmuLegend',
    legendEntries,
    getDescriptor: (fieldValue: string) => fieldValue,
    getColor: (fieldValue: string) => colorMap[fieldValue],
  }
}
