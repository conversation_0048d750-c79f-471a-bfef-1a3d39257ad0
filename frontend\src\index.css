@import "./var.css";
@import "./utility.css";

:root {
  --header-height: 64px;

  --explorer-pane-z-max: 1000;
  --explorer-pane-z-background: calc(var(--explorer-pane-z-max) * 0.2);
  --explorer-pane-z-foreground: calc(var(--explorer-pane-z-max) * 0.8);

  --export-primary-font-size: 14px;
  --export-header-font-size: 16px;
  --export-a4-width: calc(210mm + 30mm);
  --export-a4-height: calc(297mm + 30mm);
  --export-map-height: 20cm;

  --export-blue: #007dba;

  --horizon: rgba(198, 223, 234, 1);
  --horizon-10: rgba(198, 223, 234, 0.1);
  --horizon-20: rgba(198, 223, 234, 0.2);
  --horizon-40: rgba(198, 223, 234, 0.3);
  --horizon-30: rgba(198, 223, 234, 0.3);
  --horizon-50: rgba(198, 223, 234, 0.5);
  --horizon-60: rgba(198, 223, 234, 0.6);
  --horizon-70: rgba(198, 223, 234, 0.7);
  --horizon-80: rgba(198, 223, 234, 0.8);
  --horizon-90: rgba(198, 223, 234, 0.9);

  --sea-spray: rgba(185, 201, 208, 1);
  --sea-spray-10: rgba(185, 201, 208, 0.1);
  --sea-spray-20: rgba(185, 201, 208, 0.2);
  --sea-spray-40: rgba(185, 201, 208, 0.3);
  --sea-spray-30: rgba(185, 201, 208, 0.3);
  --sea-spray-50: rgba(185, 201, 208, 0.5);
  --sea-spray-60: rgba(185, 201, 208, 0.6);
  --sea-spray-70: rgba(185, 201, 208, 0.7);
  --sea-spray-80: rgba(185, 201, 208, 0.8);
  --sea-spray-90: rgba(185, 201, 208, 0.9);

  --mt-cook-river: rgba(185, 204, 195, 1);
  --mt-cook-river-10: rgba(185, 204, 195, 0.1);
  --mt-cook-river-20: rgba(185, 204, 195, 0.2);
  --mt-cook-river-40: rgba(185, 204, 195, 0.3);
  --mt-cook-river-30: rgba(185, 204, 195, 0.3);
  --mt-cook-river-50: rgba(185, 204, 195, 0.5);
  --mt-cook-river-60: rgba(185, 204, 195, 0.6);
  --mt-cook-river-70: rgba(185, 204, 195, 0.7);
  --mt-cook-river-80: rgba(185, 204, 195, 0.8);
  --mt-cook-river-90: rgba(185, 204, 195, 0.9);

  --spinifex: rgba(211, 205, 139, 1);
  --spinifex-10: rgba(211, 205, 139, 0.1);
  --spinifex-20: rgba(211, 205, 139, 0.2);
  --spinifex-40: rgba(211, 205, 139, 0.3);
  --spinifex-30: rgba(211, 205, 139, 0.3);
  --spinifex-50: rgba(211, 205, 139, 0.5);
  --spinifex-60: rgba(211, 205, 139, 0.6);
  --spinifex-70: rgba(211, 205, 139, 0.7);
  --spinifex-80: rgba(211, 205, 139, 0.8);
  --spinifex-90: rgba(211, 205, 139, 0.9);

  --driftwood: rgba(237, 232, 196, 1);
  --driftwood-10: rgba(237, 232, 196, 0.1);
  --driftwood-20: rgba(237, 232, 196, 0.2);
  --driftwood-40: rgba(237, 232, 196, 0.3);
  --driftwood-30: rgba(237, 232, 196, 0.3);
  --driftwood-50: rgba(237, 232, 196, 0.5);
  --driftwood-60: rgba(237, 232, 196, 0.6);
  --driftwood-70: rgba(237, 232, 196, 0.7);
  --driftwood-80: rgba(237, 232, 196, 0.8);
  --driftwood-90: rgba(237, 232, 196, 0.9);

  --deep-sea-spray: rgba(122, 153, 172, 1);
  --deep-sea-spray-10: rgba(122, 153, 172, 0.1);
  --deep-sea-spray-20: rgba(122, 153, 172, 0.2);
  --deep-sea-spray-40: rgba(122, 153, 172, 0.3);
  --deep-sea-spray-30: rgba(122, 153, 172, 0.3);
  --deep-sea-spray-50: rgba(122, 153, 172, 0.5);
  --deep-sea-spray-60: rgba(122, 153, 172, 0.6);
  --deep-sea-spray-70: rgba(122, 153, 172, 0.7);
  --deep-sea-spray-80: rgba(122, 153, 172, 0.8);
  --deep-sea-spray-90: rgba(122, 153, 172, 0.9);

  --sky: rgba(91, 198, 232, 1);
  --sky-10: rgba(91, 198, 232, 0.1);
  --sky-20: rgba(91, 198, 232, 0.2);
  --sky-40: rgba(91, 198, 232, 0.3);
  --sky-30: rgba(91, 198, 232, 0.3);
  --sky-50: rgba(91, 198, 232, 0.5);
  --sky-60: rgba(91, 198, 232, 0.6);
  --sky-70: rgba(91, 198, 232, 0.7);
  --sky-80: rgba(91, 198, 232, 0.8);
  --sky-90: rgba(91, 198, 232, 0.9);

  --stone: #394a58;
  --stone-10: rgba(57, 74, 88, 0.1);
  --stone-20: rgba(57, 74, 88, 0.2);
  --stone-40: rgba(57, 74, 88, 0.3);
  --stone-30: rgba(57, 74, 88, 0.3);
  --stone-50: rgba(57, 74, 88, 0.5);
  --stone-50: rgba(57, 74, 88, 0.6);
  --stone-70: rgba(57, 74, 88, 0.7);
  --stone-80: rgba(57, 74, 88, 0.8);
  --stone-90: rgba(57, 74, 88, 0.9);

  --anz-grey: rgba(116, 118, 120, 1);
  --anz-grey-10: rgba(116, 118, 120, 0.1);
  --anz-grey-20: rgba(116, 118, 120, 0.2);
  --anz-grey-40: rgba(116, 118, 120, 0.3);
  --anz-grey-30: rgba(116, 118, 120, 0.3);
  --anz-grey-50: rgba(116, 118, 120, 0.5);
  --anz-grey-60: rgba(116, 118, 120, 0.6);
  --anz-grey-70: rgba(116, 118, 120, 0.7);
  --anz-grey-80: rgba(116, 118, 120, 0.8);
  --anz-grey-90: rgba(116, 118, 120, 0.9);

  --rockpool: rgba(170, 156, 143, 1);
  --rockpool-10: rgba(170, 156, 143, 0.1);
  --rockpool-20: rgba(170, 156, 143, 0.2);
  --rockpool-40: rgba(170, 156, 143, 0.3);
  --rockpool-30: rgba(170, 156, 143, 0.3);
  --rockpool-50: rgba(170, 156, 143, 0.5);
  --rockpool-60: rgba(170, 156, 143, 0.6);
  --rockpool-70: rgba(170, 156, 143, 0.7);
  --rockpool-80: rgba(170, 156, 143, 0.8);
  --rockpool-90: rgba(170, 156, 143, 0.9);

  --west-coast-sunset: #df7a00;
  --west-coast-sunset-10: rgba(223, 122, 0, 0.1);
  --west-coast-sunset-20: rgba(223, 122, 0, 0.2);
  --west-coast-sunset-40: rgba(223, 122, 0, 0.3);
  --west-coast-sunset-30: rgba(223, 122, 0, 0.3);
  --west-coast-sunset-50: rgba(223, 122, 0, 0.5);
  --west-coast-sunset-60: rgba(223, 122, 0, 0.6);
  --west-coast-sunset-70: rgba(223, 122, 0, 0.7);
  --west-coast-sunset-80: rgba(223, 122, 0, 0.8);
  --west-coast-sunset-90: rgba(223, 122, 0, 0.9);

  --golden-shore: rgba(253, 200, 47, 1);
  --golden-shore-10: rgba(253, 200, 47, 0.1);
  --golden-shore-20: rgba(253, 200, 47, 0.2);
  --golden-shore-40: rgba(253, 200, 47, 0.3);
  --golden-shore-30: rgba(253, 200, 47, 0.3);
  --golden-shore-50: rgba(253, 200, 47, 0.5);
  --golden-shore-60: rgba(253, 200, 47, 0.6);
  --golden-shore-70: rgba(253, 200, 47, 0.7);
  --golden-shore-80: rgba(253, 200, 47, 0.8);
  --golden-shore-90: rgba(253, 200, 47, 0.9);

  --turquoise-sea: #00c6d7;
  --turquoise-sea-10: rgba(0, 198, 215, 0.1);
  --turquoise-sea-20: rgba(0, 198, 215, 0.2);
  --turquoise-sea-40: rgba(0, 198, 215, 0.3);
  --turquoise-sea-30: rgba(0, 198, 215, 0.3);
  --turquoise-sea-50: rgba(0, 198, 215, 0.5);
  --turquoise-sea-60: rgba(0, 198, 215, 0.6);
  --turquoise-sea-70: rgba(0, 198, 215, 0.7);
  --turquoise-sea-80: rgba(0, 198, 215, 0.8);
  --turquoise-sea-90: rgba(0, 198, 215, 0.9);

  --seagrass: rgba(88, 145, 153, 1);
  --seagrass-10: rgba(88, 145, 153, 0.1);
  --seagrass-20: rgba(88, 145, 153, 0.2);
  --seagrass-40: rgba(88, 145, 153, 0.3);
  --seagrass-30: rgba(88, 145, 153, 0.3);
  --seagrass-50: rgba(88, 145, 153, 0.5);
  --seagrass-60: rgba(88, 145, 153, 0.6);
  --seagrass-70: rgba(88, 145, 153, 0.7);
  --seagrass-80: rgba(88, 145, 153, 0.8);
  --seagrass-90: rgba(88, 145, 153, 0.9);

  --secondary: rgba(0, 125, 186, 1);
  --secondary-10: rgba(0, 125, 186, 0.1);
  --secondary-20: rgba(0, 125, 186, 0.2);
  --secondary-30: rgba(0, 125, 186, 0.3);
  --secondary-40: rgba(0, 125, 186, 0.3);
  --secondary-50: rgba(0, 125, 186, 0.5);
  --secondary-60: rgba(0, 125, 186, 0.6);
  --secondary-70: rgba(0, 125, 186, 0.7);
  --secondary-80: rgba(0, 125, 186, 0.8);
  --secondary-90: rgba(0, 125, 186, 0.9);

  --primary: rgba(0, 63, 102, 1);
  --primary-10: rgba(0, 63, 102, 0.1);
  --primary-20: rgba(0, 63, 102, 0.2);
  --primary-30: rgba(0, 63, 102, 0.3);
  --primary-40: rgba(0, 63, 102, 0.3);
  --primary-50: rgba(0, 63, 102, 0.5);
  --primary-60: rgba(0, 63, 102, 0.6);
  --primary-70: rgba(0, 63, 102, 0.7);
  --primary-80: rgba(0, 63, 102, 0.8);
  --primary-90: rgba(0, 63, 102, 0.9);

  --reject: rgba(255, 77, 79, 1);
  --reject-10: rgba(255, 77, 79, 0.1);
  --reject-20: rgba(255, 77, 79, 0.2);
  --reject-30: rgba(255, 77, 79, 0.3);
  --reject-40: rgba(255, 77, 79, 0.3);
  --reject-50: rgba(255, 77, 79, 0.5);
  --reject-60: rgba(255, 77, 79, 0.6);
  --reject-70: rgba(255, 77, 79, 0.7);
  --reject-80: rgba(255, 77, 79, 0.8);
  --reject-90: rgba(255, 77, 79, 0.9);

  --info: rgba(250, 173, 20, 1);
  --info-10: rgba(250, 173, 20, 0.1);
  --info-20: rgba(250, 173, 20, 0.2);
  --info-30: rgba(250, 173, 20, 0.3);
  --info-40: rgba(250, 173, 20, 0.3);
  --info-50: rgba(250, 173, 20, 0.5);
  --info-60: rgba(250, 173, 20, 0.6);
  --info-70: rgba(250, 173, 20, 0.7);
  --info-80: rgba(250, 173, 20, 0.8);
  --info-90: rgba(250, 173, 20, 0.9);

  --approve: rgb(82, 196, 26, 1);
  --approve-10: rgba(82, 196, 26, 0.1);
  --approve-20: rgba(82, 196, 26, 0.2);
  --approve-30: rgba(82, 196, 26, 0.3);
  --approve-40: rgba(82, 196, 26, 0.3);
  --approve-50: rgba(82, 196, 26, 0.5);
  --approve-60: rgba(82, 196, 26, 0.6);
  --approve-70: rgba(82, 196, 26, 0.7);
  --approve-80: rgba(82, 196, 26, 0.8);
  --approve-90: rgba(82, 196, 26, 0.9);

  --anz-ocean-blue: rgba(0, 125, 186, 1);
  --deep-current: rgba(0, 65, 101, 1);

  --white: rgba(255, 255, 255, 0);
  --white-10: rgba(255, 255, 255, 0.1);
  --white-20: rgba(255, 255, 255, 0.2);
  --white-30: rgba(255, 255, 255, 0.3);
  --white-50: rgba(255, 255, 255, 0.5);
  --white-60: rgba(255, 255, 255, 0.6);
  --white-70: rgba(255, 255, 255, 0.7);
  --white-80: rgba(255, 255, 255, 0.8);
  --white-90: rgba(255, 255, 255, 0.9);
  --grey-1: #eeeeee;
  --grey-2: #cccccc;
  --grey-3: #aaaaaa;
  --grey-4: #888888;
  --grey-5: #666666;
  --grey-6: #444444;

  --highlight: rgba(244, 244, 64, 1);
  --highlight-10: rgba(244, 244, 64, 0.1);
  --highlight-20: rgba(244, 244, 64, 0.2);
  --highlight-30: rgba(244, 244, 64, 0.3);
  --highlight-40: rgba(244, 244, 64, 0.4);
  --highlight-50: rgba(244, 244, 64, 0.5);
  --highlight-60: rgba(244, 244, 64, 0.6);
  --highlight-70: rgba(244, 244, 64, 0.7);
  --highlight-80: rgba(244, 244, 64, 0.8);
  --highlight-90: rgba(244, 244, 64, 0.9);

  --smBp: 768px;
  --mdBp: 1366px;
  --lgBp: 1920px;

  --anz-gradient: linear-gradient(var(--secondary), var(--primary));

  --color-border-light: rgb(240, 240, 240);
}

* {
  -webkit-print-color-adjust: exact !important;
  /* Chrome, Safari, Edge */
  color-adjust: exact !important;
  /*Firefox*/
}

* {
  -webkit-print-color-adjust: exact !important;
  /* Chrome, Safari, Edge */
  color-adjust: exact !important;
  /*Firefox*/
}

@page {
  margin: 0;
  width: var(--export-a4-width);
  height: var(--export-a4-height);
}

@font-face {
  font-family: MyriadPro;
  font-weight: 400;
  src: url("/fonts/MyriadPro-Regular.otf") format("opentype");
}

@font-face {
  font-family: MyriadPro;
  font-weight: 100;
  src: url("/fonts/MyriadPro-Light.otf") format("opentype");
}

@font-face {
  font-family: MyriadPro;
  font-weight: bold;
  src: url("/fonts/MyriadPro-Semibold.otf") format("opentype");
}

@font-face {
  font-family: Gotham;
  font-weight: bold;
  src: url("/fonts/Gotham-Bold.otf") format("opentype");
}

@font-face {
  font-family: Gotham;
  font-weight: bold;
  font-style: italic;
  src: url("/fonts/Gotham-BoldItalic.otf") format("opentype");
}

@font-face {
  font-family: Gotham;
  font-weight: 100;
  src: url("/fonts/Gotham-Light.otf") format("opentype");
}

@font-face {
  font-family: Gotham;
  font-weight: 100;
  font-style: italic;
  src: url("/fonts/Gotham-LightItalic.otf") format("opentype");
}

@font-face {
  font-family: Gotham;
  src: url("/fonts/Gotham-Medium.otf") format("opentype");
}

@font-face {
  font-family: Gotham;
  font-style: italic;
  src: url("/fonts/Gotham-MediumItalic.otf") format("opentype");
}

@font-face {
  font-family: Aeonik, system-ui, sans-serif;
  font-weight: 400;
  src: url("/fonts/Aeonik-Regular.woff2") format("woff2");
}

@font-face {
  font-family: Aeonik, system-ui, sans-serif;
  font-weight: 400;
  font-style: italic;
  src: url("/fonts/Aeonik-RegularItalic.woff2") format("woff2");
}

@font-face {
  font-family: Aeonik, system-ui, sans-serif;
  font-weight: 700;
  src: url("/fonts/Aeonik-Bold.woff2") format("woff2");
}

@font-face {
  font-family: Aeonik, system-ui, sans-serif;
  font-weight: 700;
  src: url("/fonts/Aeonik-BoldItalic.woff2") format("woff2");
}

@font-face {
  font-family: Aeonik, system-ui, sans-serif;
  font-weight: 300;
  src: url("/fonts/Aeonik-Light.woff2") format("woff2");
}

@font-face {
  font-family: Aeonik, system-ui, sans-serif;
  font-weight: 300;
  src: url("/fonts/Aeonik-LightItalic.woff2") format("woff2");
}

@font-face {
  font-family: Aeonik, system-ui, sans-serif;
  font-weight: 500;
  src: url("/fonts/Aeonik-Medium.woff2") format("woff2");
}

@font-face {
  font-family: Aeonik, system-ui, sans-serif;
  font-weight: 500;
  src: url("/fonts/Aeonik-MediumItalic.woff2") format("woff2");
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-y: scroll;
  overflow-x: hidden;
}

.root {
  height: 100%;
}

.login-form {

  background-color: rgb(255, 255, 255);
  border: 1px solid rgb(226, 228, 230);
  border-radius: 3px;
  padding: 64px;
  margin-top: 0px;

}

.login-title {
  font-size: 28px;
  font-weight: 600;
  line-height: 1.125;
  letter-spacing: normal;
}

@media (min-width: 320px) and (max-width: 480px) {
  .login-form {

    border: 0px none;
    border-radius: 0px;
    padding: 64px;
    margin-top: 0px;

  }
}



code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

.sr-only {
  display: none;
}

.step-gate-hidden {
  display: none;
}

:not(.ant-popover-buttons)>.ant-btn {
  display: flex;
  font-size: 12px;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.font-label {
  margin: 16px 0 4px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.04em;
  color: currentColor;
}

.agrigis-modal-table,
.agrigis-modal-table-nested {
  font-family: MyriadPro;
}

.leaflet-popup-pane {
  z-index: 999999999;
}

.pdfTooltip {
  --stroke-color: #f0faff;
  --stroke-width: 1px;
  --shadow-width: 1pt;
  --shadow-offset: calc(-1 * var(--shadow-width));

  color: var(--primary);
  background: transparent;
  border: none;
  box-shadow: none;
  font-size: 14pt;
  font-weight: 800;
  text-shadow: var(--shadow-offset) 0 var(--stroke-color),
    0 var(--shadow-width) var(--stroke-color),
    var(--shadow-width) 0 var(--stroke-color),
    0 var(--shadow-offset) var(--stroke-color);
}

.pdfTooltip:before,
.pdfTooltip:after {
  display: none;
}