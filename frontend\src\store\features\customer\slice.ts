import { type PayloadAction, createSlice } from '@reduxjs/toolkit'

export type CovenantTestOperator = 'lt' | 'lte' | 'gt' | 'gte'

export type NumeratorFormulaType = string[] | undefined
export type DenominatorFormulaType = NumeratorFormulaType

export type CovenantTest = {
  value: number
  label?: string
  operator: CovenantTestOperator | undefined
}

export type ContentSelections = string[][]

export type CustomerPageTab =
  | 'profile'
  | 'funding'
  | 'financials'
  | 'emissions'
  | 'climate'
  | 'valuations'
  | 'data'
  | 'addresses'
  | 'benchmarking'
  | 'balances'
  | 'ccra'

export type SelectedModel = 'sales' | 'projects' | undefined

export type KpiMetricType = '%' | '$' | 'x'
export type EmissionsModeType =
  | 'benchmarking'
  | 'breakdown'
  | 'pathway'
  | 'scatter'
export type BalancesModeType = 'aggregated' | 'individual'
export type BenchmarkingModeType = 'benchmarking' | 'covenant'
export type FinancialsModeType = 'metrics' | 'singleCustomerView' | 'myFarmView'

export const SCV_MODES = [
  'incomeItems',
  'incomeBreakdown',
  'assetsBreakdown',
  'liabilitiesEquityBreakdown',
  'liabilityComposition',
  'shortLongTermDebt',
  'returnOnInvestedCapital',
  'returnOnEquity',
  'cfads',
  'incomeStatementMetrics',
  'balanceSheetMetrics',
  'otherUsefulMetrics',
  'definitions',
  'figuresAndCalculations',
  'cashConversionCycle',
  'workingCapital',
] as const

/** Single Customer View */
export type ScvModeType = (typeof SCV_MODES)[number]

const MFV_MODES = [
  'netStockSales',
  'financialPerformance',
  'farmWorkingExpenses',
  'totalFarmIncome',
  'breakevenMilkPrice',
  'financialPerformancePerEffectiveHectare',
  'liabilitiesKgMs',
  'liabilitiesTfi',
  'liabilitiesEBITR',
  'interestRentVsEBITR',
  'calculations',
] as const

/** My Farm View */
export type MfvModeType = (typeof MFV_MODES)[number]

export type CustomerFinancialsType = ScvModeType | MfvModeType

export const includesFinancialsType =
  (type: CustomerFinancialsType) =>
  (...types: CustomerFinancialsType[]) =>
    types.includes(type)

export interface CustomerReportState {
  selectedComponents: string[][]
  selectedMeasures: string[][]
  selectedFinancials: CustomerFinancialsType[]
  denominator: string
  reportTitle: string
  customerName?: string
}

export interface CustomerState {
  drawer: {
    visible: boolean
    mode: 'data' | 'report'
  }
  tab: CustomerPageTab
  financials: {
    [customerId: number]: {
      mode: FinancialsModeType
      scvMode: ScvModeType
      mfvMode: MfvModeType
    }
  }
  covenant: {
    [customerId: number]: {
      numeratorFormula: NumeratorFormulaType
      denominatorFormula: NumeratorFormulaType
      selectedCustomers: string[] | undefined
      covenantTesting: { [metricKey: string]: CovenantTest }
      metric: KpiMetricType
    }
  }
  benchmarking: {
    [customerId: number]: {
      mode: BenchmarkingModeType
      denominator: [number, string] | undefined
      numerator: [number, string] | undefined
      anzsic: string | undefined
      region: number | undefined
      metric: KpiMetricType
    }
  }
  balances: {
    [customerId: number]: {
      mode: BalancesModeType
      selected: string[][] | undefined
    }
  }
  emissions: {
    [customerId: number]: {
      mode: EmissionsModeType
    }
  }
  report: {
    [customerId: number]: CustomerReportState
  }
}

const initialState: CustomerState = {
  drawer: {
    visible: false,
    mode: 'data',
  },
  tab: 'profile',
  financials: {},
  covenant: {},
  benchmarking: {},
  balances: {},
  emissions: {},
  report: {},
}

export const initialFinancialsState = {
  mode: 'metrics' as FinancialsModeType,
  scvMode: 'incomeBreakdown' as ScvModeType,
  mfvMode: 'financialPerformance' as MfvModeType,
}

export const initialCovenantState = {
  denominatorFormula: undefined,
  numeratorFormula: undefined,
  selectedCustomers: undefined,
  covenantTesting: {},
  metric: '$' as KpiMetricType,
}

export const initialCovenantTestState = {
  label: 'Covenant Test',
  value: 0,
  operator: 'gte' as CovenantTestOperator,
}

export const initialBenchmarkState = {
  mode: 'benchmarking' as BenchmarkingModeType,
  numerator: undefined,
  denominator: undefined,
  metric: '$' as KpiMetricType,
  anzsic: undefined,
  region: undefined,
}

export const initialEmissionState = {
  mode: 'benchmarking' as EmissionsModeType,
}

export const initialBalancesState = {
  selected: undefined,
  mode: 'aggregated' as BalancesModeType,
}

export const initialReportState: CustomerReportState = {
  selectedComponents: [],
  selectedMeasures: [],
  selectedFinancials: [],
  denominator: 'NONE',
  reportTitle: 'Business Performance Report',
}

const customer = createSlice({
  name: 'customer',
  initialState,
  reducers: {
    toggleCustomerDrawer: (state) => {
      state.drawer.visible = !state.drawer.visible
    },
    openCustomerDrawer: (
      state,
      { payload }: PayloadAction<{ mode: 'data' | 'report' }>
    ) => {
      state.drawer.visible = true
      state.drawer.mode = payload.mode
    },
    openReportDrawer: (state) => {
      state.drawer.visible = true
      state.drawer.mode = 'report'
    },
    closeCustomerDrawer: (state) => {
      state.drawer.visible = false
    },
    setCustomerTab: (
      state,
      { payload }: PayloadAction<{ tab: CustomerPageTab }>
    ) => {
      state.tab = payload.tab
    },
    setFinancialsMode: (
      state,
      {
        payload,
      }: PayloadAction<{ mode: FinancialsModeType; customerId: number }>
    ) => {
      const { customerId, mode } = { ...payload }
      if (!state.financials[customerId]) {
        state.financials[customerId] = { ...initialFinancialsState }
      }
      state.financials[customerId].mode = mode
    },
    setFinancialsMfvMode: (
      state,
      { payload }: PayloadAction<{ mfvMode: MfvModeType; customerId: number }>
    ) => {
      const { customerId, mfvMode } = { ...payload }
      if (!state.financials[customerId]) {
        state.financials[customerId] = { ...initialFinancialsState }
      }
      state.financials[customerId].mfvMode = mfvMode
    },
    setFinancialsScvMode: (
      state,
      { payload }: PayloadAction<{ scvMode: ScvModeType; customerId: number }>
    ) => {
      const { customerId, scvMode } = { ...payload }
      if (!state.financials[customerId]) {
        state.financials[customerId] = { ...initialFinancialsState }
      }
      state.financials[customerId].scvMode = scvMode
    },
    setEmissionsMode: (
      state,
      {
        payload,
      }: PayloadAction<{ mode: EmissionsModeType; customerId: number }>
    ) => {
      const { customerId, mode } = { ...payload }
      if (!state.emissions[customerId]) {
        state.emissions[customerId] = { ...initialEmissionState }
      }
      state.emissions[customerId].mode = mode
    },
    setBalancesMode: (
      state,
      { payload }: PayloadAction<{ mode: BalancesModeType; customerId: number }>
    ) => {
      const { customerId, mode } = { ...payload }
      if (!state.balances[customerId]) {
        state.balances[customerId] = { ...initialBalancesState }
      }
      state.balances[customerId].mode = mode
    },
    setCovenantSelectedCustomers: (
      state,
      {
        payload,
      }: PayloadAction<{
        selectedCustomers: string[]
        customerId: number
      }>
    ) => {
      const { customerId, selectedCustomers } = { ...payload }
      if (!state.covenant[customerId]) {
        state.covenant[customerId] = { ...initialCovenantState }
      }
      state.covenant[customerId].selectedCustomers = selectedCustomers
    },
    setBalancesSelectedAccounts: (
      state,
      { payload }: PayloadAction<{ selected: string[][]; customerId: number }>
    ) => {
      const { customerId, selected } = { ...payload }
      if (!state.balances[customerId]) {
        state.balances[customerId] = { ...initialBalancesState }
      }
      state.balances[customerId].selected = selected
    },
    setInitialBalancesSelectedAccounts: (
      state,
      { payload }: PayloadAction<{ selected: string[][]; customerId: number }>
    ) => {
      const { customerId, selected } = { ...payload }
      if (!state.balances[customerId]) {
        state.balances[customerId] = { ...initialBalancesState }
      }
      state.balances[customerId].selected = selected
    },
    setReportSelectedMeasures: (
      state,
      {
        payload,
      }: PayloadAction<{
        selectedMeasures: string[][]
        customerId: number
      }>
    ) => {
      const { customerId, selectedMeasures } = { ...payload }
      if (!state.report[customerId]) {
        state.report[customerId] = { ...initialReportState }
      }
      state.report[customerId].selectedMeasures = selectedMeasures
    },
    setReportSelectedComponents: (
      state,
      {
        payload,
      }: PayloadAction<{
        selectedComponents: string[][]
        customerId: number
      }>
    ) => {
      const { customerId, selectedComponents } = { ...payload }
      if (!state.report[customerId]) {
        state.report[customerId] = { ...initialReportState }
      }
      state.report[customerId].selectedComponents = selectedComponents
    },
    setReportState: (
      state,
      {
        payload,
      }: PayloadAction<
        { customerId: number } & Partial<CustomerState['report'][number]>
      >
    ) => {
      const { customerId, ...selections } = payload
      state.report[customerId] = {
        ...initialReportState,
        ...state.report[customerId],
        ...selections,
      }
    },
    setReportTitle: (
      state,
      {
        payload,
      }: PayloadAction<{
        reportTitle: string
        customerId: number
      }>
    ) => {
      const { customerId, reportTitle } = { ...payload }
      if (!state.report[customerId]) {
        state.report[customerId] = { ...initialReportState }
      }
      state.report[customerId].reportTitle = reportTitle
    },
    setReportCustomerName: (
      state,
      {
        payload,
      }: PayloadAction<{
        customerName: string
        customerId: number
      }>
    ) => {
      const { customerId, customerName } = { ...payload }
      if (!state.report[customerId]) {
        state.report[customerId] = { ...initialReportState }
      }
      state.report[customerId].customerName = customerName
    },
    setReportDenominator: (
      state,
      {
        payload,
      }: PayloadAction<{
        denominator: string
        customerId: number
      }>
    ) => {
      const { customerId, denominator } = { ...payload }
      if (!state.report[customerId]) {
        state.report[customerId] = { ...initialReportState }
      }
      state.report[customerId].denominator = denominator
    },
    setBenchmarkMode: (
      state,
      {
        payload,
      }: PayloadAction<{
        mode: BenchmarkingModeType
        customerId: number
      }>
    ) => {
      const { mode, customerId } = payload
      if (!state.benchmarking[customerId]) {
        state.benchmarking[customerId] = { ...initialBenchmarkState }
      }
      state.benchmarking[customerId].mode = mode
    },
    setBenchmarkNumerator: (
      state,
      {
        payload,
      }: PayloadAction<{
        numerator: [number, string] | undefined
        customerId: number
      }>
    ) => {
      const { numerator, customerId } = payload
      if (!state.benchmarking[customerId]) {
        state.benchmarking[customerId] = { ...initialBenchmarkState }
      }
      state.benchmarking[customerId].numerator = numerator
    },
    setBenchmarkDenominator: (
      state,
      {
        payload,
      }: PayloadAction<{
        denominator: [number, string] | undefined
        customerId: number
      }>
    ) => {
      const { denominator, customerId } = payload
      if (!state.benchmarking[customerId]) {
        state.benchmarking[customerId] = { ...initialBenchmarkState }
      }
      state.benchmarking[customerId].denominator = denominator
    },
    setBenchmarkAnzsic: (
      state,
      {
        payload,
      }: PayloadAction<{
        anzsic: string | undefined
        customerId: number
      }>
    ) => {
      const { anzsic, customerId } = payload
      if (!state.benchmarking[customerId]) {
        state.benchmarking[customerId] = { ...initialBenchmarkState }
      }
      state.benchmarking[customerId].anzsic = anzsic
    },
    setBenchmarkRegion: (
      state,
      {
        payload,
      }: PayloadAction<{
        region: number | undefined
        customerId: number
      }>
    ) => {
      const { region, customerId } = payload
      if (!state.benchmarking[customerId]) {
        state.benchmarking[customerId] = { ...initialBenchmarkState }
      }
      state.benchmarking[customerId].region = region
    },
    setCovenantNumerator: (
      state,
      {
        payload,
      }: PayloadAction<{
        numerator: NumeratorFormulaType
        customerId: number
      }>
    ) => {
      const { numerator, customerId } = payload
      if (!state.covenant[customerId]) {
        state.covenant[customerId] = { ...initialCovenantState }
      }
      state.covenant[customerId].numeratorFormula = numerator
    },
    setCovenantDenominator: (
      state,
      {
        payload,
      }: PayloadAction<{
        denominator: DenominatorFormulaType
        customerId: number
      }>
    ) => {
      const { denominator, customerId } = payload
      if (!state.covenant[customerId]) {
        state.covenant[customerId] = { ...initialCovenantState }
      }
      state.covenant[customerId].denominatorFormula = denominator
    },
    setKpiMetric: (
      state,
      {
        payload,
      }: PayloadAction<{
        metric: KpiMetricType
        customerId: number
      }>
    ) => {
      const { metric, customerId } = payload
      if (!state.benchmarking[customerId]) {
        state.benchmarking[customerId] = { ...initialBenchmarkState }
      }
      state.benchmarking[customerId].metric = metric
    },
    setCovenantMetric: (
      state,
      {
        payload,
      }: PayloadAction<{
        metric: KpiMetricType
        customerId: number
      }>
    ) => {
      const { metric, customerId } = payload
      if (!state.covenant[customerId]) {
        state.covenant[customerId] = { ...initialCovenantState }
      }
      state.covenant[customerId].metric = metric
    },
    setKpiCovenant: (
      state,
      {
        payload,
      }: PayloadAction<{
        covenant: Partial<CovenantTest>
        customerId: number
        numerator?: NumeratorFormulaType
        denominator?: DenominatorFormulaType
      }>
    ) => {
      const { covenant, customerId, numerator, denominator } = payload
      if (!state.covenant[customerId]) {
        state.covenant[customerId] = { ...initialCovenantState }
      }
      const key = [
        ...[...(numerator ?? [])],
        '/',
        ...[...(denominator ?? [])],
      ].join('_')
      if (!state.covenant[customerId].covenantTesting[key]) {
        state.covenant[customerId].covenantTesting[key] = {
          ...initialCovenantTestState,
        }
      }
      state.covenant[customerId].covenantTesting[key] = {
        ...state.covenant[customerId].covenantTesting[key],
        ...covenant,
      }
    },
  },
})

export const actions = customer.actions

export const reducer = customer.reducer

export default customer
