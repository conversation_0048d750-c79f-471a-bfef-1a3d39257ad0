import { CheckOutlined, MinusOutlined, PlusOutlined } from '@ant-design/icons'
import { Alert, Form, Modal, Table, message } from 'antd'
import { useForm } from 'antd/lib/form/Form'
import type BigNumber from 'bignumber.js'
import React, { useMemo, useState } from 'react'
import { useCallback } from 'react'
import { useDispatch } from 'react-redux'
import { HighestAndBestUseInput } from '@components/assets/land/HighestAndBestUseInput'
import { useGetValuationTypesQuery } from '@store/services/assets'
import type { Sale } from '@store/services/sdk'
import { uiActions } from '@store/ui'
import type { ValuationSummary } from '@models/assets/Assets'
import {
  type HighestAndBestUseType,
  compatibleBestUses,
} from '@models/assets/ValuationTypes'
import {
  type SaleEditState,
  type SaleEditStateType,
  getSaleCacheId,
} from '@models/sales/util/SaleEditState'
import type { ValuationSummary as ValuationValuationSummary } from '@models/valuations/ValuationSummary'
import { formatDateFromIso } from '@util'
import { integerToAlpha } from '@util/intgerToAlpha'
import './ComparableSaleSearchTable.scss'

interface ComparableSaleSearchTableProps {
  isValuer: boolean
  salesCount: number
  salesCurrentPage: number
  subjectValuation: ValuationValuationSummary | undefined
  pageName: string
  subjectSummary?: ValuationSummary
  saleEditState: SaleEditState
}

interface SaleWithState {
  index: number
  sale: Sale
  state?: SaleEditStateType
  saleHighestAndBestUseType?: number
  isCompatibleWithSubject: boolean
}

const inferiorOrSuperior = (
  subjectValue?: BigNumber,
  comparableValue?: number | null
) => {
  if (
    subjectValue === undefined ||
    subjectValue === null ||
    comparableValue === undefined ||
    comparableValue === null
  ) {
    return ''
  }
  if (subjectValue.gt(comparableValue)) {
    return 'inferior-comparison'
  }
  if (subjectValue.lt(comparableValue)) {
    return 'superior-comparison'
  }
  return ''
}

interface ComparableSaleFormValues {
  highestAndBestUseType?: number
}

export const ComparableSaleSearchTable = (
  props: ComparableSaleSearchTableProps
) => {
  const {
    salesCount,
    salesCurrentPage,
    subjectValuation,
    pageName,
    saleEditState,
    subjectSummary,
    isValuer,
  } = props

  const dispatch = useDispatch()

  const [addComparableSaleForm] = useForm<ComparableSaleFormValues>()

  const searchResultsPagination = useMemo(() => {
    return {
      simple: true,
      total: salesCount,
      pageSize:
        15 +
        (Object.values(saleEditState || {}).filter((x) => x?.state !== 'NEW')
          .length || 0),
      current: Number(salesCurrentPage),
      onChange: (e: number) => {
        dispatch(
          uiActions.updateSaleFilterState({
            pageName,
            update: {
              page: e,
            },
          })
        )
      },
    }
  }, [salesCount, saleEditState, salesCurrentPage, dispatch, pageName])

  const [saleToAdd, setSaleToAdd] = useState<SaleWithState | undefined>(
    undefined
  )

  const { data: valuationTypes } = useGetValuationTypesQuery()

  const subjectBestUses = useMemo(
    () =>
      subjectSummary?.highestAndBestUseSummary?.map(
        (bestUseSummary) => bestUseSummary.highestAndBestUseType
      ) ?? [],
    [subjectSummary]
  )

  const getCompatibleSubjectBestUses = useCallback(
    (saleRecord: SaleWithState) => {
      if (!valuationTypes) {
        return []
      }
      const compatibleSubjectBestUses = saleRecord?.saleHighestAndBestUseType
        ? compatibleBestUses(
            valuationTypes,
            subjectBestUses,
            saleRecord.saleHighestAndBestUseType
          )
        : subjectBestUses
      return compatibleSubjectBestUses
    },
    [subjectBestUses, valuationTypes]
  )

  const handleOk = useCallback(() => {
    if (saleToAdd) {
      dispatch(
        uiActions.toggleSaleInSaleEditState({
          saleId: getSaleCacheId(
            saleToAdd.sale.id,
            saleToAdd.saleHighestAndBestUseType
          ),
          ...(addComparableSaleForm.getFieldsValue(
            true
          ) as ComparableSaleFormValues),
        })
      )
      setSaleToAdd(undefined)
    }
  }, [addComparableSaleForm, dispatch, saleToAdd])

  const handleCancel = useCallback(() => {
    setSaleToAdd(undefined)
  }, [])

  const onSaleToggle = useCallback(
    (record: SaleWithState) => {
      if (isValuer && record.state === 'NEW') {
        const compatibleSubjectBestUses = getCompatibleSubjectBestUses(record)
        const subjectHasMultipleBestUses = compatibleSubjectBestUses.length > 1
        if (!record.sale.properties.hasValuationSummary) {
          void message.error(
            'Please breakdown this sale before adding it as a comparable sale'
          )
        } else if (
          subjectHasMultipleBestUses ||
          !record.isCompatibleWithSubject
        ) {
          setSaleToAdd(record)
          addComparableSaleForm.resetFields()
        } else {
          // only show modal if the subject property has multiple best uses
          dispatch(
            uiActions.toggleSaleInSaleEditState({
              saleId: getSaleCacheId(
                record.sale.id,
                record.saleHighestAndBestUseType
              ),
              highestAndBestUseType: compatibleSubjectBestUses[0],
            })
          )
        }
      } else {
        dispatch(
          uiActions.toggleSaleInSaleEditState({
            saleId: getSaleCacheId(
              record.sale.id,
              record.saleHighestAndBestUseType
            ),
          })
        )
      }
    },
    [addComparableSaleForm, dispatch, getCompatibleSubjectBestUses, isValuer]
  )

  const columns = useMemo(() => {
    const renderHighestAndBestUseLabel = (
      highestAndBestUseType: HighestAndBestUseType | undefined,
      compatibleWithSubject: boolean
    ) => {
      return (
        <span>
          {highestAndBestUseType?.highestAndBestUse}
          {!compatibleWithSubject && (
            <span title="H&BS is not compatible with subject">*</span>
          )}
        </span>
      )
    }

    return [
      {
        dataIndex: '',
        title: '',
        render: (_unused: unknown, record: SaleWithState) => {
          return (
            <button type="button" onClick={() => onSaleToggle(record)}>
              {record.state === 'ADD' || record.state === 'EXISTING' ? (
                <MinusOutlined />
              ) : (
                <PlusOutlined />
              )}
            </button>
          )
        },
      },
      {
        dataIndex: 'index',
        title: 'Key',
        render: (_: string, record: SaleWithState) => {
          return integerToAlpha(record.index)
        },
      },
      {
        dataIndex: ['sale', 'properties', 'fullAddress'],
        title: 'Address',
        render: (address: string, record: SaleWithState) => {
          return (
            <div className="address-column">
              <span>{address}</span>
              {record.sale.properties.hasValuationSummary ? (
                <span className="vetted">
                  <CheckOutlined />
                </span>
              ) : null}
            </div>
          )
        },
      },
      {
        dataIndex: 'saleHighestAndBestUseType',
        title: 'Best Use',
        render: (saleHighestAndBestUseType: string, record: SaleWithState) => {
          if (
            saleHighestAndBestUseType?.toString() ===
            record.sale.properties.highestAndBestUseType?.toString()
          ) {
            return renderHighestAndBestUseLabel(
              valuationTypes?.[saleHighestAndBestUseType],
              record.isCompatibleWithSubject
            )
            // return valuationTypes?.[saleHighestAndBestUseType]?.highestAndBestUse;
          }
          return (
            <div className="secondary-best-use">
              <div className="header">Secondary H&BS</div>
              <div className="best-use">
                {renderHighestAndBestUseLabel(
                  valuationTypes?.[saleHighestAndBestUseType],
                  record.isCompatibleWithSubject
                )}
              </div>
            </div>
          )
        },
      },
      {
        dataIndex: ['sale', 'properties', 'saleDate'],
        title: 'Sale Date',
        render: (saleDate: string) => formatDateFromIso(saleDate),
      },
      {
        dataIndex: ['sale', 'properties', 'grossSalesPrice'],
        title: 'Gross ($K)',
        render: (value: string, row: SaleWithState) => {
          if (!subjectSummary) {
            return value
          }

          return (
            <span
              className={`${inferiorOrSuperior(
                subjectSummary?.marketValue?.div(1000),
                row.sale.properties.grossSalesPrice
              )}`}
            >
              {value}
            </span>
          )
        },
      },
      {
        dataIndex: ['sale', 'properties', 'netLandAndBuildingsPerTotalHa'],
        title: 'NSP / Total Ha',
        render: (value: string, row: SaleWithState) => {
          if (!subjectSummary) {
            return value
          }

          return (
            <span
              className={`${inferiorOrSuperior(
                subjectSummary?.marketValue?.div(1000),
                row.sale.properties.netLandAndBuildingsPerTotalHa
              )}`}
            >
              {value}
            </span>
          )
        },
      },
      {
        dataIndex: ['sale', 'properties', 'averageEfficientProduction'],
        title: 'AEP',
        render: (value: string, row: SaleWithState) => {
          if (!subjectSummary) {
            return value
          }

          const subjectAEP = subjectSummary?.highestAndBestUseSummary?.find(
            (summary) =>
              summary?.highestAndBestUseType ===
              row?.sale?.properties?.highestAndBestUseType
          )?.total_AEP

          return (
            <span
              className={`${inferiorOrSuperior(
                subjectAEP,
                row?.sale?.properties?.averageEfficientProduction
              )}`}
            >
              {value}
            </span>
          )
        },
      },
      {
        dataIndex: ['sale', 'properties', 'totalHa'],
        title: 'Total Area',
        render: (value: string, row: SaleWithState) => {
          if (!subjectSummary) {
            return value
          }

          const subjectTotalHectares = subjectSummary?.totalHectares

          return (
            <span
              className={`${inferiorOrSuperior(
                subjectTotalHectares,
                row.sale.properties.totalHa
              )}`}
            >
              {value}
            </span>
          )
        },
      },
    ]
  }, [onSaleToggle, valuationTypes, subjectSummary])

  const searchResultsTable = useMemo(() => {
    if (!saleEditState) {
      return []
    }

    const dataSource: SaleWithState[] = Object.values(saleEditState)
      .map((saleEdit, index) => {
        return {
          sale: saleEdit.sale,
          state: saleEdit.state,
          saleHighestAndBestUseType: saleEdit.saleHighestAndBestUseType,
          isCompatibleWithSubject: saleEdit.isCompatibleWithSubject,
          index,
        }
      })
      .sort((saleEditA: SaleWithState, saleEditB: SaleWithState) => {
        if (
          saleEditA.state !== saleEditB.state &&
          saleEditA.state === 'EXISTING'
        ) {
          return -1
        }
        if (
          saleEditA.state !== saleEditB.state &&
          saleEditB.state === 'EXISTING'
        ) {
          return 1
        }
        const sortValue =
          new Date(
            saleEditB.sale.properties.saleDate ?? '1970-01-01'
          ).valueOf() -
          new Date(saleEditA.sale.properties.saleDate ?? '1970-01-01').valueOf()
        if (Number.isNaN(sortValue)) {
          return 0
        }
        return sortValue
      })

    return (
      <Table
        pagination={searchResultsPagination}
        className="no-header-table"
        dataSource={dataSource}
        size="small"
        rowClassName={(row) =>
          `sales-search-result ${row.state?.toLowerCase() ?? ''}`
        }
        columns={columns}
        rowKey={(e) =>
          `${e.sale.id ?? ''}-${e.sale.properties.source ?? ''}-${
            e.sale.properties.sourceReference ?? ''
          }-${e.saleHighestAndBestUseType ?? ''}`
        }
        onRow={(record, rowIndex) => {
          return {
            onClick: () => {
              dispatch(
                uiActions.setSelectedSale({
                  sale: record.sale,
                  saleRowIndex: rowIndex,
                })
              )
            },
          }
        }}
      />
    )
  }, [saleEditState, searchResultsPagination, columns, dispatch])

  const compatibleSubjectBestUses = useMemo(
    () => (saleToAdd ? getCompatibleSubjectBestUses(saleToAdd) : []),
    [getCompatibleSubjectBestUses, saleToAdd]
  )
  const onlyOneBestUse = compatibleSubjectBestUses.length === 1

  return (
    <>
      <div className="agrigis-table">{searchResultsTable}</div>
      <Modal
        title="Add Comparable Sale"
        visible={saleToAdd !== undefined}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form form={addComparableSaleForm}>
          <>
            <Form.Item
              hidden={onlyOneBestUse}
              label="Comparable Highest & Best Use"
              name="highestAndBestUseType"
              initialValue={
                subjectValuation?.valuation?.highestAndBestUseType ?? -1
              }
            >
              <HighestAndBestUseInput
                validOptions={
                  saleToAdd?.isCompatibleWithSubject
                    ? compatibleSubjectBestUses
                    : subjectBestUses
                }
              />
            </Form.Item>
            {!onlyOneBestUse && (
              <Alert
                type="info"
                message={
                  'Select the Best Use for the subject that this sale is comparable for.'
                }
              />
            )}
          </>
        </Form>
      </Modal>
    </>
  )
}
