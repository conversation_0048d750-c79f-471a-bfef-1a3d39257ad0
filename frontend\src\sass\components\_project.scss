.agrigis-project-click-menu {
  .ant-form {
    > .ant-row {
      display: flex;
      flex-flow: row;
      flex-wrap: none;

      .ant-col {
        flex-shrink: 1;
      }
    }
  }
}

.Layout[id='projects'] {
  .layout-content-header {
    display: flex;
    padding: 1em;
    margin-right: 1em;
    margin-bottom: 2em;
    justify-content: space-between;
    background: rgba(238, 238, 238, 0.25);
    outline: 1px solid rgba(238, 238, 238, 1);
    align-items: center;
  }

  .layout-content {
    display: flex;
  }

  .agrigis-project-asset {
    display: grid;
    grid-template-columns: 1fr 1fr;
    margin-top: 1em;
    max-width: 1200px;
    min-height: 300px;
    outline: 2px solid #eee;
    padding: 1em;

    &:not(:last-child) {
      margin-bottom: 2em;
    }
  }

  .layout-content-wrapper {
    padding: 1em;
    padding-right: 0;
    display: block;
    width: 100%;
    max-width: 1920px;
  }

  .agrigis-table > .agrigis-project-table {
    // Purge other !important settings
    tr:nth-child(odd):hover,
    tr:nth-child(even):hover,
    tr:hover,
    tr:nth-child(odd),
    tr:nth-child(even),
    tr {
      background-color: transparent;

      th,
      td {
        background-color: transparent;
        border: none;
      }
    }

    thead {
      th {
        border-bottom: none !important;
      }
    }

    tbody tr {
      transition: 0.5s background-color;
      color: black;
      background-color: white;

      $border-color: #ddd;

      &:first-child {
        td {
          border-top: $border-color 1px solid !important;
        }
      }

      td {
        border-left: none !important;
        border-right: none !important;
        border-bottom: $border-color 1px solid !important;
        border-top: none !important;

        &:first-child {
          border-left: $border-color 1px solid !important;
        }

        &:last-child {
          border-right: $border-color 1px solid !important;
        }
      }

      &:hover {
        cursor: pointer;
        transition: 0.2s background-color;
        background-color: rgba(245, 245, 245, 0.9) !important;
      }
    }

    .agrigis-approval-draft,
    .agrigis-approval-rework {
      background-color: #fff5cc !important;

      &:hover {
        background-color: tint(#fff5cc, 50%) !important;
      }
    }

    .agrigis-approval-approved {
      background-color: #cdffc6 !important;

      &:hover {
        background-color: tint(#cdffc6, 50%) !important;
      }
    }

    .agrigis-approval-cancelled {
      background-color: #eeeeee !important;

      &:hover {
        background-color: tint(#eeeeee, 50%) !important;
      }
    }

    .agrigis-approval-rejected {
      background-color: #ffd0d0 !important;

      &:hover {
        background-color: tint(#ffd0d0, 50%) !important;
      }
    }
  }

  .agrigis-project-form {
    padding-right: 1em;
    background-color: white;

    .agrigis-project-form-row {
      display: flex;
      justify-content: flex-start;

      .ant-form-item-control-input {
        min-width: 150px !important;
      }

      .ant-form-item-control {
        flex-wrap: wrap;
        align-content: flex-end;
        align-items: flex-end;
      }

      > .ant-row {
        flex-grow: 1;
        max-width: 500px;
        flex-wrap: none;
      }
    }

    .ant-select,
    input {
      font-size: 12px;
      max-width: 250px;
    }

    textarea {
      font-size: 12px;
      min-width: 250px;
    }

    .ant-form-item-control-input-content {
      display: flex;
    }

    .ant-form-item {
      margin: 0;

      .ant-form-item-control-input {
        min-width: 100px;
      }

      // .ant-form-item-label label {
      //     text-transform: none !important;
      //     min-width     : 175px;
      // }
    }
  }

  .agrigis-project-click-menu {
    position: absolute;
    z-index: 1000;
  }
}

ul.project-approval-comments {
  padding: 0;
  margin: 0;
  min-width: 300px;
  max-width: 100%;

  li {
    &:before {
      content: attr(data-label);
      margin-right: 8px;
    }
  }
}

.agrigis-project-menu-list-item {
  a,
  a:hover {
    text-decoration: none !important;
    color: currentColor !important;
  }
}

.agrigis-project-map-container {
  display: flex;
  width: 100%;
  padding-right: 1em;
  height: calc(100% - 1em);
  max-width: 1920px;

  .map-container-sider {
    width: 350px;
    flex-grow: 1;
    padding-right: 1em;
    margin-right: 1em;

    .ant-btn {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }

    h2 {
      margin-bottom: 1em;
      padding-bottom: 1em;
      border-bottom: #eee 1px solid;
    }
  }
}

.button-group {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 6px;
  border: 1px solid #eee;
  transition: background-color 0.5s;

  &[data-dragging='true'] {
    background-color: #ffff99;
    transition: background-color 0.1s;
  }

  .content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &:before {
    content: url('../img/draggable.svg');
    margin-right: 1em;
  }

  .label {
    font-size: 12px;
    color: #004168;
  }

  .cluster {
    display: flex;
  }

  &:not(:first-child) {
    margin-top: 0.25em;
  }
}

.ProjectMapColorPickerButton,
.project-map-button.ant-btn {
  width: 32px !important;
  height: 32px !important;
  padding: 0;
  justify-content: center !important;
  display: flex;
}

.agrigis-project-chart {
  &:not(:first-child) {
    margin-top: 2em;
  }
}

.agrigis-projects-project-search-filters {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 1em;
  padding-top: 1em;

  .agrigis-projects-project-search-filter {
    font-size: 12px;

    &:not(:first-child) {
      margin-top: 0.5em;
    }

    &:before {
      content: attr(data-label);
    }
  }

  .ant-select {
    width: 100%;

    .ant-select-selection-item-remove {
      line-height: 12px !important;
      align-self: center !important;
    }
  }
}

.agrigis-project-progress {
  display: flex;
  justify-content: flex-start;

  .complete {
    background-color: rgb(246, 255, 237);
    outline: rgb(183, 235, 143) solid 1px;
    display: flex;
    justify-content: space-between;
    padding: 1em;
    width: 100%;
    align-items: center;

    span {
      font-size: 14px;
      letter-spacing: 1px;
      color: $approve-color;
    }

    span:first-child {
      font-weight: 500;
      align-items: center;
      display: flex;
    }
  }

  .agrigis-alert {
    width: 100%;
    min-width: 100% !important;
    max-width: 50vw;
  }

  .ant-progress-text {
    color: #007dba;
    font-weight: bold;
  }

  .ant-progress-text {
    display: flex;
    justify-content: center;
    align-items: center;

    svg {
      width: 24px;
      height: 24px;
    }
  }

  .agrigis-project-progress-title {
    font-size: 14px;
    letter-spacing: 1px;
    color: #007dba;
    margin-bottom: 1em;
  }

  &:not(:first-child) {
    margin-top: 1em;
    padding-top: 1em;
    border-top: 1px #eee solid;
  }

  .agrigis-project-progress-column {
    min-width: 200px;

    &:nth-child(2) {
      width: 100%;
    }

    &:first-child {
      margin-right: 2em;
    }

    ul {
      font-size: 12px;
      list-style: upper-greek;
      margin-bottom: 0;
    }
  }

  .agrigis-alert {
    padding: 1em !important;

    p {
      margin: none;
    }

    ul {
      font-size: 12px;
      display: block;
      list-style-type: disc;
      -webkit-margin-before: 1em;
      -webkit-margin-after: 1em;
      -webkit-margin-start: 0px;
      -webkit-margin-end: 0px;
      -webkit-padding-start: 40px;
    }
  }
}

.agrigis-projects-compliance-status,
.agrigis-projects-project-search-container {
  margin: 2em 0.25em 0.25em 0.25em;
  padding: 0;
  background-color: rgba(255, 255, 255, 0.95);
  padding: 0.5em;
  border: 1px solid #333;

  .agrigis-projects-project-search {
    margin-bottom: 0.5em;
  }

  .agrigis-projects-project-search-title {
    margin-bottom: 0.5em;
    color: #004168;
    font-size: 14px;
    letter-spacing: 0.5px;

    svg {
      margin-right: 1em;
    }
  }

  .ant-checkbox-wrapper span {
    font-size: 12px;
  }

  .ant-select {
    margin-bottom: 0;
  }
}

.agrigis-projects-project-search,
.agrigis-projects-user-search {
  min-width: 200px;

  .ant-select-selection-placeholder {
    font-size: 12px;
  }

  .debounced-select {
    width: 100%;
  }
}

.agrigis-project-type,
.agrigis-project-approval-state {
  white-space: nowrap;
  min-width: 200px;

  &:before {
    content: attr(data-icon);
    margin-right: 8px;
  }
}

.agrigis-project-search-container {
  padding: 1em;

  tr {
    &:hover {
      cursor: pointer !important;
    }
  }

  .agrigis-table {
    margin-top: 1em;
    outline: #eee solid 1px;
  }
}

.agrigis-project-impact-summary {
  margin-top: 1em;
}

.agrigis-project-statistics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2em;

  .ant-statistic-title {
    font-size: 12px;
    margin-bottom: 0;
  }

  .ant-statistic-content-title,
  .ant-statistic-content {
    padding: 0;
    margin-bottom: 0;
  }

  .ant-statistic-content-value {
    color: #007dba;
    font-size: 17px;
  }
}

.agrigis-project-click-menu {
  position: absolute;
  z-index: 1000;
  background-color: white;
  font-size: 13px;
  min-width: 150px;
  padding: 1em;
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.75);

  .agrigis-project-menu-content {
    max-width: 600px;

    .agrigis-project-form-row {
      display: flex;
      flex-grow: 1;

      > .ant-row {
        flex-grow: 1;
      }
    }

    .ant-form-item-control-input-content {
      display: flex;
    }

    .ant-select-selector {
      min-width: 225px !important;
    }
  }

  .agrigis-project-menu-content-footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 1em;
    margin-top: 1em;
    border-top: 1px #eee solid;

    button {
      margin-left: 1em;
    }

    > div {
      margin-left: 3px;
    }
  }

  .agrigis-project-form {
    label {
      text-transform: none;
    }

    [data-metric='text'] .ant-form-item-control-input {
      min-width: 300px;
      max-width: 100%;
    }

    [data-metric='date'],
    [data-metric='\$'],
    [data-metric='kWh'],
    [data-metric='%'],
    [data-metric='yrs'] {
      .ant-form-item-control-input {
        width: 225px;
      }
    }

    .ant-form-item {
      margin-bottom: 0px;

      .ant-form-item-label label {
        min-width: 150px;
      }
    }

    .ant-input-group {
      min-width: 100px;
    }

    .ant-form-item-control-input {
      align-self: flex-end;

      textarea {
        min-width: 300px;
        min-height: 150px;
      }
    }
  }

  .agrigis-project-click-menu-header {
    display: flex;
    border-bottom: #eee solid 1px;
    margin-bottom: 1em;
    padding-bottom: 1em;
    justify-content: space-between;

    &:before {
      content: attr(data-icon);
      padding-right: 16px;
    }
  }

  .agrigis-project-menu-list-item {
    padding: 4px;
    transition: 0.25s background-color;
    padding: 0.5em 1em;

    &:before {
      display: inline-block;
      content: attr(data-icon);
      margin-right: 8px;
      min-width: 30px;
    }

    &:hover {
      background-color: #eee;
      outline: 1px solid #ccc;
      cursor: pointer;
    }
  }
}

.agrigis-sidebar[id='projects'] {
  display: block;
  min-width: 300px !important;
  height: 100vh;
  flex: 0 0;
  background-color: #747678;
  border-right: 1px solid #004168;

  .agrigis-sidebar-menu {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 3px 3px;

    .agrigis-sidebar-menu-item {
      background-color: #c6dfea;
      color: #004168;
      border: 1px solid #004168;
      filter: brightness(90%);
      padding: 8px 16px;
      transition: 0.25s filter, 0.5s background-color;
      cursor: pointer;
      font-size: 12px;
      margin-bottom: 2px;
      display: flex;
      letter-spacing: 0.75px;

      &[data-disabled='true'] {
        background-color: #eee;
        border-color: #333;
        color: #333;
        cursor: not-allowed;
        z-index: 10010;

        &:after {
          transition: opacity 0.25s;
          content: attr(data-disabled-message);
          position: absolute;
          left: 125px;
          direction: ltr;
          opacity: 0;
          margin-top: -8px;
          width: 200px;
          background-color: tint($reject-color, 80%);
          border: 1px solid shade($reject-color, 30%);
          padding: 0.25em 1em;
          letter-spacing: 0;
        }

        &:hover {
          filter: brightness(90%);

          &:after {
            opacity: 1;
          }
        }
      }

      svg {
        margin-right: 16px;
      }

      &:not([data-disabled='true']) {
        &:hover {
          filter: brightness(100%);
        }

        &:active {
          transition: 0.05s background-color;
          background-color: #fff;
        }
      }

      &[data-selected='true'] {
        transition: 0.05s background-color;
        background-color: #fff;
        filter: brightness(100%);
      }
    }
  }
}
