<svg width="282" height="76" viewBox="0 0 282 76" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_26_9)">
<g filter="url(#filter1_d_26_9)">
<path d="M85.7421 75C79.5565 75 74.4387 74.1532 70.3886 72.4595C66.4122 70.7658 63.5403 68.3358 61.773 65.1694C60.0057 61.9293 59.3061 58.0265 59.6743 53.461C59.8216 52.2091 60.3371 51.5832 61.2207 51.5832H71.6036C72.5609 51.5832 73.0027 52.1355 72.9291 53.2401C72.9291 56.8483 74.0337 59.4993 76.2428 61.1929C78.4519 62.813 81.8393 63.623 86.4048 63.623C90.8967 63.623 94.2104 62.9234 96.3459 61.5243C98.555 60.0515 99.6596 57.8056 99.6596 54.7864C99.6596 53.3873 99.4019 52.1723 98.8864 51.1414C98.3709 50.0368 97.4873 49.0795 96.2354 48.2695C94.9836 47.3859 93.2899 46.5022 91.1544 45.6186L75.1382 38.7702C71.6036 37.2239 68.7686 35.567 66.6331 33.7997C64.4976 31.9588 62.988 29.8601 62.1044 27.5037C61.2207 25.1473 60.7789 22.4595 60.7789 19.4404C60.8525 13.1811 62.9512 8.3947 67.0749 5.081C71.1986 1.76731 77.3474 0.110462 85.5212 0.110462C91.2649 0.110462 96.125 0.920475 100.101 2.5405C104.151 4.08689 107.171 6.40648 109.159 9.49926C111.147 12.5184 111.92 16.3108 111.478 20.8763C111.331 22.2018 110.889 22.8645 110.153 22.8645H99.6596C99.2178 22.8645 98.8864 22.7172 98.6655 22.4227C98.4446 22.1281 98.3709 21.6127 98.4446 20.8763C98.6655 17.9308 97.5977 15.6112 95.2413 13.9175C92.9586 12.1502 89.6817 11.2666 85.4107 11.2666C81.6552 11.2666 78.8201 11.9293 76.9055 13.2548C74.991 14.5803 74.0337 16.5685 74.0337 19.2194C74.0337 20.6186 74.2914 21.8704 74.8069 22.975C75.3223 24.0059 76.206 24.9632 77.4578 25.8468C78.7833 26.7305 80.5506 27.651 82.7597 28.6082L98.555 35.3461C102.163 36.8188 104.998 38.4757 107.06 40.3166C109.196 42.0839 110.705 44.1458 111.589 46.5022C112.546 48.8586 112.988 51.6568 112.914 54.8969C112.914 61.3034 110.742 66.2739 106.397 69.8085C102.053 73.2695 95.1677 75 85.7421 75Z" fill="white"/>
<path d="M151.285 75C145.91 75 141.197 73.9691 137.147 71.9072C133.097 69.7717 129.967 66.6789 127.758 62.6289C125.549 58.5788 124.444 53.6082 124.444 47.7172V27.2828C124.444 18.4462 127.021 11.7084 132.176 7.06922C137.404 2.35641 144.547 0 153.605 0C160.085 0 165.534 1.03093 169.952 3.09278C174.444 5.15464 177.721 8.02651 179.783 11.7084C181.918 15.3166 182.655 19.4772 181.992 24.19C181.918 25.0736 181.477 25.5155 180.667 25.5155L169.952 25.405C169.069 25.405 168.664 24.9632 168.737 24.0795C168.884 21.4286 168.406 19.1826 167.301 17.3417C166.27 15.4271 164.65 13.9912 162.441 13.0339C160.232 12.0766 157.397 11.5979 153.936 11.5979C148.56 11.5979 144.51 12.9234 141.786 15.5744C139.061 18.1517 137.699 22.165 137.699 27.6141V47.3859C137.699 52.9087 139.024 56.9588 141.675 59.5361C144.4 62.1134 148.413 63.4021 153.715 63.4021C158.87 63.4021 162.809 62.2239 165.534 59.8674C168.259 57.4374 169.621 53.9396 169.621 49.3741V45.8395H154.709C153.826 45.8395 153.384 45.3976 153.384 44.514V37.1134C153.384 36.3034 153.826 35.8984 154.709 35.8984H180.998C181.366 35.8984 181.661 36.0088 181.882 36.2297C182.102 36.4507 182.213 36.7452 182.213 37.1134V57.4374C182.213 59.9411 182.25 62.4816 182.323 65.0589C182.471 67.6362 182.728 70.0295 183.097 72.2386C183.244 72.6804 183.17 73.0486 182.876 73.3432C182.655 73.5641 182.36 73.6745 181.992 73.6745H173.376C173.008 73.6745 172.714 73.6009 172.493 73.4536C172.272 73.2327 172.088 72.9381 171.94 72.57C171.793 71.6863 171.646 70.6186 171.499 69.3667C171.425 68.1149 171.351 66.8998 171.278 65.7216H171.057C169.805 67.6362 168.222 69.2931 166.307 70.6922C164.466 72.0913 162.294 73.1591 159.79 73.8954C157.287 74.6318 154.451 75 151.285 75Z" fill="white"/>
<path d="M199.903 73.6745C199.093 73.6745 198.688 73.2327 198.688 72.349V2.3564C198.688 1.47275 199.093 1.03093 199.903 1.03093H210.617C211.501 1.03093 211.943 1.47275 211.943 2.3564V72.349C211.943 73.2327 211.501 73.6745 210.617 73.6745H199.903Z" fill="white"/>
<path d="M250.78 75C244.594 75 239.476 74.1532 235.426 72.4595C231.45 70.7658 228.578 68.3358 226.811 65.1694C225.043 61.9293 224.344 58.0265 224.712 53.461C224.859 52.2091 225.375 51.5832 226.258 51.5832H236.641C237.599 51.5832 238.04 52.1355 237.967 53.2401C237.967 56.8483 239.071 59.4993 241.28 61.1929C243.49 62.813 246.877 63.623 251.442 63.623C255.934 63.623 259.248 62.9234 261.383 61.5243C263.593 60.0515 264.697 57.8056 264.697 54.7864C264.697 53.3873 264.439 52.1723 263.924 51.1414C263.409 50.0368 262.525 49.0795 261.273 48.2695C260.021 47.3859 258.328 46.5022 256.192 45.6186L240.176 38.7702C236.641 37.2239 233.806 35.567 231.671 33.7997C229.535 31.9588 228.026 29.8601 227.142 27.5037C226.258 25.1473 225.816 22.4595 225.816 19.4404C225.89 13.1811 227.989 8.3947 232.113 5.081C236.236 1.76731 242.385 0.110462 250.559 0.110462C256.302 0.110462 261.163 0.920475 265.139 2.5405C269.189 4.08689 272.208 6.40648 274.196 9.49926C276.185 12.5184 276.958 16.3108 276.516 20.8763C276.369 22.2018 275.927 22.8645 275.191 22.8645H264.697C264.255 22.8645 263.924 22.7172 263.703 22.4227C263.482 22.1281 263.409 21.6127 263.482 20.8763C263.703 17.9308 262.635 15.6112 260.279 13.9175C257.996 12.1502 254.719 11.2666 250.448 11.2666C246.693 11.2666 243.858 11.9293 241.943 13.2548C240.029 14.5803 239.071 16.5685 239.071 19.2194C239.071 20.6186 239.329 21.8704 239.844 22.975C240.36 24.0059 241.244 24.9632 242.495 25.8468C243.821 26.7305 245.588 27.651 247.797 28.6082L263.593 35.3461C267.201 36.8188 270.036 38.4757 272.098 40.3166C274.233 42.0839 275.743 44.1458 276.626 46.5022C277.584 48.8586 278.026 51.6568 277.952 54.8969C277.952 61.3034 275.78 66.2739 271.435 69.8085C267.09 73.2695 260.205 75 250.78 75Z" fill="white"/>
</g>
<rect x="4" y="1.10458" width="13.2548" height="72.349" fill="white"/>
<rect opacity="0.7" x="17.2548" y="45.8395" width="16.5685" height="18.2253" transform="rotate(-90 17.2548 45.8395)" fill="#C6DFEA"/>
<rect opacity="0.4" x="17.2548" y="17.673" width="16.5685" height="31.4801" transform="rotate(-90 17.2548 17.673)" fill="#C6DFEA"/>
<rect x="17.2548" y="73.4536" width="16.5685" height="31.4801" transform="rotate(-90 17.2548 73.4536)" fill="#007DBA"/>
</g>
<defs>
<!-- <filter id="filter0_d_26_9" x="0" y="0" width="281.96" height="83" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> -->
<!-- <feFlood flood-opacity="0" result="BackgroundImageFix"/> -->
<!-- <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> -->
<!-- <feOffset dy="4"/> -->
<!-- <feGaussianBlur stdDeviation="2"/> -->
<!-- <feComposite in2="hardAlpha" operator="out"/> -->
<!-- <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/> -->
<!-- <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_26_9"/> -->
<!-- <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_26_9" result="shape"/> -->
<!-- </filter> -->
<!-- <filter id="filter1_d_26_9" x="55.5791" y="0" width="226.381" height="83" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> -->
<!-- <feFlood flood-opacity="0" result="BackgroundImageFix"/> -->
<!-- <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> -->
<!-- <feOffset dy="4"/> -->
<!-- <feGaussianBlur stdDeviation="2"/> -->
<!-- <feComposite in2="hardAlpha" operator="out"/> -->
<!-- <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/> -->
<!-- <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_26_9"/> -->
<!-- <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_26_9" result="shape"/> -->
<!-- </filter> -->
</defs>
</svg>
