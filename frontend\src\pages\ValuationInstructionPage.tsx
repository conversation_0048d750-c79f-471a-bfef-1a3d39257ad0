import { Helmet } from 'react-helmet'
import { Route, Routes, useParams } from 'react-router-dom'
import ProtectedRoute from '@components/ProtectedRoute'
import InstructionLetterGeneratorView from '@components/propertyFlow/externalValuation/instructionLetter/InstructionLetterGeneratorView'
import InstructionLetterValuerSearchView from '@components/propertyFlow/externalValuation/instructionLetter/InstructionLetterValuerSearchView'
import { isValuerType } from '@components/propertyFlow/externalValuation/types'
import { capitalize } from 'lodash'
import ValuerSearchSidebar from '@components/propertyFlow/externalValuation/instructionLetter/ValuerSearchSidebar'
import { Dashboard } from '@components/generic/Layout'
import InstructionLetterListView from '@components/propertyFlow/externalValuation/instructionLetter/InstructionLetterListView'
import { useEntitled } from '@components/EntitledComponent'
import { PermissionDeniedPage } from './PermissionDeniedPage'

const ValuationInstructionPage = () => {
  const { valuerType } = useParams()

  if (!valuerType || !isValuerType(valuerType)) {
    return null
  }

  return (
    <>
      <Helmet>
        <title>{capitalize(valuerType)} Panel</title>
      </Helmet>
      <ProtectedRoute
        requiredEntitlements={
          valuerType === 'commercial'
            ? ['client:propertyflow:instruction_letter:commercial:view']
            : ['client:propertyflow:instruction_letter:rural:view']
        }
      >
        <Dashboard data-testid="instruction-letter-search">
          <ValuerSearchSidebar valuerType={valuerType} />
          <Routes>
            <Route
              path=""
              element={
                <InstructionLetterValuerSearchView valuerType={valuerType} />
              }
            />
            <Route
              path="mine"
              element={<InstructionLetterListView valuerType={valuerType} />}
            />
            <Route
              path="request/new"
              element={
                <InstructionLetterGeneratorView valuerType={valuerType} />
              }
            />
            <Route
              path="request/:requestId"
              element={
                <InstructionLetterGeneratorView valuerType={valuerType} />
              }
            />
          </Routes>
        </Dashboard>
      </ProtectedRoute>
    </>
  )
}

export default ValuationInstructionPage
