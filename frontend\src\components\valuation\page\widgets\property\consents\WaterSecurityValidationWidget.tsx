import { Button, Form, Input, Radio, Space, message } from 'antd'
import { useForm } from 'antd/lib/form/Form'
import React, { useCallback, useEffect, useState } from 'react'
import { waterSecurityPermitQuestion } from '@components/valuation/language'
import {
  useGetValuationSummaryQuery,
  useUpdateValuationWaterSecurityReviewMutation,
} from '../../../../../../store/services/valuations'

const VALIDATION_OPTIONS = [
  { value: 1, label: 'Yes' },
  { value: 2, label: 'No' },
]

interface WaterSecurityValidationFormProperties {
  waterSecurityReviewStatus: number
  waterSecurityReviewComments: string
}

export interface WaterSecurityValidationWidgetProps {
  valuationId: string
  disabled?: boolean
}

export const WaterSecurityValidationWidget = ({
  valuationId,
  disabled,
}: WaterSecurityValidationWidgetProps) => {
  const [form] = useForm<WaterSecurityValidationFormProperties>()

  const [dirty, setDirty] = useState(false)

  const [updateWaterSecurityReview] =
    useUpdateValuationWaterSecurityReviewMutation()

  const { data: valuationSummary } = useGetValuationSummaryQuery(valuationId)

  // biome-ignore lint/correctness/useExhaustiveDependencies:
  useEffect(() => {
    form.resetFields()
  }, [form, valuationSummary])

  const saveChanges = useCallback(async () => {
    const formValues: WaterSecurityValidationFormProperties =
      form.getFieldsValue(true)
    const result = await updateWaterSecurityReview({
      valuationId,
      ...formValues,
    })
    if ('error' in result) {
      void message.error(JSON.stringify(result.error))
    } else {
      setDirty(false)
    }
  }, [form, updateWaterSecurityReview, valuationId])

  const clearChanges = useCallback(() => {
    form.resetFields()
    setDirty(false)
  }, [form])

  const permitQuestion = waterSecurityPermitQuestion

  return (
    <div>
      <h2>Water Security Review</h2>
      <Form
        disabled={disabled}
        form={form}
        onValuesChange={(
          changedValues: Partial<WaterSecurityValidationFormProperties>
        ) => {
          setDirty(true)
          if (
            changedValues.waterSecurityReviewStatus !== undefined &&
            changedValues.waterSecurityReviewStatus !== 2
          ) {
            form.setFieldsValue({
              waterSecurityReviewComments: '',
            })
          }
        }}
        initialValues={{
          waterSecurityReviewStatus:
            valuationSummary?.valuation?.waterSecurityReviewStatus,
          waterSecurityReviewComments:
            valuationSummary?.valuation?.waterSecurityReviewComments,
        }}
      >
        <Space direction="vertical">
          <div style={{ fontWeight: 500 }}>{permitQuestion}</div>
          <Form.Item noStyle name="waterSecurityReviewStatus">
            <Radio.Group
              options={VALIDATION_OPTIONS}
              disabled={!!valuationSummary?.valuation?.completedDate}
            />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) =>
              prevValues.waterSecurityReviewStatus !==
              curValues.waterSecurityReviewStatus
            }
          >
            {() =>
              form.getFieldValue('waterSecurityReviewStatus') === 2 && (
                <Form.Item name="waterSecurityReviewComments" label="Comments">
                  <Input.TextArea
                    rows={4}
                    disabled={!!valuationSummary?.valuation?.completedDate}
                  />
                </Form.Item>
              )
            }
          </Form.Item>
          <div
            style={{
              display: 'grid',
              gridAutoFlow: 'column',
              justifyContent: 'flex-start',
              columnGap: '4px',
              marginTop: '16px',
              // background: 'white',
            }}
          >
            <Button disabled={!dirty} onClick={saveChanges}>
              Save
            </Button>
            <Button disabled={!dirty} onClick={clearChanges}>
              Cancel
            </Button>
          </div>
        </Space>
      </Form>
    </div>
  )
}
// <Select options={VALIDATION_OPTIONS} defaultValue={0} disabled={!!(valuationSummary?.valuation?.completedDate)}/>
