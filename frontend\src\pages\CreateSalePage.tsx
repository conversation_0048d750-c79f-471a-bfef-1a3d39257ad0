import React from 'react'
import ProtectedRoute from '@components/ProtectedRoute'
import { CreateSaleView } from '@components/sales'

const CreateSalePage = ({ listingMode }: { listingMode?: boolean }) => {
  return (
    <ProtectedRoute requiredEntitlements={['client:propertyflow:sales:create']}>
      <CreateSaleView listingMode={listingMode} />
    </ProtectedRoute>
  )
}

export default CreateSalePage
