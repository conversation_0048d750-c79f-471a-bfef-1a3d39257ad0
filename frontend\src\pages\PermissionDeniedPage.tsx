import { Divider, Typography } from 'antd'
import { Helmet } from 'react-helmet'
import { <PERSON> } from 'react-router-dom'
import styles from './ErrorPage.module.scss'

export const PermissionDeniedPage = () => {
  return (
    <div className={styles.container}>
      <Helmet>
        <title>Permission Denied</title>
      </Helmet>
      <div className={styles.error}>
        <Typography className={styles.errorCode}>403</Typography>
        <Typography className={styles.errorType}>Forbidden</Typography>
        <Divider />
        <Typography className={styles.errorDescription}>
          <p>
            You do not have sufficient permissions to view this module of ESGIS.
            If you believe this is a mistake, please contact{' '}
            <a href="mailto:<EMAIL>"><EMAIL></a>
          </p>
          <p>
            This could be because you are not logged in or have not requested an
            appropriate access level.
          </p>
          <p>
            You can login by clicking <Link to="/login/">here</Link>.
          </p>
        </Typography>
      </div>
    </div>
  )
}
