import React from 'react'
import { Helmet } from 'react-helmet'
import { useParams } from 'react-router-dom'
import { LoadingContainer } from '@components/layout'
import { PDFView } from '@components/pdf'
import { ValuationPvsSummaryPdf } from '@components/valuation/ValuationPvs'
import sdk from '@store/services/sdk'
import { skipArgObject } from '@util/helpers'
import ErrorPage from './ErrorPage'
import ProtectedRoute from '@components/ProtectedRoute'

export interface ValuationPageRouteParams {
  valuationId: string
}

const ValuationPvsSummaryPage = () => {
  const { valuationId } = useParams()

  const { data, error, isLoading } = sdk.useValuationsPvsSummaryRetrieveQuery(
    skipArgObject({ pk: Number(valuationId) })
  )

  if (isLoading) return <LoadingContainer loading />
  if (error) return <ErrorPage />
  if (!data) return null

  return (
    <>
      <Helmet>
        <title>Valuation</title>
      </Helmet>
      <PDFView>
        <ValuationPvsSummaryPdf valuation={data} preview />
      </PDFView>
    </>
  )
}

export default ValuationPvsSummaryPage
