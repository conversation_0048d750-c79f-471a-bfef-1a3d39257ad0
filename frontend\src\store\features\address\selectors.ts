import { createSelector } from '@reduxjs/toolkit'
import type { RootState } from '../..'
import {
  type AsyncState,
  type GeoFeatureGroup,
  defaultAsyncState,
} from '../../../types'
import type { <PERSON>Key } from './slice'

const defaultExclusionAreaState = defaultAsyncState({
  type: 'FeatureCollection',
  features: [],
} as GeoFeatureGroup)

defaultExclusionAreaState.didInvalidate = false

export const getExclusionAreas = createSelector(
  (state: RootState, valuationId: string) => {
    const exclusionAreas = state.addresses.exclusionAreas[valuationId]
    if (exclusionAreas === undefined || exclusionAreas.data === undefined) {
      return defaultExclusionAreaState
    }
    return exclusionAreas
  },
  (exclusionAreas: AsyncState<GeoFeatureGroup>) => exclusionAreas
)

export const getSelectedTitleNumber = (state: RootState) => {
  return state.addresses.ui.selectedTitleNumber
}

export const getPanelState = (state: RootState, panel: <PERSON><PERSON>ey) => {
  return state.addresses.ui.panels[panel]
}
