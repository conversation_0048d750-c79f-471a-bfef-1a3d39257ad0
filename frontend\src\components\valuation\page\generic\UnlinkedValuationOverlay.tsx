import { ArrowLeftOutlined, LinkOutlined } from '@ant-design/icons'
import { <PERSON><PERSON>, But<PERSON>, message } from 'antd'
import React from 'react'
import { useNavigate } from 'react-router-dom'
import { ButtonWidget } from '@components/generic'
import {
  useGetValuationSummaryQuery,
  useUpdateValuationMutation,
} from '@store/services/valuations'
import { TradingGroupSearch } from '../../../tradingGroup/search/TradingGroupSearch'

export const UnlinkedValuationOverlay = ({
  valuationId,
}: {
  valuationId: string
}) => {
  const navigate = useNavigate()

  const [updateValuation] = useUpdateValuationMutation()

  const { data: valuationSummary } = useGetValuationSummaryQuery(valuationId)

  const { valuation } = { ...valuationSummary }

  const onChange = async (tradingGroupId: string) => {
    if (!valuation) {
      return
    }
    try {
      await updateValuation({
        valuationId: valuation.valuationId,
        valuation: {
          tradingGroupId: tradingGroupId,
        },
      })
      navigate(`/valuations/${valuation.valuationId}`)
    } catch (_) {
      void message.error('failed to link valuation. Unknown error occurred.')
    }
  }

  const linkToProspect = async () => {
    if (!valuation) {
      return
    }
    try {
      await updateValuation({
        valuationId: valuation.valuationId,
        valuation: {
          tradingGroupId: null,
          linkedToProspect: true,
        },
      })
      navigate(`/valuations/${valuation.valuationId}`)
    } catch (_) {
      void message.error('failed to link valuation. Unknown error occurred.')
    }
  }

  const alertMessage = (
    <>
      <p>
        There is no trading group linked to this valuation, all valuations
        should have an associated trading group for which they belong.{' '}
      </p>
      <p>
        If this valuation belongs to a prospect, please click{' '}
        <b>&apos;Link Valuation to Prospect&apos;</b>.
      </p>
    </>
  )

  if (!valuation || valuation.tradingGroupId || valuation.linkedToProspect) {
    return null
  }
  return (
    <div className="agrigis-overlay">
      <div className="agrigis-overlay-widget">
        <Alert
          message="No Linked Trading Group"
          description={alertMessage}
          type="warning"
        />
        <TradingGroupSearch onChange={onChange} />
        <ButtonWidget>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => {
              navigate(-1)
            }}
          >
            Back
          </Button>
          <Button
            type="primary"
            onClick={linkToProspect}
            icon={<LinkOutlined />}
          >
            Link Valuation to Prospect
          </Button>
        </ButtonWidget>
      </div>
    </div>
  )
}
