import { Helmet } from 'react-helmet'
import { Route, Routes, useParams } from 'react-router-dom'
import ProtectedRoute from '@components/ProtectedRoute'
import ValuerPanelView from '@components/propertyFlow/externalValuation/valuerPanel/ValuerPanelView'
import { isValuerType } from '@components/propertyFlow/externalValuation/types'

const ValuerPanelPage = () => {
  const { valuerType } = useParams()

  if (!valuerType || !isValuerType(valuerType)) {
    return null
  }

  return (
    <>
      <Helmet>
        <title>Valuer Panel</title>
      </Helmet>
      <ProtectedRoute
        requiredEntitlements={
          valuerType === 'commercial'
            ? ['client:propertyflow:valuer_panel:commercial:edit']
            : ['client:propertyflow:valuer_panel:rural:edit']
        }
      >
        <Routes>
          <Route index element={<ValuerPanelView valuerType={valuerType} />} />
          <Route
            path="edit/:valuerId"
            element={<ValuerPanelView valuerType={valuerType} showForm />}
          />
          <Route
            path="new"
            element={<ValuerPanelView valuerType={valuerType} showForm />}
          />
        </Routes>
      </ProtectedRoute>
    </>
  )
}

export default ValuerPanelPage
