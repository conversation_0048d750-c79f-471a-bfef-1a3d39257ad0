import { sortDateTextTime } from './sort'

describe('sortDateTextTime', () => {
  it('should return a negative number when the first date is earlier', () => {
    const result = sortDateTextTime('01/02/2000', '02/02/2000')
    expect(result).toBeLessThan(0)
  })

  it('should return a positive number when the first date is later', () => {
    const result = sortDateTextTime('03/02/2000', '02/02/2000')
    expect(result).toBeGreaterThan(0)
  })

  it('should return the same result when the delimiter is changed', () => {
    const resultA = sortDateTextTime('01-02-2000', '02-02-2000')
    expect(resultA).toBeLessThan(0)
    const resultB = sortDateTextTime('03-02-2000', '02-02-2000')
    expect(resultB).toBeGreaterThan(0)
  })

  it('should return zero when the dates are the same', () => {
    const result = sortDateTextTime('02/02/2000', '02/02/2000')
    expect(result).toBe(0)
  })
})
