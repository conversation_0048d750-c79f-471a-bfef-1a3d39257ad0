import { Select } from 'antd'
import type { LabeledValue } from 'antd/lib/select'
import React, { useState } from 'react'

export const useSelectInput = (
  options: LabeledValue[],
  defaultValue?: string
) => {
  const [selectedValue, setSelectedValue] = useState<string | undefined>(
    defaultValue
  )
  const elem = (
    <div className="agrigis-select">
      <Select
        defaultValue={defaultValue}
        options={options}
        onChange={(e) => setSelectedValue(e?.toString())}
        style={{ width: '100%' }}
      />
    </div>
  )
  return [elem, selectedValue]
}
