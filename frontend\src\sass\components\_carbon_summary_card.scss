.carbon-summary-card {
  margin-top: 1px;
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  // border: #ccc 1px dashed;
  // border-left: 4px solid rgba(50, 50, 50, 0.05);
  // border-left: 2px solid rgba(50, 50, 50, 0.05);
}

.carbon-summary-card .header {
  // font-weight: 500;
  color: #004165;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.carbon-summary-card .value {
  // margin-top: 20px;
  // margin-bottom: 5px;
  font-weight: bold;
  width: 100%;
  font-size: 20px;
  text-align: center;
}

.carbon-summary-card {
  display: flex;
  // border-radius: 5px;
  // margin-left: 10px;
  // margin-right: 10px;
  align-items: center;
  width: 225px;
  height: 100px;
}
/*
.carbon-summary-card:hover {
    background-color: #eeeeee;
    box-shadow: 2px 2px 5px 2px #898989;
}
*/

.carbon-summary-card.summary-selected {
  background-color: #eeeeee;
  box-shadow: inset 0px 0px 5px -1px #898989;
}
