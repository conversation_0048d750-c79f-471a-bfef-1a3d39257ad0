import { View } from '@react-pdf/renderer'
import { PdfParagraph } from '@components/pdf'
import type { ValuationsPvsSummary } from '@store/services/sdk'
import KeyValueSummaryTable from './KeyValueSummaryTable'
import { marketValueToWords } from './helpers'

type Props = {
  summary: ValuationsPvsSummary['summary']
}

const MarketValueAssessmentSummaryTable = ({ summary }: Props) => {
  const marketValueText = `${summary.marketValueDollars} (plus GST, if any)
    ${marketValueToWords(Number(summary.marketValue))}
`

  const rows = [
    {
      key: 'Land without buildings (LWB)',
      value: summary.totalLwb, // TODO: Change name on serializer to reflect formatted value
    },
    {
      key: 'Added Value of Buildings',
      value: summary.improvementsMarketValueDollars,
    },
    {
      key: 'Market Value',
      value: (
        <View wrap={false}>
          <PdfParagraph style={{ fontWeight: 600 }} size="s">
            {marketValueText}
          </PdfParagraph>
          <PdfParagraph size="s" margin={false}>
            Includes the value of irrigation shares, and resource consents.
            Excludes the value of woodlots, shelterbelts, plant and equipment
            and chattels.
          </PdfParagraph>
        </View>
      ),
    },
  ]

  return <KeyValueSummaryTable rows={rows} />
}

export default MarketValueAssessmentSummaryTable
