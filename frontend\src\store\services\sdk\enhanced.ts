import {
  FetchBaseQueryError,
  FetchBaseQueryMeta,
} from '@reduxjs/toolkit/dist/query'
import { QueryReturnValue } from '@reduxjs/toolkit/dist/query/baseQueryTypes'
import { MaybePromise } from '@reduxjs/toolkit/dist/query/tsHelpers'
import {
  Benchmark,
  Facility,
  FrontlineSalesPartialUpdateApiArg,
  FrontlineSalesPartialUpdateApiResponse,
  ValuationsResourceConsentRecordsCreateApiArg,
  ValuationsResourceConsentRecordsCreateApiResponse,
  enhancedApi,
  PanelValuersPartialUpdateApiResponse,
  PanelValuersPartialUpdateApiArg,
  PanelValuersCreateApiArg,
  PanelValuersCreateApiResponse,
  CcraCcraCreateApiResponse,
  CcraCcraCreateApiArg,
  CcraCcraUpdateApiResponse,
  CcraCcraUpdateApiArg,
} from './codegen'
import { buildFileRefFormData } from '../helpers'

/*
  NOTE: For some reason hot-reload is buggy when this file changes, so you may need to refresh the frontend manually
*/

const injectedApi = enhancedApi.injectEndpoints({
  endpoints: (build) => ({
    ccraMultipartCreate: build.mutation<
      CcraCcraCreateApiResponse,
      CcraCcraCreateApiArg
    >({
      query: (queryArg) => {
        const formData = buildFileRefFormData(queryArg.ccra)

        return {
          method: 'POST',
          url: '/api/ccra/ccra/',
          body: formData,
          headers: {
            'Content-Type': undefined,
          },
        }
      },
      invalidatesTags: ['ccra'],
    }),
    ccraMultipartUpdate: build.mutation<
      CcraCcraUpdateApiResponse,
      CcraCcraUpdateApiArg
    >({
      query: (queryArg) => {
        const formData = buildFileRefFormData(queryArg.ccra)

        return {
          method: 'PUT',
          url: `/api/ccra/ccra/${queryArg.pk}/`,
          body: formData,
          headers: {
            'Content-Type': undefined,
          },
        }
      },
      invalidatesTags: ['ccra'],
    }),
    valuationsResourceConsentRecordsNestedCreate: build.mutation<
      ValuationsResourceConsentRecordsCreateApiResponse,
      ValuationsResourceConsentRecordsCreateApiArg
    >({
      query: (queryArg) => {
        const fields = queryArg.valuationsResourceConsentRecord

        const formData = new FormData()

        Object.entries(fields).forEach(([key, value]) => {
          if (value !== undefined) {
            formData.append(key, value as string | Blob)
          }
        })

        return {
          method: 'POST',
          url: `/api/valuations/${queryArg.valuationPk}/resource-consent-records/`,
          body: formData,
          headers: {
            'Content-Type': undefined,
          },
        }
      },
      invalidatesTags: ['CombinedResourceConsents', 'ResourceConsentRecords'],
    }),
    frontlineSaleNestedPartialUpdate: build.mutation<
      FrontlineSalesPartialUpdateApiResponse,
      FrontlineSalesPartialUpdateApiArg
    >({
      query: (queryArg) => {
        const fields = queryArg.patchedFrontlineSale

        const formData = new FormData()

        Object.entries(fields).forEach(([key, value]) => {
          if (value !== undefined) {
            if (key === 'attachments') {
              for (const file of value as unknown as FileList) {
                formData.append(key, file)
              }
            } else {
              const data =
                typeof value === 'string' ? value : JSON.stringify(value)
              formData.append(key, data)
            }
          }
        })

        return {
          method: 'PATCH',
          url: `/api/frontline-sales/${queryArg.pk}/`,
          body: formData,
          headers: {
            'Content-Type': undefined,
          },
        }
      },
      invalidatesTags: ['frontline-sales'],
    }),
    panelValuersNestedPartialUpdate: build.mutation<
      PanelValuersPartialUpdateApiResponse,
      PanelValuersPartialUpdateApiArg
    >({
      query: (queryArg) => {
        const formData = buildFileRefFormData(queryArg.patchedPanelValuer)

        return {
          method: 'PUT',
          url: `/api/panel_valuers/${queryArg.pk}/`,
          body: formData,
          headers: {
            'Content-Type': undefined,
          },
        }
      },
      invalidatesTags: ['panel_valuers'],
    }),
    panelValuersNestedCreate: build.mutation<
      PanelValuersCreateApiResponse,
      PanelValuersCreateApiArg
    >({
      query: (queryArg) => {
        const formData = buildFileRefFormData(queryArg.panelValuer)

        return {
          method: 'POST',
          url: `/api/panel_valuers/`,
          body: formData,
          headers: {
            'Content-Type': undefined,
          },
        }
      },
      invalidatesTags: ['panel_valuers'],
    }),
    getFacilitiesByEntity: build.query<Facility[], number[]>({
      async queryFn(ids, _queryApi, _extraOptions, fetchWithBQ) {
        const results = await Promise.all(
          ids.map(
            (id) =>
              fetchWithBQ(`api/customer/${id}/lending/`) as MaybePromise<
                QueryReturnValue<
                  Facility[],
                  FetchBaseQueryError,
                  FetchBaseQueryMeta
                >
              >
          )
        )
        const [failed] = results.filter((result) => result.error)
        if (failed?.error) return { error: failed.error }
        const data = results.flatMap((result) => result.data as Facility[])
        return { data }
      },
    }),
    customerBenchmarksByMeasureList: build.query<
      Benchmark[][],
      {
        customerPk: number
        segment: 'W' | 'R'
        denominator: string
        measures: string[]
      }
    >({
      // NOTE: This shouldn't be expanded on.
      // It's a stopgap towards combining the values on the server.
      async queryFn(
        { customerPk, denominator, measures = [] },
        _,
        __,
        fetchWithBQ
      ) {
        const results = await Promise.all(
          measures.map((measure) => {
            const params = new URLSearchParams({
              customerPk: customerPk.toString(),
              denominator,
              measure,
            }).toString()
            return fetchWithBQ(
              `api/customer/${customerPk}/kpi/benchmark/?${params}`
            ) as MaybePromise<
              QueryReturnValue<
                Benchmark[],
                FetchBaseQueryError,
                FetchBaseQueryMeta
              >
            >
          })
        )
        const [failed] = results.filter((result) => result.error)
        if (failed?.error) return { error: failed.error }
        const data = results.map((result) => result.data as Benchmark[])
        return { data }
      },
    }),
  }),
})

const sdk = injectedApi.enhanceEndpoints({
  addTagTypes: [
    'LossModelStep',
    'PerilSummary',
    'ExplorerTitles',
    'GreenProject',
    'GreenAsset',
    'GreenProjectImpactSummary',
    'GreenProjectFormOption',
    'GreenProjectSubcategory',
    'GreenProjectCategory',
    'GreenProjectLoans',
    'GreenProjectHistory',
    'GreenProjectCompliance',
    'GreenProjectFormTemplate',
    'GreenAssetCategory',
    'GreenProjectApproval',
    'Valuation',
    'ValuationSavedTitles',
    'ValuationSavedTitlesReview',
    'TradingGroupAddress',
    'GreenProjectAnnualReporting',
    'CustomerEmissions',
    'AdminSales',
  ],
  endpoints: {
    adminSalesList: {
      providesTags: ['AdminSales'],
    },
    adminSalesRetrieve: {
      providesTags: ['AdminSales'],
    },
    adminSalesDeleteCreate: {
      invalidatesTags: ['AdminSales'],
    },
    projectAnnualReportingList: {
      providesTags: ['GreenProjectAnnualReporting'],
    },
    projectAnnualReportingCreate: {
      invalidatesTags: ['GreenProjectAnnualReporting'],
    },
    projectAnnualReportingRetrieve: {
      providesTags: ['GreenProjectAnnualReporting'],
    },
    projectAnnualReportingUpdate: {
      invalidatesTags: ['GreenProjectAnnualReporting'],
    },
    projectAnnualReportingPartialUpdate: {
      invalidatesTags: ['GreenProjectAnnualReporting'],
    },
    projectAnnualReportingDestroy: {
      invalidatesTags: ['GreenProjectAnnualReporting'],
    },
    explorerTitlesList: {
      providesTags: ['ExplorerTitles'],
    },
    customerEmissionsRetrieve: { providesTags: ['CustomerEmissions'] },
    emissionBenchmarkingRetrieve: {
      providesTags: ['CustomerEmissions'],
    },
    projectApprovalsCreate: {
      invalidatesTags: ['GreenProjectApproval', 'GreenProjectCompliance'],
    },
    lossModelStepList: {
      providesTags: ['LossModelStep'],
    },
    lossModelStepPartialUpdate: {
      invalidatesTags: ['PerilSummary', 'LossModelStep'],
    },
    lossModelStepDestroy: {
      invalidatesTags: ['PerilSummary', 'LossModelStep'],
    },
    lossModelStepCreate: {
      invalidatesTags: ['PerilSummary', 'LossModelStep'],
    },
    perilSummaryRetrieve: {
      providesTags: ['PerilSummary'],
    },
    projectApprovalsList: { providesTags: ['GreenProjectApproval'] },
    projectAssetList: { providesTags: ['GreenAsset'] },
    projectAssetCreate: {
      invalidatesTags: [
        'GreenProjectFormTemplate',
        'GreenProjectCompliance',
        'GreenAsset',
        'GreenProjectImpactSummary',
        'GreenProjectHistory',
        'GreenAssetCategory',
      ],
    },
    projectAssetRetrieve: { providesTags: ['GreenAsset'] },
    projectAssetUpdate: {
      invalidatesTags: [
        'GreenProjectFormTemplate',
        'GreenProjectCompliance',
        'GreenAsset',
        'GreenProjectImpactSummary',
        'GreenProjectHistory',
        'GreenAssetCategory',
      ],
    },
    projectAssetPartialUpdate: {
      invalidatesTags: [
        'GreenProjectFormTemplate',
        'GreenProjectCompliance',
        'GreenAsset',
        'GreenProjectImpactSummary',
        'GreenProjectHistory',
        'GreenAssetCategory',
      ],
    },
    projectAssetDestroy: {
      invalidatesTags: [
        'GreenProjectFormTemplate',
        'GreenProjectCompliance',
        'GreenAsset',
        'GreenProjectImpactSummary',
        'GreenProjectHistory',
        'GreenAssetCategory',
      ],
    },
    projectHistoryList: { providesTags: ['GreenProjectHistory'] },
    projectAssetCategoryList: { providesTags: ['GreenAssetCategory'] },
    projectList: { providesTags: ['GreenProject'] },
    projectLoansList: { providesTags: ['GreenProjectLoans'] },
    projectCreate: {
      invalidatesTags: [
        'GreenProjectFormTemplate',
        'GreenProjectCompliance',
        'GreenProject',
        'GreenProjectLoans',
        'GreenProjectImpactSummary',
        'GreenProjectHistory',
        'GreenProjectFormOption',
        'GreenProjectSubcategory',
        'GreenAssetCategory',
      ],
    },
    projectRetrieve: { providesTags: ['GreenProject'] },
    projectComplianceList: {
      providesTags: ['GreenProjectCompliance'],
    },
    projectUpdate: {
      invalidatesTags: [
        'GreenProjectFormTemplate',
        'GreenProjectCompliance',
        'GreenProject',
        'GreenProjectLoans',
        'GreenProjectImpactSummary',
        'GreenProjectHistory',
        'GreenProjectFormOption',
        'GreenProjectSubcategory',
        'GreenAssetCategory',
      ],
    },
    projectPartialUpdate: {
      invalidatesTags: [
        'GreenProjectFormTemplate',
        'GreenProjectCompliance',
        'GreenProject',
        'GreenProjectLoans',
        'GreenProjectImpactSummary',
        'GreenProjectHistory',
        'GreenProjectFormOption',
        'GreenProjectSubcategory',
        'GreenAssetCategory',
      ],
    },
    projectDestroy: {
      invalidatesTags: [
        'GreenProjectFormTemplate',
        'GreenProjectCompliance',
        'GreenProject',
        'GreenProjectLoans',
        'GreenProjectImpactSummary',
        'GreenProjectHistory',
        'GreenProjectFormOption',
        'GreenProjectSubcategory',
        'GreenAssetCategory',
      ],
    },
    projectImpactSummaryRetrieve: {
      providesTags: ['GreenProjectImpactSummary'],
    },
    projectCategoryList: { providesTags: ['GreenProjectCategory'] },
    projectFormOptionList: {
      providesTags: ['GreenProjectFormOption'],
    },
    projectSubcategoryList: {
      providesTags: ['GreenProjectSubcategory'],
    },
    tradingGroupAddressesList: {
      providesTags: ['TradingGroupAddress'],
    },
    tradingGroupLinkAddressCreate: {
      invalidatesTags: ['TradingGroupAddress'],
    },
    projectFormTemplateRetrieve: {
      providesTags: ['GreenProjectFormTemplate'],
    },
    valuationsCombinedResourceConsentsList: {
      providesTags: ['CombinedResourceConsents'],
    },
    valuationsResourceConsentRecordsCreate: {
      invalidatesTags: ['CombinedResourceConsents', 'ResourceConsentRecords'],
    },
    valuationsResourceConsentRecordsUpdate: {
      invalidatesTags: ['CombinedResourceConsents', 'ResourceConsentRecords'],
    },
    valuationsResourceConsentRecordsDestroy: {
      invalidatesTags: ['CombinedResourceConsents', 'ResourceConsentRecords'],
    },
    valuationsRetrieve: {
      providesTags: ['Valuation'],
    },
    valuationsList: {
      providesTags: ['Valuation'],
    },
    valuationsPartialUpdate: {
      invalidatesTags: (_, __, { pk }) => [
        'Valuation',
        { type: 'ValuationSummary', id: pk.toString() },
      ],
    },
    valuationsTitlesList: {
      providesTags: ['ValuationSavedTitles'],
    },
    valuationsTitlesRetrieve: {
      providesTags: ['ValuationSavedTitles', 'ValuationSavedTitlesReview'],
    },
    valuationsTitlesReviewsCreate: {
      invalidatesTags: ['ValuationSavedTitlesReview'],
    },
    valuationsTitlesReviewsUpdate: {
      invalidatesTags: ['ValuationSavedTitlesReview'],
    },
    valuationsTitlesReviewsDestroy: {
      invalidatesTags: ['ValuationSavedTitlesReview'],
    },
  },
})

export default sdk
