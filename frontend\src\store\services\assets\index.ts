import { Feature, FeatureCollection, Geometry } from 'geojson'
import { BenchmarkComparableSale } from '@models/assets/BenchmarkComparableSale'
import { RootState } from '../..'
import { BuildingOutline } from '../../../models/BuildingOutline'
import {
  Assets,
  AssetsEdits,
  SerializableAssets,
  deserializeAssets,
} from '../../../models/assets/Assets'
import {
  ComparableSale,
  SerializableComparableSale,
  deserializeComparableSale,
} from '../../../models/assets/ComparableSale'
import {
  ComparableSaleAdjustment,
  ComparableSaleAssetMetricAdjustment,
  serializeComparableSaleAdjustment,
  serializeComparableSaleAssetMetricAdjustment,
} from '../../../models/assets/ComparableSaleAdjustment'
import { ImprovementAsset } from '../../../models/assets/ImprovementAsset'
import { LandAsset } from '../../../models/assets/LandAsset'
import { PlantingAsset } from '../../../models/assets/PlantingAsset'
import { ValuationDescription } from '../../../models/assets/ValuationDescription'
import {
  HighestAndBestUseType,
  HighestAndBestUseTypeMap,
} from '../../../models/assets/ValuationTypes'
import { CarbonProjectionData } from '../../../models/carbon/CarbonProjectionData'
import { SpeciesType } from '../../../models/carbon/SpeciesType'
import { assetsActions, assetsSelectors } from '../../features/assets'
import { mapActions } from '../../features/map'
import { baseApi } from '../baseApi'

const buildBulkEditPayload = (assetEdits: AssetsEdits) => {
  const apiChanges = {
    ...assetEdits,
    assets: {
      ...assetEdits.assets,
      Land: assetEdits.assets.Land.map((landAsset: Partial<LandAsset>) => {
        // TODO
        return {
          ...landAsset,
          landClassId: landAsset.landClassId ?? landAsset?.landClass?.id,
          secondaryHighestAndBestUseType:
            landAsset?.secondaryHighestAndBestUseType,
        }
      }),
      Improvement: assetEdits.assets.Improvement.map(
        (improvementAsset: Partial<ImprovementAsset>) => {
          // TODO
          return {
            ...improvementAsset,
            secondaryHighestAndBestUseType:
              improvementAsset?.secondaryHighestAndBestUseType,
            improvementType: improvementAsset?.improvementType?.id,
          }
        }
      ),
    },
  }

  return apiChanges
}

type ValuationId = string | number

// TODO: ensure each endpoint updated on backend too (should be, just check)
export const assetsApi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    getValuationTypes: build.query<HighestAndBestUseTypeMap, void>({
      query: () => ({
        url: `/api/v2/valuation_types/`,
      }),
      transformResponse: (response: HighestAndBestUseType[]) => {
        return Object.fromEntries(
          response.map((value: HighestAndBestUseType) => [value.id, value])
        )
      },
      keepUnusedDataFor: 60000,
    }),
    getAssets: build.query<Assets, ValuationId>({
      query: (valuationId) => ({
        url: `/api/assets/`,
        params: { valuationId },
      }),
      transformResponse: (response: SerializableAssets) => {
        return deserializeAssets(response)
      },
      providesTags: (result, error, valuationId) => [
        { type: 'Assets', id: valuationId?.toString() },
      ],
    }),
    getBuildingOutlines: build.query<
      FeatureCollection<Geometry, BuildingOutline>,
      ValuationId
    >({
      query: (valuationId: ValuationId) => ({
        url: `/api/building_outlines/`,
        params: {
          valuationId,
        },
      }),
      providesTags: (result, error, valuationId) => [
        { type: 'BuildingOutlines', id: valuationId?.toString() },
      ],
    }),
    getSpeciesTypes: build.query<SpeciesType[], void>({
      query: () => ({
        url: `/api/tree_species/`,
      }),
    }),
    getCarbonProjections: build.query<CarbonProjectionData, ValuationId>({
      query: (valuationId: string) => ({
        url: `/api/sequestration/`,
        params: { valuationId },
      }),
    }),
    getRemainingGeometry: build.query<
      {
        remainingFeature: Feature<Geometry, { area: number }>
        remainingFeatureOriginal: Feature<Geometry, { area: number }>
      },
      {
        valuationId: string
        landAssetId: string | number | undefined
        inputFeatures: Feature[]
        ignoreInputFeatures?: boolean
        nonTitledLand?: boolean
      }
    >({
      query: ({ valuationId, landAssetId, inputFeatures, nonTitledLand }) => ({
        url: `/api/assets/remaining_geometry/`,
        params: { valuationId },
        method: 'POST',
        body: {
          landAssetId: landAssetId,
          inputFeatures: inputFeatures,
          nonTitledLand: !!nonTitledLand,
        },
      }),
    }),
    getFullRemainingGeometry: build.query<
      {
        remainingFeature: Feature<Geometry, { area: number }>
        remainingFeatureOriginal: Feature<Geometry, { area: number }>
      },
      { valuationId: string | number; landAssetId?: string }
    >({
      query: ({ valuationId, landAssetId }) => ({
        url: `/api/assets/remaining_geometry/`,
        params: { valuationId },
        method: 'POST',
        body: {
          landAssetId,
        },
      }),
      providesTags: (_result, _error, { valuationId }) => [
        { type: 'AssetsGeometry', id: valuationId?.toString() },
      ],
    }),
    commitBulkAssetMetricsChanges: build.mutation<
      unknown,
      { valuationId: string | number; assetEdits: AssetsEdits }
    >({
      query: ({ valuationId, assetEdits }) => {
        return {
          url: `/api/assets/bulk_edit/`,
          params: { valuationId },
          method: 'POST',
          body: buildBulkEditPayload(assetEdits),
        }
      },
      invalidatesTags: (_result, _error, { valuationId }) => [
        { type: 'Assets', id: valuationId?.toString() },
        { type: 'TitleApportionment', id: valuationId?.toString() },
        'assets',
      ],
    }),
    deleteLandAssets: build.mutation<
      unknown,
      { valuationId: ValuationId; assetIds: (string | number)[] }
    >({
      query: ({ valuationId, assetIds }) => ({
        url: `/api/v2/valuations/${valuationId}/land_assets/delete/`,
        method: 'POST',
        body: assetIds,
      }),
      onCacheEntryAdded: async (_arg, { dispatch, cacheDataLoaded }) => {
        await cacheDataLoaded
        dispatch(assetsActions.commitPendingEditsComplete())
        dispatch(mapActions.resetLayerSelectionFilter())
      },
      invalidatesTags: (_result, _error, { valuationId }) => [
        { type: 'Assets', id: valuationId?.toString() },
        { type: 'TitleApportionment', id: valuationId?.toString() },
        { type: 'AssetsGeometry', id: valuationId?.toString() },
        { type: 'ValuationSummary', id: valuationId?.toString() },
        'assets',
      ],
    }),
    createLandAsset: build.mutation<
      unknown,
      { valuationId: ValuationId; assetFields: Partial<LandAsset> }
    >({
      queryFn: (
        { valuationId, assetFields },
        { getState },
        _extraOptions,
        baseQuery
      ) => {
        const landAssetFeature = assetsSelectors.editedGeoFeature(
          getState() as RootState,
          assetFields
        )
        if (!landAssetFeature) {
          return {
            data: undefined,
            error: {
              status: 500,
              statusText: 'Internal Error',
              data: 'Could not serialize LandAsset to geometry',
            },
          }
        }

        return baseQuery({
          url: `/api/v2/valuations/${valuationId}/land_assets/`,
          method: 'POST',
          body: landAssetFeature,
        })
      },
      onCacheEntryAdded: async (_arg, { dispatch, cacheDataLoaded }) => {
        await cacheDataLoaded
        dispatch(assetsActions.commitPendingEditsComplete())
        dispatch(mapActions.resetLayerSelectionFilter())
      },
      invalidatesTags: (_result, _error, { valuationId }) => [
        { type: 'Assets', id: valuationId?.toString() },
        { type: 'TitleApportionment', id: valuationId?.toString() },
        { type: 'AssetsGeometry', id: valuationId?.toString() },
        { type: 'ValuationSummary', id: valuationId?.toString() },
        'assets',
      ],
    }),
    updateLandAsset: build.mutation<
      unknown,
      {
        valuationId: ValuationId
        assetFields: Partial<LandAsset>
        id: number | string
      }
    >({
      queryFn: (
        { valuationId, assetFields, id },
        { getState },
        _extraOptions,
        baseQuery
      ) => {
        const landAssetFeature = assetsSelectors.editedGeoFeature(
          getState() as RootState,
          assetFields,
          id
        )
        if (!landAssetFeature) {
          return {
            data: undefined,
            error: {
              status: 500,
              statusText: 'Internal Error',
              data: 'Could not serialize LandAsset to geometry',
            },
          }
        }

        return baseQuery({
          url: `/api/v2/valuations/${valuationId}/land_assets/${id}/`,
          method: 'PATCH',
          body: landAssetFeature,
        })
      },
      onCacheEntryAdded: async (_arg, { dispatch, cacheDataLoaded }) => {
        await cacheDataLoaded
        dispatch(assetsActions.commitPendingEditsComplete())
        dispatch(mapActions.resetLayerSelectionFilter())
      },
      invalidatesTags: (_result, _error, { valuationId }) => [
        { type: 'Assets', id: valuationId?.toString() },
        { type: 'TitleApportionment', id: valuationId?.toString() },
        { type: 'AssetsGeometry', id: valuationId?.toString() },
        { type: 'ValuationSummary', id: valuationId?.toString() },
        'assets',
      ],
    }),
    deleteImprovementAssets: build.mutation<
      unknown,
      { valuationId: ValuationId; assetIds: (string | number)[] }
    >({
      query: ({ valuationId, assetIds }) => ({
        url: `/api/v2/valuations/${valuationId}/improvement_assets/delete/`,
        method: 'POST',
        body: assetIds,
      }),
      onCacheEntryAdded: async (_arg, { dispatch, cacheDataLoaded }) => {
        await cacheDataLoaded
        dispatch(assetsActions.commitPendingEditsComplete())
        dispatch(mapActions.resetLayerSelectionFilter())
      },
      invalidatesTags: (_result, _error, { valuationId }) => [
        { type: 'Assets', id: valuationId?.toString() },
        { type: 'TitleApportionment', id: valuationId?.toString() },
        { type: 'AssetsGeometry', id: valuationId?.toString() },
        'assets',
      ],
    }),
    createImprovementAsset: build.mutation<
      unknown,
      { valuationId: ValuationId; assetFields: Partial<ImprovementAsset> }
    >({
      queryFn: (
        { valuationId, assetFields },
        { getState },
        _extraOptions,
        baseQuery
      ) => {
        const landAssetFeature = assetsSelectors.editedGeoFeature(
          getState() as RootState,
          assetFields
        )
        if (!landAssetFeature) {
          return {
            data: undefined,
            error: {
              status: 500,
              statusText: 'Internal Error',
              data: 'Could not serialize ImprovementAsset to geometry',
            },
          }
        }

        return baseQuery({
          url: `/api/v2/valuations/${valuationId}/improvement_assets/`,
          method: 'POST',
          body: landAssetFeature,
        })
      },
      onCacheEntryAdded: async (_arg, { dispatch, cacheDataLoaded }) => {
        await cacheDataLoaded
        dispatch(assetsActions.commitPendingEditsComplete())
        dispatch(mapActions.resetLayerSelectionFilter())
      },
      invalidatesTags: (_result, _error, { valuationId }) => [
        { type: 'Assets', id: valuationId?.toString() },
        { type: 'TitleApportionment', id: valuationId?.toString() },
        { type: 'AssetsGeometry', id: valuationId?.toString() },
        'assets',
      ],
    }),
    updateImprovementAsset: build.mutation<
      unknown,
      {
        valuationId: ValuationId
        assetFields: Partial<ImprovementAsset>
        id: number | string
      }
    >({
      queryFn: (
        { valuationId, assetFields, id },
        { getState },
        _extraOptions,
        baseQuery
      ) => {
        const landAssetFeature = assetsSelectors.editedGeoFeature(
          getState() as RootState,
          assetFields,
          id
        )
        if (!landAssetFeature) {
          return {
            data: undefined,
            error: {
              status: 500,
              statusText: 'Internal Error',
              data: 'Could not serialize ImprovementAsset to geometry',
            },
          }
        }

        return baseQuery({
          url: `/api/v2/valuations/${valuationId}/improvement_assets/${id}/`,
          method: 'PATCH',
          body: landAssetFeature,
        })
      },
      onCacheEntryAdded: async (_arg, { dispatch, cacheDataLoaded }) => {
        await cacheDataLoaded
        dispatch(assetsActions.commitPendingEditsComplete())
        dispatch(mapActions.resetLayerSelectionFilter())
      },
      invalidatesTags: (_result, _error, { valuationId }) => [
        { type: 'Assets', id: valuationId?.toString() },
        { type: 'TitleApportionment', id: valuationId?.toString() },
        { type: 'AssetsGeometry', id: valuationId?.toString() },
        'assets',
      ],
    }),
    deletePlantingAssets: build.mutation<
      unknown,
      { valuationId: ValuationId; assetIds: (string | number)[] }
    >({
      query: ({ valuationId, assetIds }) => ({
        url: `/api/v2/valuations/${valuationId}/planting_assets/delete/`,
        method: 'POST',
        body: assetIds,
      }),
      onCacheEntryAdded: async (_arg, { dispatch, cacheDataLoaded }) => {
        await cacheDataLoaded
        dispatch(assetsActions.commitPendingEditsComplete())
        dispatch(mapActions.resetLayerSelectionFilter())
      },
      invalidatesTags: (_result, _error, { valuationId }) => [
        { type: 'PlantingAssets', id: valuationId?.toString() },
        { type: 'AssetsGeometry', id: valuationId?.toString() },
      ],
    }),
    createPlantingAsset: build.mutation<
      unknown,
      { valuationId: ValuationId; assetFields: Partial<PlantingAsset> }
    >({
      queryFn: (
        { valuationId, assetFields },
        { getState },
        _extraOptions,
        baseQuery
      ) => {
        const landAssetFeature = assetsSelectors.editedGeoFeature(
          getState() as RootState,
          assetFields
        )
        if (!landAssetFeature) {
          return {
            data: undefined,
            error: {
              status: 500,
              statusText: 'Internal Error',
              data: 'Could not serialize PlantingAsset to geometry',
            },
          }
        }

        return baseQuery({
          url: `/api/v2/valuations/${valuationId}/planting_assets/`,
          method: 'POST',
          body: landAssetFeature,
        })
      },
      onCacheEntryAdded: async (_arg, { dispatch, cacheDataLoaded }) => {
        await cacheDataLoaded
        dispatch(assetsActions.commitPendingEditsComplete())
        dispatch(mapActions.resetLayerSelectionFilter())
      },
      invalidatesTags: (_result, _error, { valuationId }) => [
        { type: 'PlantingAssets', id: valuationId?.toString() },
        { type: 'AssetsGeometry', id: valuationId?.toString() },
      ],
    }),
    updatePlantingAsset: build.mutation<
      unknown,
      {
        valuationId: ValuationId
        assetFields: Partial<PlantingAsset>
        id: number | string
      }
    >({
      queryFn: (
        { valuationId, assetFields, id },
        { getState },
        _extraOptions,
        baseQuery
      ) => {
        const landAssetFeature = assetsSelectors.editedGeoFeature(
          getState() as RootState,
          assetFields,
          id
        )
        if (!landAssetFeature) {
          return {
            data: undefined,
            error: {
              status: 500,
              statusText: 'Internal Error',
              data: 'Could not serialize PlantingAsset to geometry',
            },
          }
        }

        return baseQuery({
          url: `/api/v2/valuations/${valuationId}/planting_assets/${id}/`,
          method: 'PATCH',
          body: landAssetFeature,
        })
      },
      onCacheEntryAdded: async (_arg, { dispatch, cacheDataLoaded }) => {
        await cacheDataLoaded
        dispatch(assetsActions.commitPendingEditsComplete())
        dispatch(mapActions.resetLayerSelectionFilter())
      },
      invalidatesTags: (_result, _error, { valuationId }) => [
        { type: 'PlantingAssets', id: valuationId?.toString() },
        { type: 'AssetsGeometry', id: valuationId?.toString() },
      ],
    }),
    getGeneratedValuationDescriptions: build.query<
      ValuationDescription,
      { valuationId: ValuationId; assetEdits: AssetsEdits }
    >({
      query: ({ valuationId, assetEdits }) => {
        return {
          url: `/api/assets/generate_description/`,
          params: { valuationId },
          method: 'POST',
          body: buildBulkEditPayload(assetEdits),
        }
      },
    }),
    getComparableSales: build.query<ComparableSale[], ValuationId>({
      query: (valuationId) => ({
        url: `/api/v2/valuations/${valuationId}/comparable_sales/`,
      }),
      transformResponse: (response: SerializableComparableSale[]) => {
        return response.map(deserializeComparableSale)
      },
      providesTags: (_result, _error, valuationId) => [
        { type: 'ComparableSales', id: valuationId?.toString() },
      ],
    }),
    commitComparableSales: build.mutation<
      void,
      {
        valuationId: ValuationId
        sales: {
          saleId: string | number
          lsdbId?: string
          comparableHighestAndBestUseType?: string
        }[]
      }
    >({
      query: ({ valuationId, sales }) => ({
        url: `/api/v2/valuations/${valuationId}/comparable_sales/`,
        method: 'PUT',
        body: sales,
      }),
      invalidatesTags: (_result, _error, { valuationId }) => [
        { type: 'ComparableSales', id: valuationId?.toString() },
        { type: 'Assets', id: valuationId?.toString() },
      ],
    }),
    commitComparableSaleAdjustments: build.mutation<
      void,
      {
        valuationId: string | number
        comparableSaleId: number
        adjustments: ComparableSaleAdjustment[]
        assetMetricAdjustments?: ComparableSaleAssetMetricAdjustment[]
        summaryComments: string
      }
    >({
      query: ({
        valuationId,
        comparableSaleId,
        adjustments,
        assetMetricAdjustments,
        summaryComments,
      }) => ({
        url: `/api/v2/valuations/${valuationId}/comparable_sales/${comparableSaleId}/adjustments/`,
        method: 'PUT',
        body: {
          adjustments: adjustments.map(serializeComparableSaleAdjustment),
          assetMetricAdjustments: assetMetricAdjustments?.map(
            serializeComparableSaleAssetMetricAdjustment
          ),
          summaryComments,
        },
      }),
      invalidatesTags: (_result, _error, { valuationId }) => [
        { type: 'ComparableSales', id: valuationId?.toString() },
      ],
    }),
    getBenchmarkComparableSales: build.query<
      BenchmarkComparableSale[],
      string | number
    >({
      query: (valuationId) =>
        `v2/valuations/${valuationId}/benchmark_comparable_sales/`,
      providesTags: (_result, _error, valuationId) => [
        {
          type: 'BenchmarkComparableSales',
          id: valuationId?.toString(),
        },
      ],
    }),
    commitBenchmarkComparableSales: build.mutation<
      void,
      {
        valuationId: string | number
        benchmarkComparableSales: BenchmarkComparableSale[]
      }
    >({
      query: ({ valuationId, benchmarkComparableSales }) => ({
        url: `/api/v2/valuations/${valuationId}/benchmark_comparable_sales/`,
        method: 'PUT',
        body: benchmarkComparableSales,
      }),
      invalidatesTags: (_result, _error, { valuationId }) => [
        { type: 'ComparableSales', id: valuationId?.toString() },
      ],
    }),
    getPlantingAssets: build.query<
      FeatureCollection<Geometry, PlantingAsset>,
      ValuationId
    >({
      query: (valuationId) => ({
        url: `/api/v2/valuations/${valuationId}/planting_assets/`,
      }),
      providesTags: (result, error, valuationId) => [
        { type: 'PlantingAssets', id: valuationId?.toString() },
      ],
    }),
    getTitleApportionment: build.query<unknown, ValuationId>({
      query: (valuationId: string) => ({
        url: `/api/assets/title_apportionment/`,
        params: { valuationId },
      }),
      providesTags: (_result, _error, valuationId) => [
        { type: 'TitleApportionment', id: valuationId?.toString() },
      ],
    }),
  }),
})

export const {
  useGetValuationTypesQuery,
  useGetAssetsQuery,
  useGetRemainingGeometryQuery,
  useGetFullRemainingGeometryQuery,
  useLazyGetRemainingGeometryQuery,
  useCommitBulkAssetMetricsChangesMutation,
  useGetSpeciesTypesQuery,
  useGetBuildingOutlinesQuery,
  useGetCarbonProjectionsQuery,
  useGetComparableSalesQuery,
  useCommitComparableSalesMutation,
  useCommitComparableSaleAdjustmentsMutation,
  useGetGeneratedValuationDescriptionsQuery,
  useLazyGetGeneratedValuationDescriptionsQuery,
  useGetPlantingAssetsQuery,
  useGetTitleApportionmentQuery,
  useUpdateLandAssetMutation,
  useCreateLandAssetMutation,
  useDeleteLandAssetsMutation,
  useUpdateImprovementAssetMutation,
  useCreateImprovementAssetMutation,
  useDeleteImprovementAssetsMutation,
  useUpdatePlantingAssetMutation,
  useCreatePlantingAssetMutation,
  useDeletePlantingAssetsMutation,
  useGetBenchmarkComparableSalesQuery,
  useCommitBenchmarkComparableSalesMutation,
} = assetsApi
