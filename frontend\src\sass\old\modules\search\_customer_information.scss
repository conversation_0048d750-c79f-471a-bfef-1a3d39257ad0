.customer-information {
  min-width: 500px;
  padding-bottom: $full;

  .customer-information-header {
    margin-top: $quarter;

    @include col-flex;
    align-items: center;
    line-height: $half;

    .anticon {
      margin-right: 10px;
    }
  }

  .customer-information-widget {
    &:not(:last-child) {
      margin-bottom: $full;
    }

    &:first-child {
      .customer-information-header {
        margin-top: 0;
      }
    }
  }
}
