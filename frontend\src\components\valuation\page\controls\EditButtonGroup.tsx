import {
  ArrowLeftOutlined,
  BlockOutlined,
  SwapOutlined,
} from '@ant-design/icons'
import { Alert, message } from 'antd'
import React, { useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { ButtonWidget, TooltipButton } from '@components/generic'
import { useUpdateValuationMutation } from '@store/services/valuations'
import type { Valuation } from '@types'
import { PromptModal } from '../../../generic/PromptModal'
import { AddressSearch } from '../../../tradingGroup/search/AddressSearch'

export const EditButtonGroup = ({
  valuation,
  disabled,
  completedDate,
}: {
  valuation: Valuation
  completedDate: string | undefined
  disabled?: boolean
}) => {
  const {
    tradingGroupId: inputTradingGroupId,
    valuationId,
    addressId,
  } = valuation

  const tradingGroupId = inputTradingGroupId ?? 'prospect'

  const [targetId, setTargetId] = useState<string | undefined>()
  const [showTransfer, setShowTransfer] = useState(false)
  const handleCloseTransfer = () => setShowTransfer(false)
  const handleShowTransfer = () => setShowTransfer(true)

  const navigate = useNavigate()
  const [updateValuation] = useUpdateValuationMutation()

  const [addressEdit] = useMemo(() => {
    const addressEdit = `/addressEdit/${tradingGroupId}/${addressId}/${valuationId}/`
    const goBack = tradingGroupId === 'prospect' ? '/' : `/${tradingGroupId}`
    return [addressEdit, goBack]
  }, [addressId, valuationId, tradingGroupId])

  const completeTransfer = async () => {
    if (targetId) {
      try {
        await updateValuation({
          valuationId,
          valuation: {
            address: targetId,
            valuationId,
          },
        }).unwrap()
        handleCloseTransfer()
        navigate(`/valuations/${valuationId}/`)
        void message.success('Valuation successfully transferred.')
      } catch (_) {
        void message.error(
          'Failed to transfer valuation to new valuer, unknown error occurred.'
        )
      }
    }
  }

  const transferValuationMessage = useMemo(() => {
    return (
      <div className="transfer-valuation-modal-items">
        <div className="transfer-valuation-modal-item">
          <Alert
            message="Move valuation to another address"
            className="transfer-valuation-alert"
            description={
              <>
                <div className="transfer-valuation-alert-text">
                  This utility will allow you to move the valuation to a more
                  appropriate address if one exists.
                </div>
                <div className="transfer-valuation-alert-text">
                  This is aimed at helping amend legacy valuations where only
                  Land Information New Zealand addresses were available.
                </div>
              </>
            }
          />
        </div>
        <div className="transfer-valaution-modal-item">
          <AddressSearch onChange={setTargetId} />
        </div>
      </div>
    )
  }, [])

  return (
    <ButtonWidget>
      <TooltipButton
        icon={<ArrowLeftOutlined />}
        onClick={() => navigate(-1)}
        title="Back"
      />
      <TooltipButton
        type="default"
        onClick={() => navigate(addressEdit)}
        icon={<BlockOutlined />}
        disabled={completedDate === undefined || disabled}
        title="Edit Titles"
      />
      <TooltipButton
        icon={<SwapOutlined />}
        type="default"
        onClick={() => handleShowTransfer()}
        disabled={completedDate === undefined || disabled}
        title="Change Valuation Address"
      />
      <PromptModal
        show={showTransfer}
        handleClose={handleCloseTransfer}
        header="Transfer Valuation Address"
        className="transfer-valuation-modal"
        body={transferValuationMessage || disabled}
        handleConfirm={async () => {
          await completeTransfer()
        }}
      />
    </ButtonWidget>
  )
}
