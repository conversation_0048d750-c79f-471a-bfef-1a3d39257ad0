import { type PayloadAction, createSlice } from '@reduxjs/toolkit'
import type { LucasFeature } from '../../../models/LucasFeature'
import type { LucasSequestrationData } from '../../../models/LucasSequestrationData'
import type { AsyncState, GeoFeatureGroup, PanelState } from '../../../types'
import { mapActions } from '../map'

export interface AddressState {
  exclusionAreas: {
    [valuationId: string]: AsyncState<GeoFeatureGroup>
  }
  lucas: {
    [valuationId: string]: AsyncState<GeoFeatureGroup<LucasFeature>>
  }
  lucasSequestration: {
    [valuationId: string]: AsyncState<LucasSequestrationData>
  }
  ui: {
    panels: {
      distances: PanelState
      salesListings: PanelState
    }
    selectedTitleNumber: string | null
  }
}

export const initialState: AddressState = {
  exclusionAreas: {},
  lucas: {},
  lucasSequestration: {},
  ui: {
    panels: {
      distances: {
        layerVisible: false,
        panelExpanded: false,
      },
      salesListings: {
        layerVisible: false,
        panelExpanded: false,
      },
    },
    selectedTitleNumber: null,
  },
}

interface UpdateExclusionAreaPayload {
  valuationId: string
  exclusionAreas: GeoFeatureGroup
}

interface SetSelectedTitlePayload {
  titleNumber: string | null
}

export type PanelKey = keyof AddressState['ui']['panels']
interface TogglePanelPayload {
  panel: PanelKey
}

export const addressSlice = createSlice({
  name: 'address',
  initialState: initialState,
  reducers: {
    updateExclusionAreas: (
      state: AddressState,
      { payload }: PayloadAction<UpdateExclusionAreaPayload>
    ) => {
      state.exclusionAreas[payload.valuationId].data = payload.exclusionAreas
      state.exclusionAreas[payload.valuationId].didInvalidate = true
      state.exclusionAreas[payload.valuationId].lastUpdated = new Date()
    },
    setSelectedTitle: (
      state: AddressState,
      { payload }: PayloadAction<SetSelectedTitlePayload>
    ) => {
      if (state.ui.selectedTitleNumber === payload.titleNumber) {
        state.ui.selectedTitleNumber = null
      } else {
        state.ui.selectedTitleNumber = payload.titleNumber
      }
    },
    togglePanel: (
      state: AddressState,
      { payload }: PayloadAction<TogglePanelPayload>
    ) => {
      const currentState = state.ui.panels[payload.panel].panelExpanded
      state.ui.panels[payload.panel].panelExpanded = !currentState
      state.ui.panels[payload.panel].layerVisible = !currentState
    },
    togglePanelLayerVisibility: (
      state: AddressState,
      { payload }: PayloadAction<TogglePanelPayload>
    ) => {
      const currentState = state.ui.panels[payload.panel].layerVisible
      state.ui.panels[payload.panel].layerVisible = !currentState
    },
  },
  extraReducers: (builder) => {
    builder.addCase(mapActions.mapFeatureClicked, (state, action) => {
      if (action.payload.layerType === 'titles') {
        state.ui.selectedTitleNumber =
          action.payload.feature.properties.titleNumber
      }
    })
  },
})

export const addressesReducer = addressSlice.reducer
